{"runtimeTarget": {"name": ".NETFramework,Version=v4.8/win-x86", "signature": ""}, "compilationOptions": {"defines": ["TRACE", "RELEASE", "NETFRAMEWORK", "NET48", "NET20_OR_GREATER", "NET30_OR_GREATER", "NET35_OR_GREATER", "NET40_OR_GREATER", "NET45_OR_GREATER", "NET451_OR_GREATER", "NET452_OR_GREATER", "NET46_OR_GREATER", "NET461_OR_GREATER", "NET462_OR_GREATER", "NET47_OR_GREATER", "NET471_OR_GREATER", "NET472_OR_GREATER", "NET48_OR_GREATER"], "languageVersion": "latest", "platform": "AnyCPU", "allowUnsafe": true, "warningsAsErrors": false, "optimize": true, "keyFile": "", "emitEntryPoint": true, "xmlDoc": false, "debugType": "portable"}, "targets": {".NETFramework,Version=v4.8": {"AccureMD.MediaBot.Worker/1.0.0": {"dependencies": {"Microsoft.AspNetCore": "2.2.0", "Microsoft.AspNetCore.HttpsPolicy": "2.2.0", "Microsoft.AspNetCore.Mvc": "2.2.0", "Microsoft.Graph.Communications.Calls": "1.2.0.850", "Microsoft.Graph.Communications.Calls.Media": "1.2.0.850", "Microsoft.Graph.Communications.Client": "1.2.0.850", "Microsoft.Graph.Communications.Common": "1.2.0.850", "Microsoft.Graph.Communications.Core": "1.2.0.850", "Microsoft.IdentityModel.Clients.ActiveDirectory": "5.2.4", "Microsoft.IdentityModel.Protocols.OpenIdConnect": "8.0.1", "Microsoft.Psi.Audio": "**********-beta", "Microsoft.Psi.Imaging": "*********-beta", "Microsoft.Psi.Imaging.Windows": "*********-beta", "Microsoft.Skype.Bots.Media": "*********-alpha", "Microsoft.CSharp.Reference": "4.0.0.0", "mscorlib": "4.0.0.0", "PresentationCore": "4.0.0.0", "System.ComponentModel.Composition": "4.0.0.0", "System.ComponentModel.DataAnnotations": "4.0.0.0", "System.Data.DataSetExtensions": "4.0.0.0", "System.Data.OracleClient": "4.0.0.0", "System.IdentityModel": "4.0.0.0", "System.Net": "4.0.0.0", "System.Security": "4.0.0.0", "System.ServiceProcess": "4.0.0.0", "System.Transactions": "4.0.0.0", "System.Windows.Forms": "4.0.0.0", "WindowsBase": "4.0.0.0", "System.AppContext.Reference": "4.1.2.0", "System.Console.Reference": "4.0.2.0", "System.Core": "4.0.0.0", "System.Data": "4.0.0.0", "System": "4.0.0.0", "System.Drawing": "4.0.0.0", "System.IO.Compression.Reference": "4.2.0.0", "System.IO.Compression.FileSystem": "4.0.0.0", "System.IO.Reference": "4.1.2.0", "System.IO.FileSystem.Reference": "4.0.3.0", "System.IO.FileSystem.Primitives.Reference": "4.0.3.0", "System.Linq.Reference": "4.1.2.0", "System.Linq.Expressions.Reference": "4.1.2.0", "System.Net.Http.Reference": "4.2.0.0", "System.Numerics": "4.0.0.0", "System.Reflection.Reference": "4.1.2.0", "System.Runtime.Reference": "4.1.2.0", "System.Runtime.Extensions.Reference": "4.1.2.0", "System.Runtime.InteropServices.Reference": "4.1.2.0", "System.Runtime.InteropServices.RuntimeInformation.Reference": "4.0.2.0", "System.Runtime.Serialization": "4.0.0.0", "System.Security.Cryptography.Algorithms.Reference": "4.3.0.0", "System.Security.Cryptography.Encoding.Reference": "4.0.2.0", "System.Security.Cryptography.Primitives.Reference": "4.0.2.0", "System.Security.Cryptography.X509Certificates.Reference": "4.1.2.0", "System.Xml": "4.0.0.0", "System.Xml.Linq": "4.0.0.0", "System.Xml.ReaderWriter.Reference": "4.1.1.0", "Microsoft.Win32.Primitives": "0.0.0.0", "netstandard": "0.0.0.0", "System.Collections.Concurrent.Reference": "0.0.0.0", "System.Collections.Reference": "0.0.0.0", "System.Collections.NonGeneric": "0.0.0.0", "System.Collections.Specialized": "0.0.0.0", "System.ComponentModel": "0.0.0.0", "System.ComponentModel.EventBasedAsync": "0.0.0.0", "System.ComponentModel.Primitives": "0.0.0.0", "System.ComponentModel.TypeConverter": "0.0.0.0", "System.Data.Common": "0.0.0.0", "System.Diagnostics.Contracts": "0.0.0.0", "System.Diagnostics.Debug.Reference": "0.0.0.0", "System.Diagnostics.FileVersionInfo.Reference": "0.0.0.0", "System.Diagnostics.Process": "0.0.0.0", "System.Diagnostics.StackTrace.Reference": "0.0.0.0", "System.Diagnostics.TextWriterTraceListener": "0.0.0.0", "System.Diagnostics.Tools.Reference": "0.0.0.0", "System.Diagnostics.TraceSource": "0.0.0.0", "System.Drawing.Primitives": "0.0.0.0", "System.Dynamic.Runtime.Reference": "0.0.0.0", "System.Globalization.Calendars": "0.0.0.0", "System.Globalization.Reference": "0.0.0.0", "System.Globalization.Extensions": "0.0.0.0", "System.IO.Compression.ZipFile": "0.0.0.0", "System.IO.FileSystem.DriveInfo": "0.0.0.0", "System.IO.FileSystem.Watcher": "0.0.0.0", "System.IO.IsolatedStorage": "0.0.0.0", "System.IO.MemoryMappedFiles": "0.0.0.0", "System.IO.Pipes": "0.0.0.0", "System.IO.UnmanagedMemoryStream": "0.0.0.0", "System.Linq.Parallel": "0.0.0.0", "System.Linq.Queryable": "0.0.0.0", "System.Net.Http.Rtc": "0.0.0.0", "System.Net.NameResolution": "0.0.0.0", "System.Net.NetworkInformation": "0.0.0.0", "System.Net.Ping": "0.0.0.0", "System.Net.Primitives": "0.0.0.0", "System.Net.Requests": "0.0.0.0", "System.Net.Security": "0.0.0.0", "System.Net.Sockets": "0.0.0.0", "System.Net.WebHeaderCollection": "0.0.0.0", "System.Net.WebSockets.Client": "0.0.0.0", "System.Net.WebSockets": "0.0.0.0", "System.ObjectModel": "0.0.0.0", "System.Reflection.Emit": "0.0.0.0", "System.Reflection.Emit.ILGeneration.Reference": "0.0.0.0", "System.Reflection.Emit.Lightweight.Reference": "0.0.0.0", "System.Reflection.Extensions": "0.0.0.0", "System.Reflection.Primitives": "0.0.0.0", "System.Resources.Reader": "0.0.0.0", "System.Resources.ResourceManager.Reference": "0.0.0.0", "System.Resources.Writer": "0.0.0.0", "System.Runtime.CompilerServices.VisualC": "0.0.0.0", "System.Runtime.Handles": "0.0.0.0", "System.Runtime.InteropServices.WindowsRuntime": "0.0.0.0", "System.Runtime.Numerics.Reference": "0.0.0.0", "System.Runtime.Serialization.Formatters": "0.0.0.0", "System.Runtime.Serialization.Json": "0.0.0.0", "System.Runtime.Serialization.Primitives": "0.0.0.0", "System.Runtime.Serialization.Xml": "0.0.0.0", "System.Security.Claims": "0.0.0.0", "System.Security.Cryptography.Csp": "0.0.0.0", "System.Security.Principal": "0.0.0.0", "System.Security.SecureString": "0.0.0.0", "System.ServiceModel.Duplex": "0.0.0.0", "System.ServiceModel.Http": "0.0.0.0", "System.ServiceModel.NetTcp": "0.0.0.0", "System.ServiceModel.Primitives": "0.0.0.0", "System.ServiceModel.Security": "0.0.0.0", "System.Text.Encoding.Reference": "0.0.0.0", "System.Text.Encoding.Extensions.Reference": "0.0.0.0", "System.Text.RegularExpressions": "0.0.0.0", "System.Threading.Reference": "0.0.0.0", "System.Threading.Overlapped": "0.0.0.0", "System.Threading.Tasks.Reference": "0.0.0.0", "System.Threading.Tasks.Parallel.Reference": "0.0.0.0", "System.Threading.Thread.Reference": "0.0.0.0", "System.Threading.ThreadPool": "0.0.0.0", "System.Threading.Timer": "0.0.0.0", "System.Xml.XDocument.Reference": "0.0.0.0", "System.Xml.XmlDocument.Reference": "0.0.0.0", "System.Xml.XmlSerializer": "0.0.0.0", "System.Xml.XPath.Reference": "0.0.0.0", "System.Xml.XPath.XDocument.Reference": "0.0.0.0"}, "compile": {"AccureMD.MediaBot.Worker.exe": {}}}, "Microsoft.CSharp.Reference/4.0.0.0": {"compile": {".NETFramework/v4.8/Microsoft.CSharp.dll": {}}}, "mscorlib/4.0.0.0": {"compile": {".NETFramework/v4.8/mscorlib.dll": {}}}, "PresentationCore/4.0.0.0": {"compile": {".NETFramework/v4.8/PresentationCore.dll": {}}}, "System.ComponentModel.Composition/4.0.0.0": {"compile": {".NETFramework/v4.8/System.ComponentModel.Composition.dll": {}}}, "System.ComponentModel.DataAnnotations/4.0.0.0": {"compile": {".NETFramework/v4.8/System.ComponentModel.DataAnnotations.dll": {}}}, "System.Data.DataSetExtensions/4.0.0.0": {"compile": {".NETFramework/v4.8/System.Data.DataSetExtensions.dll": {}}}, "System.Data.OracleClient/4.0.0.0": {"compile": {".NETFramework/v4.8/System.Data.OracleClient.dll": {}}}, "System.IdentityModel/4.0.0.0": {"compile": {".NETFramework/v4.8/System.IdentityModel.dll": {}}}, "System.Net/4.0.0.0": {"compile": {".NETFramework/v4.8/System.Net.dll": {}}}, "System.Security/4.0.0.0": {"compile": {".NETFramework/v4.8/System.Security.dll": {}}}, "System.ServiceProcess/4.0.0.0": {"compile": {".NETFramework/v4.8/System.ServiceProcess.dll": {}}}, "System.Transactions/4.0.0.0": {"compile": {".NETFramework/v4.8/System.Transactions.dll": {}}}, "System.Windows.Forms/4.0.0.0": {"compile": {".NETFramework/v4.8/System.Windows.Forms.dll": {}}}, "WindowsBase/4.0.0.0": {"compile": {".NETFramework/v4.8/WindowsBase.dll": {}}}, "System.AppContext.Reference/4.1.2.0": {"compile": {".NETFramework/v4.8/Facades/System.AppContext.dll": {}}}, "System.Console.Reference/4.0.2.0": {"compile": {".NETFramework/v4.8/Facades/System.Console.dll": {}}}, "System.Core/4.0.0.0": {"compile": {".NETFramework/v4.8/System.Core.dll": {}}}, "System.Data/4.0.0.0": {"compile": {".NETFramework/v4.8/System.Data.dll": {}}}, "System/4.0.0.0": {"compile": {".NETFramework/v4.8/System.dll": {}}}, "System.Drawing/4.0.0.0": {"compile": {".NETFramework/v4.8/System.Drawing.dll": {}}}, "System.IO.Compression.Reference/4.2.0.0": {"compile": {".NETFramework/v4.8/System.IO.Compression.dll": {}}}, "System.IO.Compression.FileSystem/4.0.0.0": {"compile": {".NETFramework/v4.8/System.IO.Compression.FileSystem.dll": {}}}, "System.IO.Reference/4.1.2.0": {"compile": {".NETFramework/v4.8/Facades/System.IO.dll": {}}}, "System.IO.FileSystem.Reference/4.0.3.0": {"compile": {".NETFramework/v4.8/Facades/System.IO.FileSystem.dll": {}}}, "System.IO.FileSystem.Primitives.Reference/4.0.3.0": {"compile": {".NETFramework/v4.8/Facades/System.IO.FileSystem.Primitives.dll": {}}}, "System.Linq.Reference/4.1.2.0": {"compile": {".NETFramework/v4.8/Facades/System.Linq.dll": {}}}, "System.Linq.Expressions.Reference/4.1.2.0": {"compile": {".NETFramework/v4.8/Facades/System.Linq.Expressions.dll": {}}}, "System.Net.Http.Reference/4.2.0.0": {"compile": {".NETFramework/v4.8/System.Net.Http.dll": {}}}, "System.Numerics/4.0.0.0": {"compile": {".NETFramework/v4.8/System.Numerics.dll": {}}}, "System.Reflection.Reference/4.1.2.0": {"compile": {".NETFramework/v4.8/Facades/System.Reflection.dll": {}}}, "System.Runtime.Reference/4.1.2.0": {"compile": {".NETFramework/v4.8/Facades/System.Runtime.dll": {}}}, "System.Runtime.Extensions.Reference/4.1.2.0": {"compile": {".NETFramework/v4.8/Facades/System.Runtime.Extensions.dll": {}}}, "System.Runtime.InteropServices.Reference/4.1.2.0": {"compile": {".NETFramework/v4.8/Facades/System.Runtime.InteropServices.dll": {}}}, "System.Runtime.InteropServices.RuntimeInformation.Reference/4.0.2.0": {"compile": {".NETFramework/v4.8/Facades/System.Runtime.InteropServices.RuntimeInformation.dll": {}}}, "System.Runtime.Serialization/4.0.0.0": {"compile": {".NETFramework/v4.8/System.Runtime.Serialization.dll": {}}}, "System.Security.Cryptography.Algorithms.Reference/4.3.0.0": {"compile": {".NETFramework/v4.8/Facades/System.Security.Cryptography.Algorithms.dll": {}}}, "System.Security.Cryptography.Encoding.Reference/4.0.2.0": {"compile": {".NETFramework/v4.8/Facades/System.Security.Cryptography.Encoding.dll": {}}}, "System.Security.Cryptography.Primitives.Reference/4.0.2.0": {"compile": {".NETFramework/v4.8/Facades/System.Security.Cryptography.Primitives.dll": {}}}, "System.Security.Cryptography.X509Certificates.Reference/4.1.2.0": {"compile": {".NETFramework/v4.8/Facades/System.Security.Cryptography.X509Certificates.dll": {}}}, "System.Xml/4.0.0.0": {"compile": {".NETFramework/v4.8/System.Xml.dll": {}}}, "System.Xml.Linq/4.0.0.0": {"compile": {".NETFramework/v4.8/System.Xml.Linq.dll": {}}}, "System.Xml.ReaderWriter.Reference/4.1.1.0": {"compile": {".NETFramework/v4.8/Facades/System.Xml.ReaderWriter.dll": {}}}, "Microsoft.Win32.Primitives/0.0.0.0": {"compile": {".NETFramework/v4.8/Facades/Microsoft.Win32.Primitives.dll": {}}}, "netstandard/0.0.0.0": {"compile": {".NETFramework/v4.8/Facades/netstandard.dll": {}}}, "System.Collections.Concurrent.Reference/0.0.0.0": {"compile": {".NETFramework/v4.8/Facades/System.Collections.Concurrent.dll": {}}}, "System.Collections.Reference/0.0.0.0": {"compile": {".NETFramework/v4.8/Facades/System.Collections.dll": {}}}, "System.Collections.NonGeneric/0.0.0.0": {"compile": {".NETFramework/v4.8/Facades/System.Collections.NonGeneric.dll": {}}}, "System.Collections.Specialized/0.0.0.0": {"compile": {".NETFramework/v4.8/Facades/System.Collections.Specialized.dll": {}}}, "System.ComponentModel/0.0.0.0": {"compile": {".NETFramework/v4.8/Facades/System.ComponentModel.dll": {}}}, "System.ComponentModel.EventBasedAsync/0.0.0.0": {"compile": {".NETFramework/v4.8/Facades/System.ComponentModel.EventBasedAsync.dll": {}}}, "System.ComponentModel.Primitives/0.0.0.0": {"compile": {".NETFramework/v4.8/Facades/System.ComponentModel.Primitives.dll": {}}}, "System.ComponentModel.TypeConverter/0.0.0.0": {"compile": {".NETFramework/v4.8/Facades/System.ComponentModel.TypeConverter.dll": {}}}, "System.Data.Common/0.0.0.0": {"compile": {".NETFramework/v4.8/Facades/System.Data.Common.dll": {}}}, "System.Diagnostics.Contracts/0.0.0.0": {"compile": {".NETFramework/v4.8/Facades/System.Diagnostics.Contracts.dll": {}}}, "System.Diagnostics.Debug.Reference/0.0.0.0": {"compile": {".NETFramework/v4.8/Facades/System.Diagnostics.Debug.dll": {}}}, "System.Diagnostics.FileVersionInfo.Reference/0.0.0.0": {"compile": {".NETFramework/v4.8/Facades/System.Diagnostics.FileVersionInfo.dll": {}}}, "System.Diagnostics.Process/0.0.0.0": {"compile": {".NETFramework/v4.8/Facades/System.Diagnostics.Process.dll": {}}}, "System.Diagnostics.StackTrace.Reference/0.0.0.0": {"compile": {".NETFramework/v4.8/Facades/System.Diagnostics.StackTrace.dll": {}}}, "System.Diagnostics.TextWriterTraceListener/0.0.0.0": {"compile": {".NETFramework/v4.8/Facades/System.Diagnostics.TextWriterTraceListener.dll": {}}}, "System.Diagnostics.Tools.Reference/0.0.0.0": {"compile": {".NETFramework/v4.8/Facades/System.Diagnostics.Tools.dll": {}}}, "System.Diagnostics.TraceSource/0.0.0.0": {"compile": {".NETFramework/v4.8/Facades/System.Diagnostics.TraceSource.dll": {}}}, "System.Drawing.Primitives/0.0.0.0": {"compile": {".NETFramework/v4.8/Facades/System.Drawing.Primitives.dll": {}}}, "System.Dynamic.Runtime.Reference/0.0.0.0": {"compile": {".NETFramework/v4.8/Facades/System.Dynamic.Runtime.dll": {}}}, "System.Globalization.Calendars/0.0.0.0": {"compile": {".NETFramework/v4.8/Facades/System.Globalization.Calendars.dll": {}}}, "System.Globalization.Reference/0.0.0.0": {"compile": {".NETFramework/v4.8/Facades/System.Globalization.dll": {}}}, "System.Globalization.Extensions/0.0.0.0": {"compile": {".NETFramework/v4.8/Facades/System.Globalization.Extensions.dll": {}}}, "System.IO.Compression.ZipFile/0.0.0.0": {"compile": {".NETFramework/v4.8/Facades/System.IO.Compression.ZipFile.dll": {}}}, "System.IO.FileSystem.DriveInfo/0.0.0.0": {"compile": {".NETFramework/v4.8/Facades/System.IO.FileSystem.DriveInfo.dll": {}}}, "System.IO.FileSystem.Watcher/0.0.0.0": {"compile": {".NETFramework/v4.8/Facades/System.IO.FileSystem.Watcher.dll": {}}}, "System.IO.IsolatedStorage/0.0.0.0": {"compile": {".NETFramework/v4.8/Facades/System.IO.IsolatedStorage.dll": {}}}, "System.IO.MemoryMappedFiles/0.0.0.0": {"compile": {".NETFramework/v4.8/Facades/System.IO.MemoryMappedFiles.dll": {}}}, "System.IO.Pipes/0.0.0.0": {"compile": {".NETFramework/v4.8/Facades/System.IO.Pipes.dll": {}}}, "System.IO.UnmanagedMemoryStream/0.0.0.0": {"compile": {".NETFramework/v4.8/Facades/System.IO.UnmanagedMemoryStream.dll": {}}}, "System.Linq.Parallel/0.0.0.0": {"compile": {".NETFramework/v4.8/Facades/System.Linq.Parallel.dll": {}}}, "System.Linq.Queryable/0.0.0.0": {"compile": {".NETFramework/v4.8/Facades/System.Linq.Queryable.dll": {}}}, "System.Net.Http.Rtc/0.0.0.0": {"compile": {".NETFramework/v4.8/Facades/System.Net.Http.Rtc.dll": {}}}, "System.Net.NameResolution/0.0.0.0": {"compile": {".NETFramework/v4.8/Facades/System.Net.NameResolution.dll": {}}}, "System.Net.NetworkInformation/0.0.0.0": {"compile": {".NETFramework/v4.8/Facades/System.Net.NetworkInformation.dll": {}}}, "System.Net.Ping/0.0.0.0": {"compile": {".NETFramework/v4.8/Facades/System.Net.Ping.dll": {}}}, "System.Net.Primitives/0.0.0.0": {"compile": {".NETFramework/v4.8/Facades/System.Net.Primitives.dll": {}}}, "System.Net.Requests/0.0.0.0": {"compile": {".NETFramework/v4.8/Facades/System.Net.Requests.dll": {}}}, "System.Net.Security/0.0.0.0": {"compile": {".NETFramework/v4.8/Facades/System.Net.Security.dll": {}}}, "System.Net.Sockets/0.0.0.0": {"compile": {".NETFramework/v4.8/Facades/System.Net.Sockets.dll": {}}}, "System.Net.WebHeaderCollection/0.0.0.0": {"compile": {".NETFramework/v4.8/Facades/System.Net.WebHeaderCollection.dll": {}}}, "System.Net.WebSockets.Client/0.0.0.0": {"compile": {".NETFramework/v4.8/Facades/System.Net.WebSockets.Client.dll": {}}}, "System.Net.WebSockets/0.0.0.0": {"compile": {".NETFramework/v4.8/Facades/System.Net.WebSockets.dll": {}}}, "System.ObjectModel/0.0.0.0": {"compile": {".NETFramework/v4.8/Facades/System.ObjectModel.dll": {}}}, "System.Reflection.Emit/0.0.0.0": {"compile": {".NETFramework/v4.8/Facades/System.Reflection.Emit.dll": {}}}, "System.Reflection.Emit.ILGeneration.Reference/0.0.0.0": {"compile": {".NETFramework/v4.8/Facades/System.Reflection.Emit.ILGeneration.dll": {}}}, "System.Reflection.Emit.Lightweight.Reference/0.0.0.0": {"compile": {".NETFramework/v4.8/Facades/System.Reflection.Emit.Lightweight.dll": {}}}, "System.Reflection.Extensions/0.0.0.0": {"compile": {".NETFramework/v4.8/Facades/System.Reflection.Extensions.dll": {}}}, "System.Reflection.Primitives/0.0.0.0": {"compile": {".NETFramework/v4.8/Facades/System.Reflection.Primitives.dll": {}}}, "System.Resources.Reader/0.0.0.0": {"compile": {".NETFramework/v4.8/Facades/System.Resources.Reader.dll": {}}}, "System.Resources.ResourceManager.Reference/0.0.0.0": {"compile": {".NETFramework/v4.8/Facades/System.Resources.ResourceManager.dll": {}}}, "System.Resources.Writer/0.0.0.0": {"compile": {".NETFramework/v4.8/Facades/System.Resources.Writer.dll": {}}}, "System.Runtime.CompilerServices.VisualC/0.0.0.0": {"compile": {".NETFramework/v4.8/Facades/System.Runtime.CompilerServices.VisualC.dll": {}}}, "System.Runtime.Handles/0.0.0.0": {"compile": {".NETFramework/v4.8/Facades/System.Runtime.Handles.dll": {}}}, "System.Runtime.InteropServices.WindowsRuntime/0.0.0.0": {"compile": {".NETFramework/v4.8/Facades/System.Runtime.InteropServices.WindowsRuntime.dll": {}}}, "System.Runtime.Numerics.Reference/0.0.0.0": {"compile": {".NETFramework/v4.8/Facades/System.Runtime.Numerics.dll": {}}}, "System.Runtime.Serialization.Formatters/0.0.0.0": {"compile": {".NETFramework/v4.8/Facades/System.Runtime.Serialization.Formatters.dll": {}}}, "System.Runtime.Serialization.Json/0.0.0.0": {"compile": {".NETFramework/v4.8/Facades/System.Runtime.Serialization.Json.dll": {}}}, "System.Runtime.Serialization.Primitives/0.0.0.0": {"compile": {".NETFramework/v4.8/Facades/System.Runtime.Serialization.Primitives.dll": {}}}, "System.Runtime.Serialization.Xml/0.0.0.0": {"compile": {".NETFramework/v4.8/Facades/System.Runtime.Serialization.Xml.dll": {}}}, "System.Security.Claims/0.0.0.0": {"compile": {".NETFramework/v4.8/Facades/System.Security.Claims.dll": {}}}, "System.Security.Cryptography.Csp/0.0.0.0": {"compile": {".NETFramework/v4.8/Facades/System.Security.Cryptography.Csp.dll": {}}}, "System.Security.Principal/0.0.0.0": {"compile": {".NETFramework/v4.8/Facades/System.Security.Principal.dll": {}}}, "System.Security.SecureString/0.0.0.0": {"compile": {".NETFramework/v4.8/Facades/System.Security.SecureString.dll": {}}}, "System.ServiceModel.Duplex/0.0.0.0": {"compile": {".NETFramework/v4.8/Facades/System.ServiceModel.Duplex.dll": {}}}, "System.ServiceModel.Http/0.0.0.0": {"compile": {".NETFramework/v4.8/Facades/System.ServiceModel.Http.dll": {}}}, "System.ServiceModel.NetTcp/0.0.0.0": {"compile": {".NETFramework/v4.8/Facades/System.ServiceModel.NetTcp.dll": {}}}, "System.ServiceModel.Primitives/0.0.0.0": {"compile": {".NETFramework/v4.8/Facades/System.ServiceModel.Primitives.dll": {}}}, "System.ServiceModel.Security/0.0.0.0": {"compile": {".NETFramework/v4.8/Facades/System.ServiceModel.Security.dll": {}}}, "System.Text.Encoding.Reference/0.0.0.0": {"compile": {".NETFramework/v4.8/Facades/System.Text.Encoding.dll": {}}}, "System.Text.Encoding.Extensions.Reference/0.0.0.0": {"compile": {".NETFramework/v4.8/Facades/System.Text.Encoding.Extensions.dll": {}}}, "System.Text.RegularExpressions/0.0.0.0": {"compile": {".NETFramework/v4.8/Facades/System.Text.RegularExpressions.dll": {}}}, "System.Threading.Reference/0.0.0.0": {"compile": {".NETFramework/v4.8/Facades/System.Threading.dll": {}}}, "System.Threading.Overlapped/0.0.0.0": {"compile": {".NETFramework/v4.8/Facades/System.Threading.Overlapped.dll": {}}}, "System.Threading.Tasks.Reference/0.0.0.0": {"compile": {".NETFramework/v4.8/Facades/System.Threading.Tasks.dll": {}}}, "System.Threading.Tasks.Parallel.Reference/0.0.0.0": {"compile": {".NETFramework/v4.8/Facades/System.Threading.Tasks.Parallel.dll": {}}}, "System.Threading.Thread.Reference/0.0.0.0": {"compile": {".NETFramework/v4.8/Facades/System.Threading.Thread.dll": {}}}, "System.Threading.ThreadPool/0.0.0.0": {"compile": {".NETFramework/v4.8/Facades/System.Threading.ThreadPool.dll": {}}}, "System.Threading.Timer/0.0.0.0": {"compile": {".NETFramework/v4.8/Facades/System.Threading.Timer.dll": {}}}, "System.Xml.XDocument.Reference/0.0.0.0": {"compile": {".NETFramework/v4.8/Facades/System.Xml.XDocument.dll": {}}}, "System.Xml.XmlDocument.Reference/0.0.0.0": {"compile": {".NETFramework/v4.8/Facades/System.Xml.XmlDocument.dll": {}}}, "System.Xml.XmlSerializer/0.0.0.0": {"compile": {".NETFramework/v4.8/Facades/System.Xml.XmlSerializer.dll": {}}}, "System.Xml.XPath.Reference/0.0.0.0": {"compile": {".NETFramework/v4.8/Facades/System.Xml.XPath.dll": {}}}, "System.Xml.XPath.XDocument.Reference/0.0.0.0": {"compile": {".NETFramework/v4.8/Facades/System.Xml.XPath.XDocument.dll": {}}}, "Microsoft.AspNetCore/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Diagnostics": "2.2.0", "Microsoft.AspNetCore.HostFiltering": "2.2.0", "Microsoft.AspNetCore.Hosting": "2.2.0", "Microsoft.AspNetCore.Routing": "2.2.0", "Microsoft.AspNetCore.Server.IIS": "2.2.0", "Microsoft.AspNetCore.Server.IISIntegration": "2.2.0", "Microsoft.AspNetCore.Server.Kestrel": "2.2.0", "Microsoft.AspNetCore.Server.Kestrel.Https": "2.2.0", "Microsoft.Extensions.Configuration.CommandLine": "2.2.0", "Microsoft.Extensions.Configuration.EnvironmentVariables": "2.2.0", "Microsoft.Extensions.Configuration.FileExtensions": "2.2.0", "Microsoft.Extensions.Configuration.Json": "2.2.0", "Microsoft.Extensions.Configuration.UserSecrets": "2.2.0", "Microsoft.Extensions.Logging": "2.2.0", "Microsoft.Extensions.Logging.Configuration": "2.2.0", "Microsoft.Extensions.Logging.Console": "2.2.0", "Microsoft.Extensions.Logging.Debug": "2.2.0", "Microsoft.Extensions.Logging.EventSource": "2.2.0"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.dll": {}}}, "Microsoft.AspNetCore.Antiforgery/2.2.0": {"dependencies": {"Microsoft.AspNetCore.DataProtection": "2.2.0", "Microsoft.AspNetCore.Http.Abstractions": "2.2.0", "Microsoft.AspNetCore.Http.Extensions": "2.2.0", "Microsoft.AspNetCore.WebUtilities": "2.2.0", "Microsoft.Extensions.ObjectPool": "2.2.0"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Antiforgery.dll": {}}}, "Microsoft.AspNetCore.Authentication.Abstractions/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Http.Abstractions": "2.2.0", "Microsoft.Extensions.Logging.Abstractions": "2.2.0", "Microsoft.Extensions.Options": "2.2.0"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Authentication.Abstractions.dll": {}}}, "Microsoft.AspNetCore.Authentication.Core/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Authentication.Abstractions": "2.2.0", "Microsoft.AspNetCore.Http": "2.2.0", "Microsoft.AspNetCore.Http.Extensions": "2.2.0"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Authentication.Core.dll": {}}}, "Microsoft.AspNetCore.Authorization/2.2.0": {"dependencies": {"Microsoft.Extensions.Logging.Abstractions": "2.2.0", "Microsoft.Extensions.Options": "2.2.0"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Authorization.dll": {}}}, "Microsoft.AspNetCore.Authorization.Policy/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Authentication.Abstractions": "2.2.0", "Microsoft.AspNetCore.Authorization": "2.2.0"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Authorization.Policy.dll": {}}}, "Microsoft.AspNetCore.Connections.Abstractions/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Http.Features": "2.2.0", "System.IO.Pipelines": "4.5.2"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Connections.Abstractions.dll": {}}}, "Microsoft.AspNetCore.Cors/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Http.Extensions": "2.2.0", "Microsoft.Extensions.Configuration.Abstractions": "2.2.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "2.2.0", "Microsoft.Extensions.Logging.Abstractions": "2.2.0", "Microsoft.Extensions.Options": "2.2.0"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Cors.dll": {}}}, "Microsoft.AspNetCore.Cryptography.Internal/2.2.0": {"compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Cryptography.Internal.dll": {}}}, "Microsoft.AspNetCore.DataProtection/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Cryptography.Internal": "2.2.0", "Microsoft.AspNetCore.DataProtection.Abstractions": "2.2.0", "Microsoft.AspNetCore.Hosting.Abstractions": "2.2.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "2.2.0", "Microsoft.Extensions.Logging.Abstractions": "2.2.0", "Microsoft.Extensions.Options": "2.2.0", "Microsoft.Win32.Registry": "4.5.0", "System.Security.Cryptography.Xml": "4.5.0", "System.Security.Principal.Windows": "4.5.0"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.DataProtection.dll": {}}}, "Microsoft.AspNetCore.DataProtection.Abstractions/2.2.0": {"compile": {"lib/netstandard2.0/Microsoft.AspNetCore.DataProtection.Abstractions.dll": {}}}, "Microsoft.AspNetCore.Diagnostics/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Diagnostics.Abstractions": "2.2.0", "Microsoft.AspNetCore.Hosting.Abstractions": "2.2.0", "Microsoft.AspNetCore.Http.Extensions": "2.2.0", "Microsoft.AspNetCore.WebUtilities": "2.2.0", "Microsoft.Extensions.FileProviders.Physical": "2.2.0", "Microsoft.Extensions.Logging.Abstractions": "2.2.0", "Microsoft.Extensions.Options": "2.2.0", "System.Diagnostics.DiagnosticSource": "4.6.0", "System.Reflection.Metadata": "1.6.0"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Diagnostics.dll": {}}}, "Microsoft.AspNetCore.Diagnostics.Abstractions/2.2.0": {"compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Diagnostics.Abstractions.dll": {}}}, "Microsoft.AspNetCore.HostFiltering/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Hosting.Abstractions": "2.2.0", "Microsoft.AspNetCore.Http": "2.2.0", "Microsoft.AspNetCore.Http.Extensions": "2.2.0", "Microsoft.Extensions.Options": "2.2.0"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.HostFiltering.dll": {}}}, "Microsoft.AspNetCore.Hosting/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Hosting.Abstractions": "2.2.0", "Microsoft.AspNetCore.Http": "2.2.0", "Microsoft.AspNetCore.Http.Extensions": "2.2.0", "Microsoft.Extensions.Configuration": "2.2.0", "Microsoft.Extensions.Configuration.EnvironmentVariables": "2.2.0", "Microsoft.Extensions.Configuration.FileExtensions": "2.2.0", "Microsoft.Extensions.DependencyInjection": "2.2.0", "Microsoft.Extensions.FileProviders.Physical": "2.2.0", "Microsoft.Extensions.Hosting.Abstractions": "2.2.0", "Microsoft.Extensions.Logging": "2.2.0", "Microsoft.Extensions.Options": "2.2.0", "System.Diagnostics.DiagnosticSource": "4.6.0", "System.Reflection.Metadata": "1.6.0"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Hosting.dll": {}}}, "Microsoft.AspNetCore.Hosting.Abstractions/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Hosting.Server.Abstractions": "2.2.0", "Microsoft.AspNetCore.Http.Abstractions": "2.2.0", "Microsoft.Extensions.Hosting.Abstractions": "2.2.0"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Hosting.Abstractions.dll": {}}}, "Microsoft.AspNetCore.Hosting.Server.Abstractions/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Http.Features": "2.2.0", "Microsoft.Extensions.Configuration.Abstractions": "2.2.0"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Hosting.Server.Abstractions.dll": {}}}, "Microsoft.AspNetCore.Html.Abstractions/2.2.0": {"dependencies": {"System.Text.Encodings.Web": "8.0.0"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Html.Abstractions.dll": {}}}, "Microsoft.AspNetCore.Http/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Http.Abstractions": "2.2.0", "Microsoft.AspNetCore.WebUtilities": "2.2.0", "Microsoft.Extensions.ObjectPool": "2.2.0", "Microsoft.Extensions.Options": "2.2.0", "Microsoft.Net.Http.Headers": "2.2.0"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Http.dll": {}}}, "Microsoft.AspNetCore.Http.Abstractions/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Http.Features": "2.2.0", "System.Text.Encodings.Web": "8.0.0"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Http.Abstractions.dll": {}}}, "Microsoft.AspNetCore.Http.Extensions/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Http.Abstractions": "2.2.0", "Microsoft.Extensions.FileProviders.Abstractions": "2.2.0", "Microsoft.Net.Http.Headers": "2.2.0", "System.Buffers": "4.5.1"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Http.Extensions.dll": {}}}, "Microsoft.AspNetCore.Http.Features/2.2.0": {"dependencies": {"Microsoft.Extensions.Primitives": "2.2.0"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Http.Features.dll": {}}}, "Microsoft.AspNetCore.HttpOverrides/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Http.Extensions": "2.2.0", "Microsoft.Extensions.Logging.Abstractions": "2.2.0", "Microsoft.Extensions.Options": "2.2.0"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.HttpOverrides.dll": {}}}, "Microsoft.AspNetCore.HttpsPolicy/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Hosting.Abstractions": "2.2.0", "Microsoft.AspNetCore.Http": "2.2.0", "Microsoft.AspNetCore.Http.Extensions": "2.2.0", "Microsoft.Extensions.Configuration.Binder": "2.2.0", "Microsoft.Extensions.Options": "2.2.0"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.HttpsPolicy.dll": {}}}, "Microsoft.AspNetCore.JsonPatch/2.2.0": {"dependencies": {"Microsoft.CSharp": "4.7.0", "Newtonsoft.Json": "13.0.2"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.JsonPatch.dll": {}}}, "Microsoft.AspNetCore.Localization/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Http.Extensions": "2.2.0", "Microsoft.Extensions.Localization.Abstractions": "2.2.0", "Microsoft.Extensions.Logging.Abstractions": "2.2.0", "Microsoft.Extensions.Options": "2.2.0"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Localization.dll": {}}}, "Microsoft.AspNetCore.Mvc/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Mvc.Analyzers": "2.2.0", "Microsoft.AspNetCore.Mvc.ApiExplorer": "2.2.0", "Microsoft.AspNetCore.Mvc.Cors": "2.2.0", "Microsoft.AspNetCore.Mvc.DataAnnotations": "2.2.0", "Microsoft.AspNetCore.Mvc.Formatters.Json": "2.2.0", "Microsoft.AspNetCore.Mvc.Localization": "2.2.0", "Microsoft.AspNetCore.Mvc.Razor.Extensions": "2.2.0", "Microsoft.AspNetCore.Mvc.RazorPages": "2.2.0", "Microsoft.AspNetCore.Mvc.TagHelpers": "2.2.0", "Microsoft.AspNetCore.Mvc.ViewFeatures": "2.2.0", "Microsoft.AspNetCore.Razor.Design": "2.2.0", "Microsoft.Extensions.Caching.Memory": "2.2.0", "Microsoft.Extensions.DependencyInjection": "2.2.0"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Mvc.dll": {}}}, "Microsoft.AspNetCore.Mvc.Abstractions/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Routing.Abstractions": "2.2.0", "Microsoft.Net.Http.Headers": "2.2.0"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Mvc.Abstractions.dll": {}}}, "Microsoft.AspNetCore.Mvc.Analyzers/2.2.0": {}, "Microsoft.AspNetCore.Mvc.ApiExplorer/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Mvc.Core": "2.2.0"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Mvc.ApiExplorer.dll": {}}}, "Microsoft.AspNetCore.Mvc.Core/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Authentication.Core": "2.2.0", "Microsoft.AspNetCore.Authorization.Policy": "2.2.0", "Microsoft.AspNetCore.Hosting.Abstractions": "2.2.0", "Microsoft.AspNetCore.Http": "2.2.0", "Microsoft.AspNetCore.Http.Extensions": "2.2.0", "Microsoft.AspNetCore.Mvc.Abstractions": "2.2.0", "Microsoft.AspNetCore.ResponseCaching.Abstractions": "2.2.0", "Microsoft.AspNetCore.Routing": "2.2.0", "Microsoft.AspNetCore.Routing.Abstractions": "2.2.0", "Microsoft.Extensions.DependencyInjection": "2.2.0", "Microsoft.Extensions.DependencyModel": "2.1.0", "Microsoft.Extensions.FileProviders.Abstractions": "2.2.0", "Microsoft.Extensions.Logging.Abstractions": "2.2.0", "System.Diagnostics.DiagnosticSource": "4.6.0", "System.Threading.Tasks.Extensions": "4.5.4"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Mvc.Core.dll": {}}}, "Microsoft.AspNetCore.Mvc.Cors/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Cors": "2.2.0", "Microsoft.AspNetCore.Mvc.Core": "2.2.0"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Mvc.Cors.dll": {}}}, "Microsoft.AspNetCore.Mvc.DataAnnotations/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Mvc.Core": "2.2.0", "Microsoft.Extensions.Localization": "2.2.0", "System.ComponentModel.Annotations": "4.5.0"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Mvc.DataAnnotations.dll": {}}}, "Microsoft.AspNetCore.Mvc.Formatters.Json/2.2.0": {"dependencies": {"Microsoft.AspNetCore.JsonPatch": "2.2.0", "Microsoft.AspNetCore.Mvc.Core": "2.2.0"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Mvc.Formatters.Json.dll": {}}}, "Microsoft.AspNetCore.Mvc.Localization/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Localization": "2.2.0", "Microsoft.AspNetCore.Mvc.Razor": "2.2.0", "Microsoft.Extensions.DependencyInjection": "2.2.0", "Microsoft.Extensions.Localization": "2.2.0"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Mvc.Localization.dll": {}}}, "Microsoft.AspNetCore.Mvc.Razor/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Mvc.Razor.Extensions": "2.2.0", "Microsoft.AspNetCore.Mvc.ViewFeatures": "2.2.0", "Microsoft.AspNetCore.Razor.Runtime": "2.2.0", "Microsoft.CodeAnalysis.CSharp": "2.8.0", "Microsoft.CodeAnalysis.Razor": "2.2.0", "Microsoft.DiaSymReader.Native": "1.7.0", "Microsoft.Extensions.Caching.Memory": "2.2.0", "Microsoft.Extensions.FileProviders.Composite": "2.2.0"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Mvc.Razor.dll": {}}}, "Microsoft.AspNetCore.Mvc.Razor.Extensions/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Razor.Language": "2.2.0", "Microsoft.CodeAnalysis.Razor": "2.2.0"}, "compile": {"lib/net46/Microsoft.AspNetCore.Mvc.Razor.Extensions.dll": {}}}, "Microsoft.AspNetCore.Mvc.RazorPages/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Mvc.Razor": "2.2.0"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Mvc.RazorPages.dll": {}}}, "Microsoft.AspNetCore.Mvc.TagHelpers/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Mvc.Razor": "2.2.0", "Microsoft.AspNetCore.Razor.Runtime": "2.2.0", "Microsoft.AspNetCore.Routing.Abstractions": "2.2.0", "Microsoft.Extensions.Caching.Memory": "2.2.0", "Microsoft.Extensions.FileSystemGlobbing": "2.2.0", "Microsoft.Extensions.Primitives": "2.2.0"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Mvc.TagHelpers.dll": {}}}, "Microsoft.AspNetCore.Mvc.ViewFeatures/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Antiforgery": "2.2.0", "Microsoft.AspNetCore.Diagnostics.Abstractions": "2.2.0", "Microsoft.AspNetCore.Html.Abstractions": "2.2.0", "Microsoft.AspNetCore.Mvc.Core": "2.2.0", "Microsoft.AspNetCore.Mvc.DataAnnotations": "2.2.0", "Microsoft.AspNetCore.Mvc.Formatters.Json": "2.2.0", "Microsoft.Extensions.WebEncoders": "2.2.0", "Newtonsoft.Json.Bson": "1.0.1"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Mvc.ViewFeatures.dll": {}}}, "Microsoft.AspNetCore.Razor/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Html.Abstractions": "2.2.0"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Razor.dll": {}}}, "Microsoft.AspNetCore.Razor.Design/2.2.0": {}, "Microsoft.AspNetCore.Razor.Language/2.2.0": {"compile": {"lib/net46/Microsoft.AspNetCore.Razor.Language.dll": {}}}, "Microsoft.AspNetCore.Razor.Runtime/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Html.Abstractions": "2.2.0", "Microsoft.AspNetCore.Razor": "2.2.0"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Razor.Runtime.dll": {}}}, "Microsoft.AspNetCore.ResponseCaching.Abstractions/2.2.0": {"dependencies": {"Microsoft.Extensions.Primitives": "2.2.0"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.ResponseCaching.Abstractions.dll": {}}}, "Microsoft.AspNetCore.Routing/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Http.Extensions": "2.2.0", "Microsoft.AspNetCore.Routing.Abstractions": "2.2.0", "Microsoft.Extensions.Logging.Abstractions": "2.2.0", "Microsoft.Extensions.ObjectPool": "2.2.0", "Microsoft.Extensions.Options": "2.2.0"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Routing.dll": {}}}, "Microsoft.AspNetCore.Routing.Abstractions/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Http.Abstractions": "2.2.0"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Routing.Abstractions.dll": {}}}, "Microsoft.AspNetCore.Server.IIS/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Authentication.Core": "2.2.0", "Microsoft.AspNetCore.Connections.Abstractions": "2.2.0", "Microsoft.AspNetCore.Hosting.Abstractions": "2.2.0", "System.IO.Pipelines": "4.5.2", "System.Security.Principal.Windows": "4.5.0"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Server.IIS.dll": {}}}, "Microsoft.AspNetCore.Server.IISIntegration/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Authentication.Core": "2.2.0", "Microsoft.AspNetCore.Hosting.Abstractions": "2.2.0", "Microsoft.AspNetCore.Http": "2.2.0", "Microsoft.AspNetCore.Http.Extensions": "2.2.0", "Microsoft.AspNetCore.HttpOverrides": "2.2.0", "Microsoft.Extensions.Logging.Abstractions": "2.2.0", "Microsoft.Extensions.Options": "2.2.0", "System.Buffers": "4.5.1", "System.IO.Pipelines": "4.5.2", "System.Memory": "4.5.5", "System.Numerics.Vectors": "4.5.0", "System.Runtime.CompilerServices.Unsafe": "6.0.0", "System.Security.Principal.Windows": "4.5.0"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Server.IISIntegration.dll": {}}}, "Microsoft.AspNetCore.Server.Kestrel/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Hosting": "2.2.0", "Microsoft.AspNetCore.Server.Kestrel.Core": "2.2.0", "Microsoft.AspNetCore.Server.Kestrel.Https": "2.2.0", "Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets": "2.2.0"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Server.Kestrel.dll": {}}}, "Microsoft.AspNetCore.Server.Kestrel.Core/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Hosting.Abstractions": "2.2.0", "Microsoft.AspNetCore.Http": "2.2.0", "Microsoft.AspNetCore.Server.Kestrel.Transport.Abstractions": "2.2.0", "Microsoft.AspNetCore.WebUtilities": "2.2.0", "Microsoft.Extensions.Configuration.Binder": "2.2.0", "Microsoft.Extensions.Logging.Abstractions": "2.2.0", "Microsoft.Extensions.Options": "2.2.0", "Microsoft.Net.Http.Headers": "2.2.0", "System.Memory": "4.5.5", "System.Numerics.Vectors": "4.5.0", "System.Runtime.CompilerServices.Unsafe": "6.0.0", "System.Security.Cryptography.Cng": "4.5.0", "System.Threading.Tasks.Extensions": "4.5.4"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Server.Kestrel.Core.dll": {}}}, "Microsoft.AspNetCore.Server.Kestrel.Https/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Http.Abstractions": "2.2.0", "Microsoft.AspNetCore.Server.Kestrel.Core": "2.2.0"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Server.Kestrel.Https.dll": {}}}, "Microsoft.AspNetCore.Server.Kestrel.Transport.Abstractions/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Connections.Abstractions": "2.2.0"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Server.Kestrel.Transport.Abstractions.dll": {}}}, "Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Hosting.Abstractions": "2.2.0", "Microsoft.AspNetCore.Server.Kestrel.Transport.Abstractions": "2.2.0", "Microsoft.Extensions.Options": "2.2.0"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.dll": {}}}, "Microsoft.AspNetCore.WebUtilities/2.2.0": {"dependencies": {"Microsoft.Net.Http.Headers": "2.2.0", "System.Text.Encodings.Web": "8.0.0"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.WebUtilities.dll": {}}}, "Microsoft.Bcl.AsyncInterfaces/8.0.0": {"dependencies": {"System.Threading.Tasks.Extensions": "4.5.4"}, "compile": {"lib/net462/Microsoft.Bcl.AsyncInterfaces.dll": {}}}, "Microsoft.CodeAnalysis.Analyzers/1.1.0": {}, "Microsoft.CodeAnalysis.Common/2.8.0": {"dependencies": {"Microsoft.CodeAnalysis.Analyzers": "1.1.0", "System.AppContext": "4.3.0", "System.Collections": "4.3.0", "System.Collections.Concurrent": "4.3.0", "System.Collections.Immutable": "1.7.0", "System.Console": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Diagnostics.FileVersionInfo": "4.3.0", "System.Diagnostics.StackTrace": "4.3.0", "System.Diagnostics.Tools": "4.3.0", "System.Dynamic.Runtime": "4.3.0", "System.Globalization": "4.3.0", "System.IO.Compression": "4.3.0", "System.IO.FileSystem": "4.3.0", "System.IO.FileSystem.Primitives": "4.3.0", "System.Linq": "4.3.0", "System.Linq.Expressions": "4.3.0", "System.Reflection": "4.3.0", "System.Reflection.Metadata": "1.6.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Runtime.Numerics": "4.3.0", "System.Security.Cryptography.Algorithms": "4.3.0", "System.Security.Cryptography.Encoding": "4.3.0", "System.Security.Cryptography.X509Certificates": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Text.Encoding.CodePages": "4.3.0", "System.Text.Encoding.Extensions": "4.3.0", "System.Threading": "4.3.0", "System.Threading.Tasks": "4.3.0", "System.Threading.Tasks.Parallel": "4.3.0", "System.Threading.Thread": "4.3.0", "System.ValueTuple": "4.5.0", "System.Xml.ReaderWriter": "4.3.0", "System.Xml.XDocument": "4.3.0", "System.Xml.XPath.XDocument": "4.3.0", "System.Xml.XmlDocument": "4.3.0"}, "compile": {"lib/netstandard1.3/Microsoft.CodeAnalysis.dll": {}}}, "Microsoft.CodeAnalysis.CSharp/2.8.0": {"dependencies": {"Microsoft.CodeAnalysis.Common": "2.8.0"}, "compile": {"lib/netstandard1.3/Microsoft.CodeAnalysis.CSharp.dll": {}}}, "Microsoft.CodeAnalysis.Razor/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Razor.Language": "2.2.0", "Microsoft.CodeAnalysis.CSharp": "2.8.0", "Microsoft.CodeAnalysis.Common": "2.8.0", "System.Runtime.InteropServices.RuntimeInformation": "4.3.0"}, "compile": {"lib/net46/Microsoft.CodeAnalysis.Razor.dll": {}}}, "Microsoft.CSharp/4.7.0": {}, "Microsoft.DiaSymReader.Native/1.7.0": {}, "Microsoft.DotNet.PlatformAbstractions/2.1.0": {"dependencies": {"System.Runtime.InteropServices.RuntimeInformation": "4.3.0"}, "compile": {"lib/net45/Microsoft.DotNet.PlatformAbstractions.dll": {}}}, "Microsoft.Extensions.Caching.Abstractions/2.2.0": {"dependencies": {"Microsoft.Extensions.Primitives": "2.2.0"}, "compile": {"lib/netstandard2.0/Microsoft.Extensions.Caching.Abstractions.dll": {}}}, "Microsoft.Extensions.Caching.Memory/2.2.0": {"dependencies": {"Microsoft.Extensions.Caching.Abstractions": "2.2.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "2.2.0", "Microsoft.Extensions.Options": "2.2.0"}, "compile": {"lib/netstandard2.0/Microsoft.Extensions.Caching.Memory.dll": {}}}, "Microsoft.Extensions.Configuration/2.2.0": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "2.2.0"}, "compile": {"lib/netstandard2.0/Microsoft.Extensions.Configuration.dll": {}}}, "Microsoft.Extensions.Configuration.Abstractions/2.2.0": {"dependencies": {"Microsoft.Extensions.Primitives": "2.2.0"}, "compile": {"lib/netstandard2.0/Microsoft.Extensions.Configuration.Abstractions.dll": {}}}, "Microsoft.Extensions.Configuration.Binder/2.2.0": {"dependencies": {"Microsoft.Extensions.Configuration": "2.2.0"}, "compile": {"lib/netstandard2.0/Microsoft.Extensions.Configuration.Binder.dll": {}}}, "Microsoft.Extensions.Configuration.CommandLine/2.2.0": {"dependencies": {"Microsoft.Extensions.Configuration": "2.2.0"}, "compile": {"lib/netstandard2.0/Microsoft.Extensions.Configuration.CommandLine.dll": {}}}, "Microsoft.Extensions.Configuration.EnvironmentVariables/2.2.0": {"dependencies": {"Microsoft.Extensions.Configuration": "2.2.0"}, "compile": {"lib/netstandard2.0/Microsoft.Extensions.Configuration.EnvironmentVariables.dll": {}}}, "Microsoft.Extensions.Configuration.FileExtensions/2.2.0": {"dependencies": {"Microsoft.Extensions.Configuration": "2.2.0", "Microsoft.Extensions.FileProviders.Physical": "2.2.0"}, "compile": {"lib/netstandard2.0/Microsoft.Extensions.Configuration.FileExtensions.dll": {}}}, "Microsoft.Extensions.Configuration.Json/2.2.0": {"dependencies": {"Microsoft.Extensions.Configuration": "2.2.0", "Microsoft.Extensions.Configuration.FileExtensions": "2.2.0", "Newtonsoft.Json": "13.0.2"}, "compile": {"lib/netstandard2.0/Microsoft.Extensions.Configuration.Json.dll": {}}}, "Microsoft.Extensions.Configuration.UserSecrets/2.2.0": {"dependencies": {"Microsoft.Extensions.Configuration.Json": "2.2.0"}, "compile": {"lib/netstandard2.0/Microsoft.Extensions.Configuration.UserSecrets.dll": {}}}, "Microsoft.Extensions.DependencyInjection/2.2.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "2.2.0"}, "compile": {"lib/net461/Microsoft.Extensions.DependencyInjection.dll": {}}}, "Microsoft.Extensions.DependencyInjection.Abstractions/2.2.0": {"compile": {"lib/netstandard2.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll": {}}}, "Microsoft.Extensions.DependencyModel/2.1.0": {"dependencies": {"Microsoft.DotNet.PlatformAbstractions": "2.1.0", "Newtonsoft.Json": "13.0.2"}, "compile": {"lib/net451/Microsoft.Extensions.DependencyModel.dll": {}}}, "Microsoft.Extensions.FileProviders.Abstractions/2.2.0": {"dependencies": {"Microsoft.Extensions.Primitives": "2.2.0"}, "compile": {"lib/netstandard2.0/Microsoft.Extensions.FileProviders.Abstractions.dll": {}}}, "Microsoft.Extensions.FileProviders.Composite/2.2.0": {"dependencies": {"Microsoft.Extensions.FileProviders.Abstractions": "2.2.0"}, "compile": {"lib/netstandard2.0/Microsoft.Extensions.FileProviders.Composite.dll": {}}}, "Microsoft.Extensions.FileProviders.Physical/2.2.0": {"dependencies": {"Microsoft.Extensions.FileProviders.Abstractions": "2.2.0", "Microsoft.Extensions.FileSystemGlobbing": "2.2.0"}, "compile": {"lib/netstandard2.0/Microsoft.Extensions.FileProviders.Physical.dll": {}}}, "Microsoft.Extensions.FileSystemGlobbing/2.2.0": {"compile": {"lib/netstandard2.0/Microsoft.Extensions.FileSystemGlobbing.dll": {}}}, "Microsoft.Extensions.Hosting.Abstractions/2.2.0": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "2.2.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "2.2.0", "Microsoft.Extensions.FileProviders.Abstractions": "2.2.0", "Microsoft.Extensions.Logging.Abstractions": "2.2.0"}, "compile": {"lib/netstandard2.0/Microsoft.Extensions.Hosting.Abstractions.dll": {}}}, "Microsoft.Extensions.Localization/2.2.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "2.2.0", "Microsoft.Extensions.Localization.Abstractions": "2.2.0", "Microsoft.Extensions.Logging.Abstractions": "2.2.0", "Microsoft.Extensions.Options": "2.2.0"}, "compile": {"lib/netstandard2.0/Microsoft.Extensions.Localization.dll": {}}}, "Microsoft.Extensions.Localization.Abstractions/2.2.0": {"compile": {"lib/netstandard2.0/Microsoft.Extensions.Localization.Abstractions.dll": {}}}, "Microsoft.Extensions.Logging/2.2.0": {"dependencies": {"Microsoft.Extensions.Configuration.Binder": "2.2.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "2.2.0", "Microsoft.Extensions.Logging.Abstractions": "2.2.0", "Microsoft.Extensions.Options": "2.2.0"}, "compile": {"lib/netstandard2.0/Microsoft.Extensions.Logging.dll": {}}}, "Microsoft.Extensions.Logging.Abstractions/2.2.0": {"compile": {"lib/netstandard2.0/Microsoft.Extensions.Logging.Abstractions.dll": {}}}, "Microsoft.Extensions.Logging.Configuration/2.2.0": {"dependencies": {"Microsoft.Extensions.Logging": "2.2.0", "Microsoft.Extensions.Options.ConfigurationExtensions": "2.2.0"}, "compile": {"lib/netstandard2.0/Microsoft.Extensions.Logging.Configuration.dll": {}}}, "Microsoft.Extensions.Logging.Console/2.2.0": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "2.2.0", "Microsoft.Extensions.Logging": "2.2.0", "Microsoft.Extensions.Logging.Configuration": "2.2.0"}, "compile": {"lib/netstandard2.0/Microsoft.Extensions.Logging.Console.dll": {}}}, "Microsoft.Extensions.Logging.Debug/2.2.0": {"dependencies": {"Microsoft.Extensions.Logging": "2.2.0"}, "compile": {"lib/netstandard2.0/Microsoft.Extensions.Logging.Debug.dll": {}}}, "Microsoft.Extensions.Logging.EventSource/2.2.0": {"dependencies": {"Microsoft.Extensions.Logging": "2.2.0", "Newtonsoft.Json": "13.0.2"}, "compile": {"lib/netstandard2.0/Microsoft.Extensions.Logging.EventSource.dll": {}}}, "Microsoft.Extensions.ObjectPool/2.2.0": {"compile": {"lib/netstandard2.0/Microsoft.Extensions.ObjectPool.dll": {}}}, "Microsoft.Extensions.Options/2.2.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "2.2.0", "Microsoft.Extensions.Primitives": "2.2.0", "System.ComponentModel.Annotations": "4.5.0"}, "compile": {"lib/netstandard2.0/Microsoft.Extensions.Options.dll": {}}}, "Microsoft.Extensions.Options.ConfigurationExtensions/2.2.0": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "2.2.0", "Microsoft.Extensions.Configuration.Binder": "2.2.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "2.2.0", "Microsoft.Extensions.Options": "2.2.0"}, "compile": {"lib/netstandard2.0/Microsoft.Extensions.Options.ConfigurationExtensions.dll": {}}}, "Microsoft.Extensions.Primitives/2.2.0": {"dependencies": {"System.Memory": "4.5.5", "System.Runtime.CompilerServices.Unsafe": "6.0.0"}, "compile": {"lib/netstandard2.0/Microsoft.Extensions.Primitives.dll": {}}}, "Microsoft.Extensions.WebEncoders/2.2.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "2.2.0", "Microsoft.Extensions.Options": "2.2.0", "System.Text.Encodings.Web": "8.0.0"}, "compile": {"lib/netstandard2.0/Microsoft.Extensions.WebEncoders.dll": {}}}, "Microsoft.Graph/3.1.0": {"dependencies": {"Microsoft.Graph.Core": "1.20.1"}, "compile": {"lib/net461/Microsoft.Graph.dll": {}}}, "Microsoft.Graph.Communications.Calls/1.2.0.850": {"dependencies": {"Microsoft.Graph.Communications.Client": "1.2.0.850", "Microsoft.Graph.Communications.Core": "1.2.0.850"}, "compile": {"lib/net461/Microsoft.Graph.Communications.Calls.dll": {}}}, "Microsoft.Graph.Communications.Calls.Media/1.2.0.850": {"dependencies": {"Microsoft.Graph.Communications.Calls": "1.2.0.850", "Microsoft.Skype.Bots.Media": "*********-alpha"}, "compile": {"lib/net472/Microsoft.Graph.Communications.Calls.Media.dll": {}}}, "Microsoft.Graph.Communications.Client/1.2.0.850": {"dependencies": {"Microsoft.Graph.Communications.Core": "1.2.0.850", "System.Threading.Tasks.Dataflow": "4.9.0"}, "compile": {"lib/net461/Microsoft.Graph.Communications.Client.dll": {}}}, "Microsoft.Graph.Communications.Common/1.2.0.850": {"dependencies": {"Microsoft.Extensions.Logging.Abstractions": "2.2.0", "Newtonsoft.Json": "13.0.2"}, "compile": {"lib/net461/Microsoft.Graph.Communications.Common.dll": {}}}, "Microsoft.Graph.Communications.Core/1.2.0.850": {"dependencies": {"Microsoft.Graph": "3.1.0", "Microsoft.Graph.Communications.Common": "1.2.0.850", "Microsoft.Graph.Core": "1.20.1", "Newtonsoft.Json": "13.0.2"}, "compile": {"lib/net461/Microsoft.Graph.Communications.Core.dll": {}}}, "Microsoft.Graph.Core/1.20.1": {"dependencies": {"Newtonsoft.Json": "13.0.2", "System.Diagnostics.DiagnosticSource": "4.6.0", "System.ValueTuple": "4.5.0"}, "compile": {"lib/net45/Microsoft.Graph.Core.dll": {}}}, "Microsoft.IdentityModel.Abstractions/8.0.1": {"compile": {"lib/net472/Microsoft.IdentityModel.Abstractions.dll": {}}}, "Microsoft.IdentityModel.Clients.ActiveDirectory/5.2.4": {"dependencies": {"System.Net.Http": "4.3.4"}, "compile": {"lib/net45/Microsoft.IdentityModel.Clients.ActiveDirectory.dll": {}}}, "Microsoft.IdentityModel.JsonWebTokens/8.0.1": {"dependencies": {"Microsoft.IdentityModel.Tokens": "8.0.1"}, "compile": {"lib/net472/Microsoft.IdentityModel.JsonWebTokens.dll": {}}}, "Microsoft.IdentityModel.Logging/8.0.1": {"dependencies": {"Microsoft.IdentityModel.Abstractions": "8.0.1"}, "compile": {"lib/net472/Microsoft.IdentityModel.Logging.dll": {}}}, "Microsoft.IdentityModel.Protocols/8.0.1": {"dependencies": {"Microsoft.IdentityModel.Tokens": "8.0.1"}, "compile": {"lib/net472/Microsoft.IdentityModel.Protocols.dll": {}}}, "Microsoft.IdentityModel.Protocols.OpenIdConnect/8.0.1": {"dependencies": {"Microsoft.IdentityModel.Protocols": "8.0.1", "System.IdentityModel.Tokens.Jwt": "8.0.1", "System.Text.Json": "8.0.4"}, "compile": {"lib/net472/Microsoft.IdentityModel.Protocols.OpenIdConnect.dll": {}}}, "Microsoft.IdentityModel.Tokens/8.0.1": {"dependencies": {"Microsoft.IdentityModel.Logging": "8.0.1", "System.Memory": "4.5.5", "System.Text.Json": "8.0.4"}, "compile": {"lib/net472/Microsoft.IdentityModel.Tokens.dll": {}}}, "Microsoft.Net.Http.Headers/2.2.0": {"dependencies": {"Microsoft.Extensions.Primitives": "2.2.0", "System.Buffers": "4.5.1"}, "compile": {"lib/netstandard2.0/Microsoft.Net.Http.Headers.dll": {}}}, "Microsoft.Psi.Audio/**********-beta": {"dependencies": {"Microsoft.Psi.Data": "**********-beta", "Microsoft.Psi.Runtime": "**********-beta"}, "compile": {"lib/netstandard2.0/Microsoft.Psi.Audio.dll": {}}}, "Microsoft.Psi.Data/**********-beta": {"dependencies": {"Microsoft.Psi.Runtime": "**********-beta", "Newtonsoft.Json": "13.0.2"}, "compile": {"lib/netstandard2.0/Microsoft.Psi.Data.dll": {}}}, "Microsoft.Psi.Imaging/*********-beta": {"dependencies": {"Microsoft.Psi.Runtime": "**********-beta", "System.Drawing.Common": "4.7.2"}, "compile": {"lib/netstandard2.0/Microsoft.Psi.Imaging.dll": {}}}, "Microsoft.Psi.Imaging.Windows/*********-beta": {"dependencies": {"Microsoft.Psi.Imaging": "*********-beta", "Microsoft.Psi.Runtime": "**********-beta"}, "compile": {"lib/net472/Microsoft.Psi.Imaging.Windows.dll": {}}}, "Microsoft.Psi.Runtime/**********-beta": {"dependencies": {"Microsoft.CSharp": "4.7.0", "System.Collections.Immutable": "1.7.0", "System.Numerics.Vectors": "4.5.0", "System.Reflection.Emit.ILGeneration": "4.7.0", "System.Reflection.Emit.Lightweight": "4.7.0"}, "compile": {"lib/netstandard2.0/Microsoft.Psi.IL.dll": {}, "lib/netstandard2.0/Microsoft.Psi.dll": {}}}, "Microsoft.Skype.Bots.Media/*********-alpha": {"dependencies": {"Newtonsoft.Json": "13.0.2", "System.Threading.Tasks.Dataflow": "4.9.0"}, "compile": {"lib/net472/Microsoft.Skype.Bots.Media.dll": {}}}, "Microsoft.Win32.Registry/4.5.0": {"dependencies": {"System.Security.AccessControl": "4.5.0", "System.Security.Principal.Windows": "4.5.0"}, "compile": {"ref/net461/Microsoft.Win32.Registry.dll": {}}}, "Newtonsoft.Json/13.0.2": {"compile": {"lib/net45/Newtonsoft.Json.dll": {}}}, "Newtonsoft.Json.Bson/1.0.1": {"dependencies": {"Newtonsoft.Json": "13.0.2"}, "compile": {"lib/net45/Newtonsoft.Json.Bson.dll": {}}}, "System.AppContext/4.3.0": {}, "System.Buffers/4.5.1": {"compile": {"ref/net45/System.Buffers.dll": {}}}, "System.Collections/4.3.0": {}, "System.Collections.Concurrent/4.3.0": {}, "System.Collections.Immutable/1.7.0": {"dependencies": {"System.Memory": "4.5.5"}, "compile": {"lib/netstandard2.0/System.Collections.Immutable.dll": {}}}, "System.ComponentModel.Annotations/4.5.0": {"compile": {"ref/net461/System.ComponentModel.Annotations.dll": {}}}, "System.Console/4.3.0": {}, "System.Diagnostics.Debug/4.3.0": {}, "System.Diagnostics.DiagnosticSource/4.6.0": {"dependencies": {"System.Memory": "4.5.5"}, "compile": {"lib/net46/System.Diagnostics.DiagnosticSource.dll": {}}}, "System.Diagnostics.FileVersionInfo/4.3.0": {}, "System.Diagnostics.StackTrace/4.3.0": {}, "System.Diagnostics.Tools/4.3.0": {}, "System.Drawing.Common/4.7.2": {"compile": {"ref/net461/System.Drawing.Common.dll": {}}}, "System.Dynamic.Runtime/4.3.0": {}, "System.Globalization/4.3.0": {}, "System.IdentityModel.Tokens.Jwt/8.0.1": {"dependencies": {"Microsoft.IdentityModel.JsonWebTokens": "8.0.1", "Microsoft.IdentityModel.Tokens": "8.0.1"}, "compile": {"lib/net472/System.IdentityModel.Tokens.Jwt.dll": {}}}, "System.IO/4.3.0": {}, "System.IO.Compression/4.3.0": {}, "System.IO.FileSystem/4.3.0": {"dependencies": {"System.IO.FileSystem.Primitives": "4.3.0"}}, "System.IO.FileSystem.Primitives/4.3.0": {}, "System.IO.Pipelines/4.5.2": {"dependencies": {"System.Buffers": "4.5.1", "System.Memory": "4.5.5", "System.Threading.Tasks.Extensions": "4.5.4"}, "compile": {"ref/netstandard1.3/System.IO.Pipelines.dll": {}}}, "System.Linq/4.3.0": {}, "System.Linq.Expressions/4.3.0": {}, "System.Memory/4.5.5": {"dependencies": {"System.Buffers": "4.5.1", "System.Numerics.Vectors": "4.5.0", "System.Runtime.CompilerServices.Unsafe": "6.0.0"}, "compile": {"lib/net461/System.Memory.dll": {}}}, "System.Net.Http/4.3.4": {"dependencies": {"System.Security.Cryptography.X509Certificates": "4.3.0"}}, "System.Numerics.Vectors/4.5.0": {"compile": {"ref/net46/System.Numerics.Vectors.dll": {}}}, "System.Reflection/4.3.0": {}, "System.Reflection.Emit.ILGeneration/4.7.0": {}, "System.Reflection.Emit.Lightweight/4.7.0": {}, "System.Reflection.Metadata/1.6.0": {"dependencies": {"System.Collections.Immutable": "1.7.0"}, "compile": {"lib/netstandard2.0/System.Reflection.Metadata.dll": {}}}, "System.Resources.ResourceManager/4.3.0": {}, "System.Runtime/4.3.0": {}, "System.Runtime.CompilerServices.Unsafe/6.0.0": {"compile": {"lib/net461/System.Runtime.CompilerServices.Unsafe.dll": {}}}, "System.Runtime.Extensions/4.3.0": {}, "System.Runtime.InteropServices/4.3.0": {"dependencies": {"System.Runtime": "4.3.0"}}, "System.Runtime.InteropServices.RuntimeInformation/4.3.0": {}, "System.Runtime.Numerics/4.3.0": {}, "System.Security.AccessControl/4.5.0": {"dependencies": {"System.Security.Principal.Windows": "4.5.0"}, "compile": {"ref/net461/System.Security.AccessControl.dll": {}}}, "System.Security.Cryptography.Algorithms/4.3.0": {"dependencies": {"System.IO": "4.3.0", "System.Runtime": "4.3.0", "System.Security.Cryptography.Encoding": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0"}}, "System.Security.Cryptography.Cng/4.5.0": {"compile": {"ref/net47/System.Security.Cryptography.Cng.dll": {}}}, "System.Security.Cryptography.Encoding/4.3.0": {}, "System.Security.Cryptography.Primitives/4.3.0": {}, "System.Security.Cryptography.X509Certificates/4.3.0": {"dependencies": {"System.Security.Cryptography.Algorithms": "4.3.0", "System.Security.Cryptography.Encoding": "4.3.0"}}, "System.Security.Cryptography.Xml/4.5.0": {"dependencies": {"System.Security.Permissions": "4.5.0"}, "compile": {"ref/net461/System.Security.Cryptography.Xml.dll": {}}}, "System.Security.Permissions/4.5.0": {"dependencies": {"System.Security.AccessControl": "4.5.0"}, "compile": {"ref/net461/System.Security.Permissions.dll": {}}}, "System.Security.Principal.Windows/4.5.0": {"compile": {"ref/net461/System.Security.Principal.Windows.dll": {}}}, "System.Text.Encoding/4.3.0": {}, "System.Text.Encoding.CodePages/4.3.0": {}, "System.Text.Encoding.Extensions/4.3.0": {}, "System.Text.Encodings.Web/8.0.0": {"dependencies": {"System.Buffers": "4.5.1", "System.Memory": "4.5.5", "System.Runtime.CompilerServices.Unsafe": "6.0.0"}, "compile": {"lib/net462/System.Text.Encodings.Web.dll": {}}}, "System.Text.Json/8.0.4": {"dependencies": {"Microsoft.Bcl.AsyncInterfaces": "8.0.0", "System.Buffers": "4.5.1", "System.Memory": "4.5.5", "System.Runtime.CompilerServices.Unsafe": "6.0.0", "System.Text.Encodings.Web": "8.0.0", "System.Threading.Tasks.Extensions": "4.5.4", "System.ValueTuple": "4.5.0"}, "compile": {"lib/net462/System.Text.Json.dll": {}}}, "System.Threading/4.3.0": {}, "System.Threading.Tasks/4.3.0": {}, "System.Threading.Tasks.Dataflow/4.9.0": {"compile": {"lib/netstandard2.0/System.Threading.Tasks.Dataflow.dll": {}}}, "System.Threading.Tasks.Extensions/4.5.4": {"dependencies": {"System.Runtime.CompilerServices.Unsafe": "6.0.0"}, "compile": {"lib/net461/System.Threading.Tasks.Extensions.dll": {}}}, "System.Threading.Tasks.Parallel/4.3.0": {}, "System.Threading.Thread/4.3.0": {}, "System.ValueTuple/4.5.0": {"compile": {"ref/net47/System.ValueTuple.dll": {}}}, "System.Xml.ReaderWriter/4.3.0": {}, "System.Xml.XDocument/4.3.0": {}, "System.Xml.XmlDocument/4.3.0": {}, "System.Xml.XPath/4.3.0": {}, "System.Xml.XPath.XDocument/4.3.0": {"dependencies": {"System.Xml.XPath": "4.3.0"}}}, ".NETFramework,Version=v4.8/win-x86": {"AccureMD.MediaBot.Worker/1.0.0": {"dependencies": {"Microsoft.AspNetCore": "2.2.0", "Microsoft.AspNetCore.HttpsPolicy": "2.2.0", "Microsoft.AspNetCore.Mvc": "2.2.0", "Microsoft.Graph.Communications.Calls": "1.2.0.850", "Microsoft.Graph.Communications.Calls.Media": "1.2.0.850", "Microsoft.Graph.Communications.Client": "1.2.0.850", "Microsoft.Graph.Communications.Common": "1.2.0.850", "Microsoft.Graph.Communications.Core": "1.2.0.850", "Microsoft.IdentityModel.Clients.ActiveDirectory": "5.2.4", "Microsoft.IdentityModel.Protocols.OpenIdConnect": "8.0.1", "Microsoft.Psi.Audio": "**********-beta", "Microsoft.Psi.Imaging": "*********-beta", "Microsoft.Psi.Imaging.Windows": "*********-beta", "Microsoft.Skype.Bots.Media": "*********-alpha", "Microsoft.CSharp.Reference": "4.0.0.0", "mscorlib": "4.0.0.0", "PresentationCore": "4.0.0.0", "System.ComponentModel.Composition": "4.0.0.0", "System.ComponentModel.DataAnnotations": "4.0.0.0", "System.Data.DataSetExtensions": "4.0.0.0", "System.Data.OracleClient": "4.0.0.0", "System.IdentityModel": "4.0.0.0", "System.Net": "4.0.0.0", "System.Security": "4.0.0.0", "System.ServiceProcess": "4.0.0.0", "System.Transactions": "4.0.0.0", "System.Windows.Forms": "4.0.0.0", "WindowsBase": "4.0.0.0", "System.AppContext.Reference": "4.1.2.0", "System.Console.Reference": "4.0.2.0", "System.Core": "4.0.0.0", "System.Data": "4.0.0.0", "System": "4.0.0.0", "System.Drawing": "4.0.0.0", "System.IO.Compression.Reference": "4.2.0.0", "System.IO.Compression.FileSystem": "4.0.0.0", "System.IO.Reference": "4.1.2.0", "System.IO.FileSystem.Reference": "4.0.3.0", "System.IO.FileSystem.Primitives.Reference": "4.0.3.0", "System.Linq.Reference": "4.1.2.0", "System.Linq.Expressions.Reference": "4.1.2.0", "System.Net.Http.Reference": "4.2.0.0", "System.Numerics": "4.0.0.0", "System.Reflection.Reference": "4.1.2.0", "System.Runtime.Reference": "4.1.2.0", "System.Runtime.Extensions.Reference": "4.1.2.0", "System.Runtime.InteropServices.Reference": "4.1.2.0", "System.Runtime.InteropServices.RuntimeInformation.Reference": "4.0.2.0", "System.Runtime.Serialization": "4.0.0.0", "System.Security.Cryptography.Algorithms.Reference": "4.3.0.0", "System.Security.Cryptography.Encoding.Reference": "4.0.2.0", "System.Security.Cryptography.Primitives.Reference": "4.0.2.0", "System.Security.Cryptography.X509Certificates.Reference": "4.1.2.0", "System.Xml": "4.0.0.0", "System.Xml.Linq": "4.0.0.0", "System.Xml.ReaderWriter.Reference": "4.1.1.0", "Microsoft.Win32.Primitives": "0.0.0.0", "netstandard": "0.0.0.0", "System.Collections.Concurrent.Reference": "0.0.0.0", "System.Collections.Reference": "0.0.0.0", "System.Collections.NonGeneric": "0.0.0.0", "System.Collections.Specialized": "0.0.0.0", "System.ComponentModel": "0.0.0.0", "System.ComponentModel.EventBasedAsync": "0.0.0.0", "System.ComponentModel.Primitives": "0.0.0.0", "System.ComponentModel.TypeConverter": "0.0.0.0", "System.Data.Common": "0.0.0.0", "System.Diagnostics.Contracts": "0.0.0.0", "System.Diagnostics.Debug.Reference": "0.0.0.0", "System.Diagnostics.FileVersionInfo.Reference": "0.0.0.0", "System.Diagnostics.Process": "0.0.0.0", "System.Diagnostics.StackTrace.Reference": "0.0.0.0", "System.Diagnostics.TextWriterTraceListener": "0.0.0.0", "System.Diagnostics.Tools.Reference": "0.0.0.0", "System.Diagnostics.TraceSource": "0.0.0.0", "System.Drawing.Primitives": "0.0.0.0", "System.Dynamic.Runtime.Reference": "0.0.0.0", "System.Globalization.Calendars": "0.0.0.0", "System.Globalization.Reference": "0.0.0.0", "System.Globalization.Extensions": "0.0.0.0", "System.IO.Compression.ZipFile": "0.0.0.0", "System.IO.FileSystem.DriveInfo": "0.0.0.0", "System.IO.FileSystem.Watcher": "0.0.0.0", "System.IO.IsolatedStorage": "0.0.0.0", "System.IO.MemoryMappedFiles": "0.0.0.0", "System.IO.Pipes": "0.0.0.0", "System.IO.UnmanagedMemoryStream": "0.0.0.0", "System.Linq.Parallel": "0.0.0.0", "System.Linq.Queryable": "0.0.0.0", "System.Net.Http.Rtc": "0.0.0.0", "System.Net.NameResolution": "0.0.0.0", "System.Net.NetworkInformation": "0.0.0.0", "System.Net.Ping": "0.0.0.0", "System.Net.Primitives": "0.0.0.0", "System.Net.Requests": "0.0.0.0", "System.Net.Security": "0.0.0.0", "System.Net.Sockets": "0.0.0.0", "System.Net.WebHeaderCollection": "0.0.0.0", "System.Net.WebSockets.Client": "0.0.0.0", "System.Net.WebSockets": "0.0.0.0", "System.ObjectModel": "0.0.0.0", "System.Reflection.Emit": "0.0.0.0", "System.Reflection.Emit.ILGeneration.Reference": "0.0.0.0", "System.Reflection.Emit.Lightweight.Reference": "0.0.0.0", "System.Reflection.Extensions": "0.0.0.0", "System.Reflection.Primitives": "0.0.0.0", "System.Resources.Reader": "0.0.0.0", "System.Resources.ResourceManager.Reference": "0.0.0.0", "System.Resources.Writer": "0.0.0.0", "System.Runtime.CompilerServices.VisualC": "0.0.0.0", "System.Runtime.Handles": "0.0.0.0", "System.Runtime.InteropServices.WindowsRuntime": "0.0.0.0", "System.Runtime.Numerics.Reference": "0.0.0.0", "System.Runtime.Serialization.Formatters": "0.0.0.0", "System.Runtime.Serialization.Json": "0.0.0.0", "System.Runtime.Serialization.Primitives": "0.0.0.0", "System.Runtime.Serialization.Xml": "0.0.0.0", "System.Security.Claims": "0.0.0.0", "System.Security.Cryptography.Csp": "0.0.0.0", "System.Security.Principal": "0.0.0.0", "System.Security.SecureString": "0.0.0.0", "System.ServiceModel.Duplex": "0.0.0.0", "System.ServiceModel.Http": "0.0.0.0", "System.ServiceModel.NetTcp": "0.0.0.0", "System.ServiceModel.Primitives": "0.0.0.0", "System.ServiceModel.Security": "0.0.0.0", "System.Text.Encoding.Reference": "0.0.0.0", "System.Text.Encoding.Extensions.Reference": "0.0.0.0", "System.Text.RegularExpressions": "0.0.0.0", "System.Threading.Reference": "0.0.0.0", "System.Threading.Overlapped": "0.0.0.0", "System.Threading.Tasks.Reference": "0.0.0.0", "System.Threading.Tasks.Parallel.Reference": "0.0.0.0", "System.Threading.Thread.Reference": "0.0.0.0", "System.Threading.ThreadPool": "0.0.0.0", "System.Threading.Timer": "0.0.0.0", "System.Xml.XDocument.Reference": "0.0.0.0", "System.Xml.XmlDocument.Reference": "0.0.0.0", "System.Xml.XmlSerializer": "0.0.0.0", "System.Xml.XPath.Reference": "0.0.0.0", "System.Xml.XPath.XDocument.Reference": "0.0.0.0"}, "runtime": {"AccureMD.MediaBot.Worker.exe": {}}}, "Microsoft.AspNetCore/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Diagnostics": "2.2.0", "Microsoft.AspNetCore.HostFiltering": "2.2.0", "Microsoft.AspNetCore.Hosting": "2.2.0", "Microsoft.AspNetCore.Routing": "2.2.0", "Microsoft.AspNetCore.Server.IIS": "2.2.0", "Microsoft.AspNetCore.Server.IISIntegration": "2.2.0", "Microsoft.AspNetCore.Server.Kestrel": "2.2.0", "Microsoft.AspNetCore.Server.Kestrel.Https": "2.2.0", "Microsoft.Extensions.Configuration.CommandLine": "2.2.0", "Microsoft.Extensions.Configuration.EnvironmentVariables": "2.2.0", "Microsoft.Extensions.Configuration.FileExtensions": "2.2.0", "Microsoft.Extensions.Configuration.Json": "2.2.0", "Microsoft.Extensions.Configuration.UserSecrets": "2.2.0", "Microsoft.Extensions.Logging": "2.2.0", "Microsoft.Extensions.Logging.Configuration": "2.2.0", "Microsoft.Extensions.Logging.Console": "2.2.0", "Microsoft.Extensions.Logging.Debug": "2.2.0", "Microsoft.Extensions.Logging.EventSource": "2.2.0"}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.dll": {}}}, "Microsoft.AspNetCore.Antiforgery/2.2.0": {"dependencies": {"Microsoft.AspNetCore.DataProtection": "2.2.0", "Microsoft.AspNetCore.Http.Abstractions": "2.2.0", "Microsoft.AspNetCore.Http.Extensions": "2.2.0", "Microsoft.AspNetCore.WebUtilities": "2.2.0", "Microsoft.Extensions.ObjectPool": "2.2.0"}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Antiforgery.dll": {}}}, "Microsoft.AspNetCore.Authentication.Abstractions/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Http.Abstractions": "2.2.0", "Microsoft.Extensions.Logging.Abstractions": "2.2.0", "Microsoft.Extensions.Options": "2.2.0"}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Authentication.Abstractions.dll": {}}}, "Microsoft.AspNetCore.Authentication.Core/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Authentication.Abstractions": "2.2.0", "Microsoft.AspNetCore.Http": "2.2.0", "Microsoft.AspNetCore.Http.Extensions": "2.2.0"}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Authentication.Core.dll": {}}}, "Microsoft.AspNetCore.Authorization/2.2.0": {"dependencies": {"Microsoft.Extensions.Logging.Abstractions": "2.2.0", "Microsoft.Extensions.Options": "2.2.0"}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Authorization.dll": {}}}, "Microsoft.AspNetCore.Authorization.Policy/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Authentication.Abstractions": "2.2.0", "Microsoft.AspNetCore.Authorization": "2.2.0"}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Authorization.Policy.dll": {}}}, "Microsoft.AspNetCore.Connections.Abstractions/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Http.Features": "2.2.0", "System.IO.Pipelines": "4.5.2"}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Connections.Abstractions.dll": {}}}, "Microsoft.AspNetCore.Cors/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Http.Extensions": "2.2.0", "Microsoft.Extensions.Configuration.Abstractions": "2.2.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "2.2.0", "Microsoft.Extensions.Logging.Abstractions": "2.2.0", "Microsoft.Extensions.Options": "2.2.0"}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Cors.dll": {}}}, "Microsoft.AspNetCore.Cryptography.Internal/2.2.0": {"runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Cryptography.Internal.dll": {}}}, "Microsoft.AspNetCore.DataProtection/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Cryptography.Internal": "2.2.0", "Microsoft.AspNetCore.DataProtection.Abstractions": "2.2.0", "Microsoft.AspNetCore.Hosting.Abstractions": "2.2.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "2.2.0", "Microsoft.Extensions.Logging.Abstractions": "2.2.0", "Microsoft.Extensions.Options": "2.2.0", "Microsoft.Win32.Registry": "4.5.0", "System.Security.Cryptography.Xml": "4.5.0", "System.Security.Principal.Windows": "4.5.0"}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.DataProtection.dll": {}}}, "Microsoft.AspNetCore.DataProtection.Abstractions/2.2.0": {"runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.DataProtection.Abstractions.dll": {}}}, "Microsoft.AspNetCore.Diagnostics/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Diagnostics.Abstractions": "2.2.0", "Microsoft.AspNetCore.Hosting.Abstractions": "2.2.0", "Microsoft.AspNetCore.Http.Extensions": "2.2.0", "Microsoft.AspNetCore.WebUtilities": "2.2.0", "Microsoft.Extensions.FileProviders.Physical": "2.2.0", "Microsoft.Extensions.Logging.Abstractions": "2.2.0", "Microsoft.Extensions.Options": "2.2.0", "System.Diagnostics.DiagnosticSource": "4.6.0", "System.Reflection.Metadata": "1.6.0"}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Diagnostics.dll": {}}}, "Microsoft.AspNetCore.Diagnostics.Abstractions/2.2.0": {"runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Diagnostics.Abstractions.dll": {}}}, "Microsoft.AspNetCore.HostFiltering/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Hosting.Abstractions": "2.2.0", "Microsoft.AspNetCore.Http": "2.2.0", "Microsoft.AspNetCore.Http.Extensions": "2.2.0", "Microsoft.Extensions.Options": "2.2.0"}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.HostFiltering.dll": {}}}, "Microsoft.AspNetCore.Hosting/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Hosting.Abstractions": "2.2.0", "Microsoft.AspNetCore.Http": "2.2.0", "Microsoft.AspNetCore.Http.Extensions": "2.2.0", "Microsoft.Extensions.Configuration": "2.2.0", "Microsoft.Extensions.Configuration.EnvironmentVariables": "2.2.0", "Microsoft.Extensions.Configuration.FileExtensions": "2.2.0", "Microsoft.Extensions.DependencyInjection": "2.2.0", "Microsoft.Extensions.FileProviders.Physical": "2.2.0", "Microsoft.Extensions.Hosting.Abstractions": "2.2.0", "Microsoft.Extensions.Logging": "2.2.0", "Microsoft.Extensions.Options": "2.2.0", "System.Diagnostics.DiagnosticSource": "4.6.0", "System.Reflection.Metadata": "1.6.0"}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Hosting.dll": {}}}, "Microsoft.AspNetCore.Hosting.Abstractions/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Hosting.Server.Abstractions": "2.2.0", "Microsoft.AspNetCore.Http.Abstractions": "2.2.0", "Microsoft.Extensions.Hosting.Abstractions": "2.2.0"}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Hosting.Abstractions.dll": {}}}, "Microsoft.AspNetCore.Hosting.Server.Abstractions/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Http.Features": "2.2.0", "Microsoft.Extensions.Configuration.Abstractions": "2.2.0"}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Hosting.Server.Abstractions.dll": {}}}, "Microsoft.AspNetCore.Html.Abstractions/2.2.0": {"dependencies": {"System.Text.Encodings.Web": "8.0.0"}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Html.Abstractions.dll": {}}}, "Microsoft.AspNetCore.Http/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Http.Abstractions": "2.2.0", "Microsoft.AspNetCore.WebUtilities": "2.2.0", "Microsoft.Extensions.ObjectPool": "2.2.0", "Microsoft.Extensions.Options": "2.2.0", "Microsoft.Net.Http.Headers": "2.2.0"}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Http.dll": {}}}, "Microsoft.AspNetCore.Http.Abstractions/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Http.Features": "2.2.0", "System.Text.Encodings.Web": "8.0.0"}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Http.Abstractions.dll": {}}}, "Microsoft.AspNetCore.Http.Extensions/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Http.Abstractions": "2.2.0", "Microsoft.Extensions.FileProviders.Abstractions": "2.2.0", "Microsoft.Net.Http.Headers": "2.2.0", "System.Buffers": "4.5.1"}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Http.Extensions.dll": {}}}, "Microsoft.AspNetCore.Http.Features/2.2.0": {"dependencies": {"Microsoft.Extensions.Primitives": "2.2.0"}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Http.Features.dll": {}}}, "Microsoft.AspNetCore.HttpOverrides/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Http.Extensions": "2.2.0", "Microsoft.Extensions.Logging.Abstractions": "2.2.0", "Microsoft.Extensions.Options": "2.2.0"}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.HttpOverrides.dll": {}}}, "Microsoft.AspNetCore.HttpsPolicy/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Hosting.Abstractions": "2.2.0", "Microsoft.AspNetCore.Http": "2.2.0", "Microsoft.AspNetCore.Http.Extensions": "2.2.0", "Microsoft.Extensions.Configuration.Binder": "2.2.0", "Microsoft.Extensions.Options": "2.2.0"}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.HttpsPolicy.dll": {}}}, "Microsoft.AspNetCore.JsonPatch/2.2.0": {"dependencies": {"Microsoft.CSharp": "4.7.0", "Newtonsoft.Json": "13.0.2"}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.JsonPatch.dll": {}}}, "Microsoft.AspNetCore.Localization/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Http.Extensions": "2.2.0", "Microsoft.Extensions.Localization.Abstractions": "2.2.0", "Microsoft.Extensions.Logging.Abstractions": "2.2.0", "Microsoft.Extensions.Options": "2.2.0"}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Localization.dll": {}}}, "Microsoft.AspNetCore.Mvc/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Mvc.Analyzers": "2.2.0", "Microsoft.AspNetCore.Mvc.ApiExplorer": "2.2.0", "Microsoft.AspNetCore.Mvc.Cors": "2.2.0", "Microsoft.AspNetCore.Mvc.DataAnnotations": "2.2.0", "Microsoft.AspNetCore.Mvc.Formatters.Json": "2.2.0", "Microsoft.AspNetCore.Mvc.Localization": "2.2.0", "Microsoft.AspNetCore.Mvc.Razor.Extensions": "2.2.0", "Microsoft.AspNetCore.Mvc.RazorPages": "2.2.0", "Microsoft.AspNetCore.Mvc.TagHelpers": "2.2.0", "Microsoft.AspNetCore.Mvc.ViewFeatures": "2.2.0", "Microsoft.AspNetCore.Razor.Design": "2.2.0", "Microsoft.Extensions.Caching.Memory": "2.2.0", "Microsoft.Extensions.DependencyInjection": "2.2.0"}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Mvc.dll": {}}}, "Microsoft.AspNetCore.Mvc.Abstractions/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Routing.Abstractions": "2.2.0", "Microsoft.Net.Http.Headers": "2.2.0"}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Mvc.Abstractions.dll": {}}}, "Microsoft.AspNetCore.Mvc.Analyzers/2.2.0": {}, "Microsoft.AspNetCore.Mvc.ApiExplorer/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Mvc.Core": "2.2.0"}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Mvc.ApiExplorer.dll": {}}}, "Microsoft.AspNetCore.Mvc.Core/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Authentication.Core": "2.2.0", "Microsoft.AspNetCore.Authorization.Policy": "2.2.0", "Microsoft.AspNetCore.Hosting.Abstractions": "2.2.0", "Microsoft.AspNetCore.Http": "2.2.0", "Microsoft.AspNetCore.Http.Extensions": "2.2.0", "Microsoft.AspNetCore.Mvc.Abstractions": "2.2.0", "Microsoft.AspNetCore.ResponseCaching.Abstractions": "2.2.0", "Microsoft.AspNetCore.Routing": "2.2.0", "Microsoft.AspNetCore.Routing.Abstractions": "2.2.0", "Microsoft.Extensions.DependencyInjection": "2.2.0", "Microsoft.Extensions.DependencyModel": "2.1.0", "Microsoft.Extensions.FileProviders.Abstractions": "2.2.0", "Microsoft.Extensions.Logging.Abstractions": "2.2.0", "System.Diagnostics.DiagnosticSource": "4.6.0", "System.Threading.Tasks.Extensions": "4.5.4"}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Mvc.Core.dll": {}}}, "Microsoft.AspNetCore.Mvc.Cors/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Cors": "2.2.0", "Microsoft.AspNetCore.Mvc.Core": "2.2.0"}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Mvc.Cors.dll": {}}}, "Microsoft.AspNetCore.Mvc.DataAnnotations/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Mvc.Core": "2.2.0", "Microsoft.Extensions.Localization": "2.2.0", "System.ComponentModel.Annotations": "4.5.0"}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Mvc.DataAnnotations.dll": {}}}, "Microsoft.AspNetCore.Mvc.Formatters.Json/2.2.0": {"dependencies": {"Microsoft.AspNetCore.JsonPatch": "2.2.0", "Microsoft.AspNetCore.Mvc.Core": "2.2.0"}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Mvc.Formatters.Json.dll": {}}}, "Microsoft.AspNetCore.Mvc.Localization/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Localization": "2.2.0", "Microsoft.AspNetCore.Mvc.Razor": "2.2.0", "Microsoft.Extensions.DependencyInjection": "2.2.0", "Microsoft.Extensions.Localization": "2.2.0"}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Mvc.Localization.dll": {}}}, "Microsoft.AspNetCore.Mvc.Razor/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Mvc.Razor.Extensions": "2.2.0", "Microsoft.AspNetCore.Mvc.ViewFeatures": "2.2.0", "Microsoft.AspNetCore.Razor.Runtime": "2.2.0", "Microsoft.CodeAnalysis.CSharp": "2.8.0", "Microsoft.CodeAnalysis.Razor": "2.2.0", "Microsoft.DiaSymReader.Native": "1.7.0", "Microsoft.Extensions.Caching.Memory": "2.2.0", "Microsoft.Extensions.FileProviders.Composite": "2.2.0"}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Mvc.Razor.dll": {}}}, "Microsoft.AspNetCore.Mvc.Razor.Extensions/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Razor.Language": "2.2.0", "Microsoft.CodeAnalysis.Razor": "2.2.0"}, "runtime": {"lib/net46/Microsoft.AspNetCore.Mvc.Razor.Extensions.dll": {}}}, "Microsoft.AspNetCore.Mvc.RazorPages/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Mvc.Razor": "2.2.0"}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Mvc.RazorPages.dll": {}}}, "Microsoft.AspNetCore.Mvc.TagHelpers/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Mvc.Razor": "2.2.0", "Microsoft.AspNetCore.Razor.Runtime": "2.2.0", "Microsoft.AspNetCore.Routing.Abstractions": "2.2.0", "Microsoft.Extensions.Caching.Memory": "2.2.0", "Microsoft.Extensions.FileSystemGlobbing": "2.2.0", "Microsoft.Extensions.Primitives": "2.2.0"}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Mvc.TagHelpers.dll": {}}}, "Microsoft.AspNetCore.Mvc.ViewFeatures/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Antiforgery": "2.2.0", "Microsoft.AspNetCore.Diagnostics.Abstractions": "2.2.0", "Microsoft.AspNetCore.Html.Abstractions": "2.2.0", "Microsoft.AspNetCore.Mvc.Core": "2.2.0", "Microsoft.AspNetCore.Mvc.DataAnnotations": "2.2.0", "Microsoft.AspNetCore.Mvc.Formatters.Json": "2.2.0", "Microsoft.Extensions.WebEncoders": "2.2.0", "Newtonsoft.Json.Bson": "1.0.1"}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Mvc.ViewFeatures.dll": {}}}, "Microsoft.AspNetCore.Razor/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Html.Abstractions": "2.2.0"}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Razor.dll": {}}}, "Microsoft.AspNetCore.Razor.Design/2.2.0": {}, "Microsoft.AspNetCore.Razor.Language/2.2.0": {"runtime": {"lib/net46/Microsoft.AspNetCore.Razor.Language.dll": {}}}, "Microsoft.AspNetCore.Razor.Runtime/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Html.Abstractions": "2.2.0", "Microsoft.AspNetCore.Razor": "2.2.0"}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Razor.Runtime.dll": {}}}, "Microsoft.AspNetCore.ResponseCaching.Abstractions/2.2.0": {"dependencies": {"Microsoft.Extensions.Primitives": "2.2.0"}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.ResponseCaching.Abstractions.dll": {}}}, "Microsoft.AspNetCore.Routing/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Http.Extensions": "2.2.0", "Microsoft.AspNetCore.Routing.Abstractions": "2.2.0", "Microsoft.Extensions.Logging.Abstractions": "2.2.0", "Microsoft.Extensions.ObjectPool": "2.2.0", "Microsoft.Extensions.Options": "2.2.0"}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Routing.dll": {}}}, "Microsoft.AspNetCore.Routing.Abstractions/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Http.Abstractions": "2.2.0"}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Routing.Abstractions.dll": {}}}, "Microsoft.AspNetCore.Server.IIS/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Authentication.Core": "2.2.0", "Microsoft.AspNetCore.Connections.Abstractions": "2.2.0", "Microsoft.AspNetCore.Hosting.Abstractions": "2.2.0", "System.IO.Pipelines": "4.5.2", "System.Security.Principal.Windows": "4.5.0"}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Server.IIS.dll": {}}}, "Microsoft.AspNetCore.Server.IISIntegration/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Authentication.Core": "2.2.0", "Microsoft.AspNetCore.Hosting.Abstractions": "2.2.0", "Microsoft.AspNetCore.Http": "2.2.0", "Microsoft.AspNetCore.Http.Extensions": "2.2.0", "Microsoft.AspNetCore.HttpOverrides": "2.2.0", "Microsoft.Extensions.Logging.Abstractions": "2.2.0", "Microsoft.Extensions.Options": "2.2.0", "System.Buffers": "4.5.1", "System.IO.Pipelines": "4.5.2", "System.Memory": "4.5.5", "System.Numerics.Vectors": "4.5.0", "System.Runtime.CompilerServices.Unsafe": "6.0.0", "System.Security.Principal.Windows": "4.5.0"}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Server.IISIntegration.dll": {}}}, "Microsoft.AspNetCore.Server.Kestrel/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Hosting": "2.2.0", "Microsoft.AspNetCore.Server.Kestrel.Core": "2.2.0", "Microsoft.AspNetCore.Server.Kestrel.Https": "2.2.0", "Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets": "2.2.0"}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Server.Kestrel.dll": {}}}, "Microsoft.AspNetCore.Server.Kestrel.Core/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Hosting.Abstractions": "2.2.0", "Microsoft.AspNetCore.Http": "2.2.0", "Microsoft.AspNetCore.Server.Kestrel.Transport.Abstractions": "2.2.0", "Microsoft.AspNetCore.WebUtilities": "2.2.0", "Microsoft.Extensions.Configuration.Binder": "2.2.0", "Microsoft.Extensions.Logging.Abstractions": "2.2.0", "Microsoft.Extensions.Options": "2.2.0", "Microsoft.Net.Http.Headers": "2.2.0", "System.Memory": "4.5.5", "System.Numerics.Vectors": "4.5.0", "System.Runtime.CompilerServices.Unsafe": "6.0.0", "System.Security.Cryptography.Cng": "4.5.0", "System.Threading.Tasks.Extensions": "4.5.4"}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Server.Kestrel.Core.dll": {}}}, "Microsoft.AspNetCore.Server.Kestrel.Https/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Http.Abstractions": "2.2.0", "Microsoft.AspNetCore.Server.Kestrel.Core": "2.2.0"}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Server.Kestrel.Https.dll": {}}}, "Microsoft.AspNetCore.Server.Kestrel.Transport.Abstractions/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Connections.Abstractions": "2.2.0"}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Server.Kestrel.Transport.Abstractions.dll": {}}}, "Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Hosting.Abstractions": "2.2.0", "Microsoft.AspNetCore.Server.Kestrel.Transport.Abstractions": "2.2.0", "Microsoft.Extensions.Options": "2.2.0"}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.dll": {}}}, "Microsoft.AspNetCore.WebUtilities/2.2.0": {"dependencies": {"Microsoft.Net.Http.Headers": "2.2.0", "System.Text.Encodings.Web": "8.0.0"}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.WebUtilities.dll": {}}}, "Microsoft.Bcl.AsyncInterfaces/8.0.0": {"dependencies": {"System.Threading.Tasks.Extensions": "4.5.4"}, "runtime": {"lib/net462/Microsoft.Bcl.AsyncInterfaces.dll": {}}}, "Microsoft.CodeAnalysis.Analyzers/1.1.0": {}, "Microsoft.CodeAnalysis.Common/2.8.0": {"dependencies": {"Microsoft.CodeAnalysis.Analyzers": "1.1.0", "System.AppContext": "4.3.0", "System.Collections": "4.3.0", "System.Collections.Concurrent": "4.3.0", "System.Collections.Immutable": "1.7.0", "System.Console": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Diagnostics.FileVersionInfo": "4.3.0", "System.Diagnostics.StackTrace": "4.3.0", "System.Diagnostics.Tools": "4.3.0", "System.Dynamic.Runtime": "4.3.0", "System.Globalization": "4.3.0", "System.IO.Compression": "4.3.0", "System.IO.FileSystem": "4.3.0", "System.IO.FileSystem.Primitives": "4.3.0", "System.Linq": "4.3.0", "System.Linq.Expressions": "4.3.0", "System.Reflection": "4.3.0", "System.Reflection.Metadata": "1.6.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Runtime.Numerics": "4.3.0", "System.Security.Cryptography.Algorithms": "4.3.0", "System.Security.Cryptography.Encoding": "4.3.0", "System.Security.Cryptography.X509Certificates": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Text.Encoding.CodePages": "4.3.0", "System.Text.Encoding.Extensions": "4.3.0", "System.Threading": "4.3.0", "System.Threading.Tasks": "4.3.0", "System.Threading.Tasks.Parallel": "4.3.0", "System.Threading.Thread": "4.3.0", "System.ValueTuple": "4.5.0", "System.Xml.ReaderWriter": "4.3.0", "System.Xml.XDocument": "4.3.0", "System.Xml.XPath.XDocument": "4.3.0", "System.Xml.XmlDocument": "4.3.0"}, "runtime": {"lib/netstandard1.3/Microsoft.CodeAnalysis.dll": {}}}, "Microsoft.CodeAnalysis.CSharp/2.8.0": {"dependencies": {"Microsoft.CodeAnalysis.Common": "2.8.0"}, "runtime": {"lib/netstandard1.3/Microsoft.CodeAnalysis.CSharp.dll": {}}}, "Microsoft.CodeAnalysis.Razor/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Razor.Language": "2.2.0", "Microsoft.CodeAnalysis.CSharp": "2.8.0", "Microsoft.CodeAnalysis.Common": "2.8.0", "System.Runtime.InteropServices.RuntimeInformation": "4.3.0"}, "runtime": {"lib/net46/Microsoft.CodeAnalysis.Razor.dll": {}}}, "Microsoft.CSharp/4.7.0": {}, "Microsoft.DiaSymReader.Native/1.7.0": {"native": {"runtimes/win-x86/native/Microsoft.DiaSymReader.Native.x86.dll": {}}}, "Microsoft.DotNet.PlatformAbstractions/2.1.0": {"dependencies": {"System.Runtime.InteropServices.RuntimeInformation": "4.3.0"}, "runtime": {"lib/net45/Microsoft.DotNet.PlatformAbstractions.dll": {}}}, "Microsoft.Extensions.Caching.Abstractions/2.2.0": {"dependencies": {"Microsoft.Extensions.Primitives": "2.2.0"}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Caching.Abstractions.dll": {}}}, "Microsoft.Extensions.Caching.Memory/2.2.0": {"dependencies": {"Microsoft.Extensions.Caching.Abstractions": "2.2.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "2.2.0", "Microsoft.Extensions.Options": "2.2.0"}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Caching.Memory.dll": {}}}, "Microsoft.Extensions.Configuration/2.2.0": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "2.2.0"}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Configuration.dll": {}}}, "Microsoft.Extensions.Configuration.Abstractions/2.2.0": {"dependencies": {"Microsoft.Extensions.Primitives": "2.2.0"}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Configuration.Abstractions.dll": {}}}, "Microsoft.Extensions.Configuration.Binder/2.2.0": {"dependencies": {"Microsoft.Extensions.Configuration": "2.2.0"}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Configuration.Binder.dll": {}}}, "Microsoft.Extensions.Configuration.CommandLine/2.2.0": {"dependencies": {"Microsoft.Extensions.Configuration": "2.2.0"}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Configuration.CommandLine.dll": {}}}, "Microsoft.Extensions.Configuration.EnvironmentVariables/2.2.0": {"dependencies": {"Microsoft.Extensions.Configuration": "2.2.0"}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Configuration.EnvironmentVariables.dll": {}}}, "Microsoft.Extensions.Configuration.FileExtensions/2.2.0": {"dependencies": {"Microsoft.Extensions.Configuration": "2.2.0", "Microsoft.Extensions.FileProviders.Physical": "2.2.0"}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Configuration.FileExtensions.dll": {}}}, "Microsoft.Extensions.Configuration.Json/2.2.0": {"dependencies": {"Microsoft.Extensions.Configuration": "2.2.0", "Microsoft.Extensions.Configuration.FileExtensions": "2.2.0", "Newtonsoft.Json": "13.0.2"}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Configuration.Json.dll": {}}}, "Microsoft.Extensions.Configuration.UserSecrets/2.2.0": {"dependencies": {"Microsoft.Extensions.Configuration.Json": "2.2.0"}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Configuration.UserSecrets.dll": {}}}, "Microsoft.Extensions.DependencyInjection/2.2.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "2.2.0"}, "runtime": {"lib/net461/Microsoft.Extensions.DependencyInjection.dll": {}}}, "Microsoft.Extensions.DependencyInjection.Abstractions/2.2.0": {"runtime": {"lib/netstandard2.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll": {}}}, "Microsoft.Extensions.DependencyModel/2.1.0": {"dependencies": {"Microsoft.DotNet.PlatformAbstractions": "2.1.0", "Newtonsoft.Json": "13.0.2"}, "runtime": {"lib/net451/Microsoft.Extensions.DependencyModel.dll": {}}}, "Microsoft.Extensions.FileProviders.Abstractions/2.2.0": {"dependencies": {"Microsoft.Extensions.Primitives": "2.2.0"}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.FileProviders.Abstractions.dll": {}}}, "Microsoft.Extensions.FileProviders.Composite/2.2.0": {"dependencies": {"Microsoft.Extensions.FileProviders.Abstractions": "2.2.0"}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.FileProviders.Composite.dll": {}}}, "Microsoft.Extensions.FileProviders.Physical/2.2.0": {"dependencies": {"Microsoft.Extensions.FileProviders.Abstractions": "2.2.0", "Microsoft.Extensions.FileSystemGlobbing": "2.2.0"}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.FileProviders.Physical.dll": {}}}, "Microsoft.Extensions.FileSystemGlobbing/2.2.0": {"runtime": {"lib/netstandard2.0/Microsoft.Extensions.FileSystemGlobbing.dll": {}}}, "Microsoft.Extensions.Hosting.Abstractions/2.2.0": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "2.2.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "2.2.0", "Microsoft.Extensions.FileProviders.Abstractions": "2.2.0", "Microsoft.Extensions.Logging.Abstractions": "2.2.0"}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Hosting.Abstractions.dll": {}}}, "Microsoft.Extensions.Localization/2.2.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "2.2.0", "Microsoft.Extensions.Localization.Abstractions": "2.2.0", "Microsoft.Extensions.Logging.Abstractions": "2.2.0", "Microsoft.Extensions.Options": "2.2.0"}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Localization.dll": {}}}, "Microsoft.Extensions.Localization.Abstractions/2.2.0": {"runtime": {"lib/netstandard2.0/Microsoft.Extensions.Localization.Abstractions.dll": {}}}, "Microsoft.Extensions.Logging/2.2.0": {"dependencies": {"Microsoft.Extensions.Configuration.Binder": "2.2.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "2.2.0", "Microsoft.Extensions.Logging.Abstractions": "2.2.0", "Microsoft.Extensions.Options": "2.2.0"}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Logging.dll": {}}}, "Microsoft.Extensions.Logging.Abstractions/2.2.0": {"runtime": {"lib/netstandard2.0/Microsoft.Extensions.Logging.Abstractions.dll": {}}}, "Microsoft.Extensions.Logging.Configuration/2.2.0": {"dependencies": {"Microsoft.Extensions.Logging": "2.2.0", "Microsoft.Extensions.Options.ConfigurationExtensions": "2.2.0"}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Logging.Configuration.dll": {}}}, "Microsoft.Extensions.Logging.Console/2.2.0": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "2.2.0", "Microsoft.Extensions.Logging": "2.2.0", "Microsoft.Extensions.Logging.Configuration": "2.2.0"}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Logging.Console.dll": {}}}, "Microsoft.Extensions.Logging.Debug/2.2.0": {"dependencies": {"Microsoft.Extensions.Logging": "2.2.0"}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Logging.Debug.dll": {}}}, "Microsoft.Extensions.Logging.EventSource/2.2.0": {"dependencies": {"Microsoft.Extensions.Logging": "2.2.0", "Newtonsoft.Json": "13.0.2"}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Logging.EventSource.dll": {}}}, "Microsoft.Extensions.ObjectPool/2.2.0": {"runtime": {"lib/netstandard2.0/Microsoft.Extensions.ObjectPool.dll": {}}}, "Microsoft.Extensions.Options/2.2.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "2.2.0", "Microsoft.Extensions.Primitives": "2.2.0", "System.ComponentModel.Annotations": "4.5.0"}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Options.dll": {}}}, "Microsoft.Extensions.Options.ConfigurationExtensions/2.2.0": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "2.2.0", "Microsoft.Extensions.Configuration.Binder": "2.2.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "2.2.0", "Microsoft.Extensions.Options": "2.2.0"}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Options.ConfigurationExtensions.dll": {}}}, "Microsoft.Extensions.Primitives/2.2.0": {"dependencies": {"System.Memory": "4.5.5", "System.Runtime.CompilerServices.Unsafe": "6.0.0"}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Primitives.dll": {}}}, "Microsoft.Extensions.WebEncoders/2.2.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "2.2.0", "Microsoft.Extensions.Options": "2.2.0", "System.Text.Encodings.Web": "8.0.0"}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.WebEncoders.dll": {}}}, "Microsoft.Graph/3.1.0": {"dependencies": {"Microsoft.Graph.Core": "1.20.1"}, "runtime": {"lib/net461/Microsoft.Graph.dll": {}}}, "Microsoft.Graph.Communications.Calls/1.2.0.850": {"dependencies": {"Microsoft.Graph.Communications.Client": "1.2.0.850", "Microsoft.Graph.Communications.Core": "1.2.0.850"}, "runtime": {"lib/net461/Microsoft.Graph.Communications.Calls.dll": {}}}, "Microsoft.Graph.Communications.Calls.Media/1.2.0.850": {"dependencies": {"Microsoft.Graph.Communications.Calls": "1.2.0.850", "Microsoft.Skype.Bots.Media": "*********-alpha"}, "runtime": {"lib/net472/Microsoft.Graph.Communications.Calls.Media.dll": {}}}, "Microsoft.Graph.Communications.Client/1.2.0.850": {"dependencies": {"Microsoft.Graph.Communications.Core": "1.2.0.850", "System.Threading.Tasks.Dataflow": "4.9.0"}, "runtime": {"lib/net461/Microsoft.Graph.Communications.Client.dll": {}}}, "Microsoft.Graph.Communications.Common/1.2.0.850": {"dependencies": {"Microsoft.Extensions.Logging.Abstractions": "2.2.0", "Newtonsoft.Json": "13.0.2"}, "runtime": {"lib/net461/Microsoft.Graph.Communications.Common.dll": {}}}, "Microsoft.Graph.Communications.Core/1.2.0.850": {"dependencies": {"Microsoft.Graph": "3.1.0", "Microsoft.Graph.Communications.Common": "1.2.0.850", "Microsoft.Graph.Core": "1.20.1", "Newtonsoft.Json": "13.0.2"}, "runtime": {"lib/net461/Microsoft.Graph.Communications.Core.dll": {}}}, "Microsoft.Graph.Core/1.20.1": {"dependencies": {"Newtonsoft.Json": "13.0.2", "System.Diagnostics.DiagnosticSource": "4.6.0", "System.ValueTuple": "4.5.0"}, "runtime": {"lib/net45/Microsoft.Graph.Core.dll": {}}}, "Microsoft.IdentityModel.Abstractions/8.0.1": {"runtime": {"lib/net472/Microsoft.IdentityModel.Abstractions.dll": {}}}, "Microsoft.IdentityModel.Clients.ActiveDirectory/5.2.4": {"dependencies": {"System.Net.Http": "4.3.4"}, "runtime": {"lib/net45/Microsoft.IdentityModel.Clients.ActiveDirectory.dll": {}}}, "Microsoft.IdentityModel.JsonWebTokens/8.0.1": {"dependencies": {"Microsoft.IdentityModel.Tokens": "8.0.1"}, "runtime": {"lib/net472/Microsoft.IdentityModel.JsonWebTokens.dll": {}}}, "Microsoft.IdentityModel.Logging/8.0.1": {"dependencies": {"Microsoft.IdentityModel.Abstractions": "8.0.1"}, "runtime": {"lib/net472/Microsoft.IdentityModel.Logging.dll": {}}}, "Microsoft.IdentityModel.Protocols/8.0.1": {"dependencies": {"Microsoft.IdentityModel.Tokens": "8.0.1"}, "runtime": {"lib/net472/Microsoft.IdentityModel.Protocols.dll": {}}}, "Microsoft.IdentityModel.Protocols.OpenIdConnect/8.0.1": {"dependencies": {"Microsoft.IdentityModel.Protocols": "8.0.1", "System.IdentityModel.Tokens.Jwt": "8.0.1", "System.Text.Json": "8.0.4"}, "runtime": {"lib/net472/Microsoft.IdentityModel.Protocols.OpenIdConnect.dll": {}}}, "Microsoft.IdentityModel.Tokens/8.0.1": {"dependencies": {"Microsoft.IdentityModel.Logging": "8.0.1", "System.Memory": "4.5.5", "System.Text.Json": "8.0.4"}, "runtime": {"lib/net472/Microsoft.IdentityModel.Tokens.dll": {}}}, "Microsoft.Net.Http.Headers/2.2.0": {"dependencies": {"Microsoft.Extensions.Primitives": "2.2.0", "System.Buffers": "4.5.1"}, "runtime": {"lib/netstandard2.0/Microsoft.Net.Http.Headers.dll": {}}}, "Microsoft.Psi.Audio/**********-beta": {"dependencies": {"Microsoft.Psi.Data": "**********-beta", "Microsoft.Psi.Runtime": "**********-beta"}, "runtime": {"lib/netstandard2.0/Microsoft.Psi.Audio.dll": {}}}, "Microsoft.Psi.Data/**********-beta": {"dependencies": {"Microsoft.Psi.Runtime": "**********-beta", "Newtonsoft.Json": "13.0.2"}, "runtime": {"lib/netstandard2.0/Microsoft.Psi.Data.dll": {}}}, "Microsoft.Psi.Imaging/*********-beta": {"dependencies": {"Microsoft.Psi.Runtime": "**********-beta", "System.Drawing.Common": "4.7.2"}, "runtime": {"lib/netstandard2.0/Microsoft.Psi.Imaging.dll": {}}}, "Microsoft.Psi.Imaging.Windows/*********-beta": {"dependencies": {"Microsoft.Psi.Imaging": "*********-beta", "Microsoft.Psi.Runtime": "**********-beta"}, "runtime": {"lib/net472/Microsoft.Psi.Imaging.Windows.dll": {}}}, "Microsoft.Psi.Runtime/**********-beta": {"dependencies": {"Microsoft.CSharp": "4.7.0", "System.Collections.Immutable": "1.7.0", "System.Numerics.Vectors": "4.5.0", "System.Reflection.Emit.ILGeneration": "4.7.0", "System.Reflection.Emit.Lightweight": "4.7.0"}, "runtime": {"lib/netstandard2.0/Microsoft.Psi.IL.dll": {}, "lib/netstandard2.0/Microsoft.Psi.dll": {}}}, "Microsoft.Skype.Bots.Media/*********-alpha": {"dependencies": {"Newtonsoft.Json": "13.0.2", "System.Threading.Tasks.Dataflow": "4.9.0"}, "runtime": {"lib/net472/Microsoft.Skype.Bots.Media.dll": {}}}, "Microsoft.Win32.Registry/4.5.0": {"dependencies": {"System.Security.AccessControl": "4.5.0", "System.Security.Principal.Windows": "4.5.0"}, "runtime": {"runtimes/win/lib/net461/Microsoft.Win32.Registry.dll": {}}}, "Newtonsoft.Json/13.0.2": {"runtime": {"lib/net45/Newtonsoft.Json.dll": {}}}, "Newtonsoft.Json.Bson/1.0.1": {"dependencies": {"Newtonsoft.Json": "13.0.2"}, "runtime": {"lib/net45/Newtonsoft.Json.Bson.dll": {}}}, "System.AppContext/4.3.0": {}, "System.Buffers/4.5.1": {"runtime": {"lib/net461/System.Buffers.dll": {}}}, "System.Collections/4.3.0": {}, "System.Collections.Concurrent/4.3.0": {}, "System.Collections.Immutable/1.7.0": {"dependencies": {"System.Memory": "4.5.5"}, "runtime": {"lib/netstandard2.0/System.Collections.Immutable.dll": {}}}, "System.ComponentModel.Annotations/4.5.0": {"runtime": {"lib/net461/System.ComponentModel.Annotations.dll": {}}}, "System.Console/4.3.0": {}, "System.Diagnostics.Debug/4.3.0": {}, "System.Diagnostics.DiagnosticSource/4.6.0": {"dependencies": {"System.Memory": "4.5.5"}, "runtime": {"lib/net46/System.Diagnostics.DiagnosticSource.dll": {}}}, "System.Diagnostics.FileVersionInfo/4.3.0": {}, "System.Diagnostics.StackTrace/4.3.0": {}, "System.Diagnostics.Tools/4.3.0": {}, "System.Drawing.Common/4.7.2": {"runtime": {"lib/net461/System.Drawing.Common.dll": {}}}, "System.Dynamic.Runtime/4.3.0": {}, "System.Globalization/4.3.0": {}, "System.IdentityModel.Tokens.Jwt/8.0.1": {"dependencies": {"Microsoft.IdentityModel.JsonWebTokens": "8.0.1", "Microsoft.IdentityModel.Tokens": "8.0.1"}, "runtime": {"lib/net472/System.IdentityModel.Tokens.Jwt.dll": {}}}, "System.IO/4.3.0": {}, "System.IO.Compression/4.3.0": {}, "System.IO.FileSystem/4.3.0": {"dependencies": {"System.IO.FileSystem.Primitives": "4.3.0"}}, "System.IO.FileSystem.Primitives/4.3.0": {}, "System.IO.Pipelines/4.5.2": {"dependencies": {"System.Buffers": "4.5.1", "System.Memory": "4.5.5", "System.Threading.Tasks.Extensions": "4.5.4"}, "runtime": {"lib/netstandard2.0/System.IO.Pipelines.dll": {}}}, "System.Linq/4.3.0": {}, "System.Linq.Expressions/4.3.0": {}, "System.Memory/4.5.5": {"dependencies": {"System.Buffers": "4.5.1", "System.Numerics.Vectors": "4.5.0", "System.Runtime.CompilerServices.Unsafe": "6.0.0"}, "runtime": {"lib/net461/System.Memory.dll": {}}}, "System.Net.Http/4.3.4": {"dependencies": {"System.Security.Cryptography.X509Certificates": "4.3.0"}}, "System.Numerics.Vectors/4.5.0": {"runtime": {"lib/net46/System.Numerics.Vectors.dll": {}}}, "System.Reflection/4.3.0": {}, "System.Reflection.Emit.ILGeneration/4.7.0": {}, "System.Reflection.Emit.Lightweight/4.7.0": {}, "System.Reflection.Metadata/1.6.0": {"dependencies": {"System.Collections.Immutable": "1.7.0"}, "runtime": {"lib/netstandard2.0/System.Reflection.Metadata.dll": {}}}, "System.Resources.ResourceManager/4.3.0": {}, "System.Runtime/4.3.0": {}, "System.Runtime.CompilerServices.Unsafe/6.0.0": {"runtime": {"lib/net461/System.Runtime.CompilerServices.Unsafe.dll": {}}}, "System.Runtime.Extensions/4.3.0": {}, "System.Runtime.InteropServices/4.3.0": {"dependencies": {"System.Runtime": "4.3.0"}}, "System.Runtime.InteropServices.RuntimeInformation/4.3.0": {}, "System.Runtime.Numerics/4.3.0": {}, "System.Security.AccessControl/4.5.0": {"dependencies": {"System.Security.Principal.Windows": "4.5.0"}, "runtime": {"runtimes/win/lib/net461/System.Security.AccessControl.dll": {}}}, "System.Security.Cryptography.Algorithms/4.3.0": {"dependencies": {"System.IO": "4.3.0", "System.Runtime": "4.3.0", "System.Security.Cryptography.Encoding": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0"}}, "System.Security.Cryptography.Cng/4.5.0": {"runtime": {"runtimes/win/lib/net47/System.Security.Cryptography.Cng.dll": {}}}, "System.Security.Cryptography.Encoding/4.3.0": {}, "System.Security.Cryptography.Primitives/4.3.0": {}, "System.Security.Cryptography.X509Certificates/4.3.0": {"dependencies": {"System.Security.Cryptography.Algorithms": "4.3.0", "System.Security.Cryptography.Encoding": "4.3.0"}}, "System.Security.Cryptography.Xml/4.5.0": {"dependencies": {"System.Security.Permissions": "4.5.0"}, "runtime": {"lib/net461/System.Security.Cryptography.Xml.dll": {}}}, "System.Security.Permissions/4.5.0": {"dependencies": {"System.Security.AccessControl": "4.5.0"}, "runtime": {"lib/net461/System.Security.Permissions.dll": {}}}, "System.Security.Principal.Windows/4.5.0": {"runtime": {"runtimes/win/lib/net461/System.Security.Principal.Windows.dll": {}}}, "System.Text.Encoding/4.3.0": {}, "System.Text.Encoding.CodePages/4.3.0": {"runtime": {"runtimes/win/lib/netstandard1.3/System.Text.Encoding.CodePages.dll": {}}}, "System.Text.Encoding.Extensions/4.3.0": {}, "System.Text.Encodings.Web/8.0.0": {"dependencies": {"System.Buffers": "4.5.1", "System.Memory": "4.5.5", "System.Runtime.CompilerServices.Unsafe": "6.0.0"}, "runtime": {"lib/net462/System.Text.Encodings.Web.dll": {}}}, "System.Text.Json/8.0.4": {"dependencies": {"Microsoft.Bcl.AsyncInterfaces": "8.0.0", "System.Buffers": "4.5.1", "System.Memory": "4.5.5", "System.Runtime.CompilerServices.Unsafe": "6.0.0", "System.Text.Encodings.Web": "8.0.0", "System.Threading.Tasks.Extensions": "4.5.4", "System.ValueTuple": "4.5.0"}, "runtime": {"lib/net462/System.Text.Json.dll": {}}}, "System.Threading/4.3.0": {}, "System.Threading.Tasks/4.3.0": {}, "System.Threading.Tasks.Dataflow/4.9.0": {"runtime": {"lib/netstandard2.0/System.Threading.Tasks.Dataflow.dll": {}}}, "System.Threading.Tasks.Extensions/4.5.4": {"dependencies": {"System.Runtime.CompilerServices.Unsafe": "6.0.0"}, "runtime": {"lib/net461/System.Threading.Tasks.Extensions.dll": {}}}, "System.Threading.Tasks.Parallel/4.3.0": {}, "System.Threading.Thread/4.3.0": {}, "System.ValueTuple/4.5.0": {"runtime": {"lib/net47/System.ValueTuple.dll": {}}}, "System.Xml.ReaderWriter/4.3.0": {}, "System.Xml.XDocument/4.3.0": {}, "System.Xml.XmlDocument/4.3.0": {}, "System.Xml.XPath/4.3.0": {}, "System.Xml.XPath.XDocument/4.3.0": {"dependencies": {"System.Xml.XPath": "4.3.0"}}}}, "libraries": {"AccureMD.MediaBot.Worker/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-Bs75iht4lXS8uVWy/Cbsr9i0m2jRtnrfPEWU+6t0dQTZcJEfF9b7G2F7XvstLFWkAKSgYRzFkAwi/KypY0Qtew==", "path": "microsoft.aspnetcore/2.2.0", "hashPath": "microsoft.aspnetcore.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Antiforgery/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-fVQsSXNZz38Ysx8iKwwqfOLHhLrAeKEMBS5Ia3Lh7BJjOC2vPV28/yk08AovOMsB3SNQPGnE7bv+lsIBTmAkvw==", "path": "microsoft.aspnetcore.antiforgery/2.2.0", "hashPath": "microsoft.aspnetcore.antiforgery.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Authentication.Abstractions/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-VloMLDJMf3n/9ic5lCBOa42IBYJgyB1JhzLsL68Zqg+2bEPWfGBj/xCJy/LrKTArN0coOcZp3wyVTZlx0y9pHQ==", "path": "microsoft.aspnetcore.authentication.abstractions/2.2.0", "hashPath": "microsoft.aspnetcore.authentication.abstractions.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Authentication.Core/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-XlVJzJ5wPOYW+Y0J6Q/LVTEyfS4ssLXmt60T0SPP+D8abVhBTl+cgw2gDHlyKYIkcJg7btMVh383NDkMVqD/fg==", "path": "microsoft.aspnetcore.authentication.core/2.2.0", "hashPath": "microsoft.aspnetcore.authentication.core.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Authorization/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-/L0W8H3jMYWyaeA9gBJqS/tSWBegP9aaTM0mjRhxTttBY9z4RVDRYJ2CwPAmAXIuPr3r1sOw+CS8jFVRGHRezQ==", "path": "microsoft.aspnetcore.authorization/2.2.0", "hashPath": "microsoft.aspnetcore.authorization.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Authorization.Policy/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-aJCo6niDRKuNg2uS2WMEmhJTooQUGARhV2ENQ2tO5443zVHUo19MSgrgGo9FIrfD+4yKPF8Q+FF33WkWfPbyKw==", "path": "microsoft.aspnetcore.authorization.policy/2.2.0", "hashPath": "microsoft.aspnetcore.authorization.policy.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Connections.Abstractions/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-Aqr/16Cu5XmGv7mLKJvXRxhhd05UJ7cTTSaUV4MZ3ynAzfgWjsAdpIU8FWuxwAjmVdmI8oOWuVDrbs+sRkhKnA==", "path": "microsoft.aspnetcore.connections.abstractions/2.2.0", "hashPath": "microsoft.aspnetcore.connections.abstractions.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Cors/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-LFlTM3ThS3ZCILuKnjy8HyK9/IlDh3opogdbCVx6tMGyDzTQBgMPXLjGDLtMk5QmLDCcP3l1TO3z/+1viA8GUg==", "path": "microsoft.aspnetcore.cors/2.2.0", "hashPath": "microsoft.aspnetcore.cors.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Cryptography.Internal/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-GXmMD8/vuTLPLvKzKEPz/4vapC5e0cwx1tUVd83ePRyWF9CCrn/pg4/1I+tGkQqFLPvi3nlI2QtPtC6MQN8Nww==", "path": "microsoft.aspnetcore.cryptography.internal/2.2.0", "hashPath": "microsoft.aspnetcore.cryptography.internal.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.DataProtection/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-G6dvu5Nd2vjpYbzazZ//qBFbSEf2wmBUbyAR7E4AwO3gWjhoJD5YxpThcGJb7oE3VUcW65SVMXT+cPCiiBg8Sg==", "path": "microsoft.aspnetcore.dataprotection/2.2.0", "hashPath": "microsoft.aspnetcore.dataprotection.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.DataProtection.Abstractions/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-seANFXmp8mb5Y12m1ShiElJ3ZdOT3mBN3wA1GPhHJIvZ/BxOCPyqEOR+810OWsxEZwA5r5fDRNpG/CqiJmQnJg==", "path": "microsoft.aspnetcore.dataprotection.abstractions/2.2.0", "hashPath": "microsoft.aspnetcore.dataprotection.abstractions.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Diagnostics/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-RobNuZecn/eefWVApOE+OWAZXCdgfzm8pB7tBvJkahsjWfn1a+bLM9I2cuKlp/9aFBok1O/oDXlgYSvaQYu/yg==", "path": "microsoft.aspnetcore.diagnostics/2.2.0", "hashPath": "microsoft.aspnetcore.diagnostics.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Diagnostics.Abstractions/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-pva9ggfUDtnJIKzv0+wxwTX7LduDx6xLSpMqWwdOJkW52L0t31PI78+v+WqqMpUtMzcKug24jGs3nTFpAmA/2g==", "path": "microsoft.aspnetcore.diagnostics.abstractions/2.2.0", "hashPath": "microsoft.aspnetcore.diagnostics.abstractions.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.HostFiltering/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-JSX6ZlVWDkokZ+xCKDhUVQNqbmFn1lHQNzJc8K4Y/uTUocZS83+b/8Q7y/yx3oJ362etGMVy0keAvmCdqbP8nA==", "path": "microsoft.aspnetcore.hostfiltering/2.2.0", "hashPath": "microsoft.aspnetcore.hostfiltering.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Hosting/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-7t4RbUGugpHtQmzAkc9fpDdYJg6t/jcB2VVnjensVYbZFnLDU8pNrG0hrekk1DQG7P2UzpSqKLzDsFF0/lkkbw==", "path": "microsoft.aspnetcore.hosting/2.2.0", "hashPath": "microsoft.aspnetcore.hosting.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Hosting.Abstractions/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-ubycklv+ZY7Kutdwuy1W4upWcZ6VFR8WUXU7l7B2+mvbDBBPAcfpi+E+Y5GFe+Q157YfA3C49D2GCjAZc7Mobw==", "path": "microsoft.aspnetcore.hosting.abstractions/2.2.0", "hashPath": "microsoft.aspnetcore.hosting.abstractions.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Hosting.Server.Abstractions/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-1PMijw8RMtuQF60SsD/JlKtVfvh4NORAhF4wjysdABhlhTrYmtgssqyncR0Stq5vqtjplZcj6kbT4LRTglt9IQ==", "path": "microsoft.aspnetcore.hosting.server.abstractions/2.2.0", "hashPath": "microsoft.aspnetcore.hosting.server.abstractions.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Html.Abstractions/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-Y4rs5aMEXY8G7wJo5S3EEt6ltqyOTr/qOeZzfn+hw/fuQj5GppGckMY5psGLETo1U9hcT5MmAhaT5xtusM1b5g==", "path": "microsoft.aspnetcore.html.abstractions/2.2.0", "hashPath": "microsoft.aspnetcore.html.abstractions.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Http/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-YogBSMotWPAS/X5967pZ+yyWPQkThxhmzAwyCHCSSldzYBkW5W5d6oPfBaPqQOnSHYTpSOSOkpZoAce0vwb6+A==", "path": "microsoft.aspnetcore.http/2.2.0", "hashPath": "microsoft.aspnetcore.http.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Http.Abstractions/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-Nxs7Z1q3f1STfLYKJSVXCs1iBl+Ya6E8o4Oy1bCxJ/rNI44E/0f6tbsrVqAWfB7jlnJfyaAtIalBVxPKUPQb4Q==", "path": "microsoft.aspnetcore.http.abstractions/2.2.0", "hashPath": "microsoft.aspnetcore.http.abstractions.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Http.Extensions/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-2DgZ9rWrJtuR7RYiew01nGRzuQBDaGHGmK56Rk54vsLLsCdzuFUPqbDTJCS1qJQWTbmbIQ9wGIOjpxA1t0l7/w==", "path": "microsoft.aspnetcore.http.extensions/2.2.0", "hashPath": "microsoft.aspnetcore.http.extensions.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Http.Features/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-ziFz5zH8f33En4dX81LW84I6XrYXKf9jg6aM39cM+LffN9KJahViKZ61dGMSO2gd3e+qe5yBRwsesvyqlZaSMg==", "path": "microsoft.aspnetcore.http.features/2.2.0", "hashPath": "microsoft.aspnetcore.http.features.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.HttpOverrides/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-pOlLQyNKQduGbtbgB55RyTHFeshSfKi3DmofrVjk+UBQjyp+Tm0RNNJFQf+sv34hlFsel+VnD79QyO9Zk/c3oA==", "path": "microsoft.aspnetcore.httpoverrides/2.2.0", "hashPath": "microsoft.aspnetcore.httpoverrides.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.HttpsPolicy/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-0EmmwzAkWEPCC8rpg9nGfcOiitIOYkZ13f+b5ED7AAZvz/ZwkdWbeMarGf77lSyA+Mb9O/iAt4LWup0RRMVOJw==", "path": "microsoft.aspnetcore.httpspolicy/2.2.0", "hashPath": "microsoft.aspnetcore.httpspolicy.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.JsonPatch/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-o9BB9hftnCsyJalz9IT0DUFxz8Xvgh3TOfGWolpuf19duxB4FySq7c25XDYBmBMS+sun5/PsEUAi58ra4iJAoA==", "path": "microsoft.aspnetcore.jsonpatch/2.2.0", "hashPath": "microsoft.aspnetcore.jsonpatch.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Localization/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-+PGX1mEfq19EVvskBBb9XBQrXZpZrh6hYhX0x3FkPTEqr+rDM2ZmsEwAAMRmzcidmlDM1/7cyDSU/WhkecU8tA==", "path": "microsoft.aspnetcore.localization/2.2.0", "hashPath": "microsoft.aspnetcore.localization.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Mvc/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-noun9xcrEvOs/ubczt2OluY9/bOOM2erv1D/gyyYtfS2sfyx2uGknUIAWoqmqc401TvQDysyx8S4M9j5zPIVBw==", "path": "microsoft.aspnetcore.mvc/2.2.0", "hashPath": "microsoft.aspnetcore.mvc.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Mvc.Abstractions/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-ET6uZpfVbGR1NjCuLaLy197cQ3qZUjzl7EG5SL4GfJH/c9KRE89MMBrQegqWsh0w1iRUB/zQaK0anAjxa/pz4g==", "path": "microsoft.aspnetcore.mvc.abstractions/2.2.0", "hashPath": "microsoft.aspnetcore.mvc.abstractions.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Mvc.Analyzers/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-Wxxt1rFVHITp4MDaGQP/wyl+ROVVVeQCTWI6C8hxI8X66C4u6gcxvelqgnmsn+dISMCdE/7FQOwgiMx1HxuZqA==", "path": "microsoft.aspnetcore.mvc.analyzers/2.2.0", "hashPath": "microsoft.aspnetcore.mvc.analyzers.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Mvc.ApiExplorer/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-iSREQct43Xg2t3KiQ2648e064al/HSLPXpI5yO9VPeTGDspWKHW23XFHRKPN1YjIQHHfBj8ytXbiF0XcSxp5pg==", "path": "microsoft.aspnetcore.mvc.apiexplorer/2.2.0", "hashPath": "microsoft.aspnetcore.mvc.apiexplorer.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Mvc.Core/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-ALiY4a6BYsghw8PT5+VU593Kqp911U3w9f/dH9/ZoI3ezDsDAGiObqPu/HP1oXK80Ceu0XdQ3F0bx5AXBeuN/Q==", "path": "microsoft.aspnetcore.mvc.core/2.2.0", "hashPath": "microsoft.aspnetcore.mvc.core.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Mvc.Cors/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-oINjMqhU7yzT2T9AMuvktlWlMd40i0do8E1aYslJS+c5fof+EMhjnwTh6cHN1dfrgjkoXJ/gutxn5Qaqf/81Kg==", "path": "microsoft.aspnetcore.mvc.cors/2.2.0", "hashPath": "microsoft.aspnetcore.mvc.cors.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Mvc.DataAnnotations/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-WOw4SA3oT47aiU7ZjN/88j+b79YU6VftmHmxK29Km3PTI7WZdmw675QTcgWfsjEX4joCB82v7TvarO3D0oqOyw==", "path": "microsoft.aspnetcore.mvc.dataannotations/2.2.0", "hashPath": "microsoft.aspnetcore.mvc.dataannotations.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Mvc.Formatters.Json/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-ScWwXrkAvw6PekWUFkIr5qa9NKn4uZGRvxtt3DvtUrBYW5Iu2y4SS/vx79JN0XDHNYgAJ81nVs+4M7UE1Y/O+g==", "path": "microsoft.aspnetcore.mvc.formatters.json/2.2.0", "hashPath": "microsoft.aspnetcore.mvc.formatters.json.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Mvc.Localization/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-H1L4pP124mrN6duwOtNVIJUqy4CczC2/ah4MXarRt9ZRpJd2zNp1j3tJCgyEQpqai6zNVP6Vp2ZRMQcNDcNAKA==", "path": "microsoft.aspnetcore.mvc.localization/2.2.0", "hashPath": "microsoft.aspnetcore.mvc.localization.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Mvc.Razor/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-TXvEOjp3r6qDEjmDtv3pXjQr/Zia9PpoGkl1MyTEqKqrUehBTpAdCjA8APXFwun19lH20OuyU+e4zDYv9g134w==", "path": "microsoft.aspnetcore.mvc.razor/2.2.0", "hashPath": "microsoft.aspnetcore.mvc.razor.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Mvc.Razor.Extensions/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-Sei/0moqBDQKaAYT9PtOeRtvYgHQQLyw/jm3exHw2w9VdzejiMEqCQrN2d63Dk4y7IY0Irr/P9JUFkoVURRcNw==", "path": "microsoft.aspnetcore.mvc.razor.extensions/2.2.0", "hashPath": "microsoft.aspnetcore.mvc.razor.extensions.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Mvc.RazorPages/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-GsMs4QKCf5VgdGZq9/nfAVkMJ/8uE4ie0Iugv4FtxbHBmMdpPQQBfTFKoUpwMbgIRw7hzV8xy2HPPU5o58PsdQ==", "path": "microsoft.aspnetcore.mvc.razorpages/2.2.0", "hashPath": "microsoft.aspnetcore.mvc.razorpages.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Mvc.TagHelpers/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-hsrm/dLx7ztfWV+WEE7O8YqEePW7TmUwFwR7JsOUSTKaV9uSeghdmoOsYuk0HeoTiMhRxH8InQVE9/BgBj+jog==", "path": "microsoft.aspnetcore.mvc.taghelpers/2.2.0", "hashPath": "microsoft.aspnetcore.mvc.taghelpers.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Mvc.ViewFeatures/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-dt7MGkzCFVTAD5oesI8UeVVeiSgaZ0tPdFstQjG6YLJSCiq1koOUSHMpf0PASGdOW/H9hxXkolIBhT5dWqJi7g==", "path": "microsoft.aspnetcore.mvc.viewfeatures/2.2.0", "hashPath": "microsoft.aspnetcore.mvc.viewfeatures.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Razor/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-V54PIyDCFl8COnTp9gezNHpUNHk7F9UnerGeZy3UfbnwYvfzbo+ipqQmSgeoESH8e0JvKhRTyQyZquW2EPtCmg==", "path": "microsoft.aspnetcore.razor/2.2.0", "hashPath": "microsoft.aspnetcore.razor.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Razor.Design/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-VLWK+ZtMMNukY6XjxYHc7mz33vkquoEzQJHm/LCF5REVxIaexLr+UTImljRRJBdUDJluDAQwU+59IX0rFDfURA==", "path": "microsoft.aspnetcore.razor.design/2.2.0", "hashPath": "microsoft.aspnetcore.razor.design.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Razor.Language/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-IeyzVFXZdpUAnWKWoNYE0SsP1Eu7JLjZaC94jaI1VfGtK57QykROz/iGMc8D0VcqC8i02qYTPQN/wPKm6PfidA==", "path": "microsoft.aspnetcore.razor.language/2.2.0", "hashPath": "microsoft.aspnetcore.razor.language.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Razor.Runtime/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-7YqK+H61lN6yj9RiQUko7oaOhKtRR9Q/kBcoWNRemhJdTIWOh1OmdvJKzZrMWOlff3BAjejkPQm+0V0qXk+B1w==", "path": "microsoft.aspnetcore.razor.runtime/2.2.0", "hashPath": "microsoft.aspnetcore.razor.runtime.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.ResponseCaching.Abstractions/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-CIHWEKrHzZfFp7t57UXsueiSA/raku56TgRYauV/W1+KAQq6vevz60zjEKaazt3BI76zwMz3B4jGWnCwd8kwQw==", "path": "microsoft.aspnetcore.responsecaching.abstractions/2.2.0", "hashPath": "microsoft.aspnetcore.responsecaching.abstractions.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Routing/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-jAhDBy0wryOnMhhZTtT9z63gJbvCzFuLm8yC6pHzuVu9ZD1dzg0ltxIwT4cfwuNkIL/TixdKsm3vpVOpG8euWQ==", "path": "microsoft.aspnetcore.routing/2.2.0", "hashPath": "microsoft.aspnetcore.routing.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Routing.Abstractions/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-lRRaPN7jDlUCVCp9i0W+PB0trFaKB0bgMJD7hEJS9Uo4R9MXaMC8X2tJhPLmeVE3SGDdYI4QNKdVmhNvMJGgPQ==", "path": "microsoft.aspnetcore.routing.abstractions/2.2.0", "hashPath": "microsoft.aspnetcore.routing.abstractions.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Server.IIS/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-6NEwFAJFrnZ0f5eJB1ReIpgPM1ZRDj3IE3Rda01nD3vJANCyJFjZ4SGW3Ckn1AmMi225fGflWzpCKLb7/l43jw==", "path": "microsoft.aspnetcore.server.iis/2.2.0", "hashPath": "microsoft.aspnetcore.server.iis.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Server.IISIntegration/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-iVjgAg+doTTrTFCOq6kZRpebXq94YGCx9efMIwO5QhwdY/sHAjfrVz2lXzji63G96YjJVK3ZRrlpgS2fd49ABw==", "path": "microsoft.aspnetcore.server.iisintegration/2.2.0", "hashPath": "microsoft.aspnetcore.server.iisintegration.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Server.Kestrel/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-D0vGB8Tp0UNMiAhT+pwAVeqDDx2OFrfpu/plwm0WhA+1DZvTLc99eDwGISL6LAY8x7a12lhl9w7/m+VdoyDu8Q==", "path": "microsoft.aspnetcore.server.kestrel/2.2.0", "hashPath": "microsoft.aspnetcore.server.kestrel.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Server.Kestrel.Core/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-F6/Vesd3ODq/ISbHfcvfRf7IzRtTvrNX8VA36Knm5e7bteJhoRA2GKQUVQ+neoO1njLvaQKnjcA3rdCZ6AF6cg==", "path": "microsoft.aspnetcore.server.kestrel.core/2.2.0", "hashPath": "microsoft.aspnetcore.server.kestrel.core.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Server.Kestrel.Https/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-nEH5mU6idUYS3/+9BKw2stMOM25ZdGwIH4P4kyj6PVkMPgQUTkBQ7l/ScPkepdhejcOlPa+g3+M4dYsSYPUJ8g==", "path": "microsoft.aspnetcore.server.kestrel.https/2.2.0", "hashPath": "microsoft.aspnetcore.server.kestrel.https.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Server.Kestrel.Transport.Abstractions/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-j1ai2CG8BGp4mYf2TWSFjjy1pRgW9XbqhdR4EOVvrlFVbcpEPfXNIPEdjkcgK+txWCupGzkFnFF8oZsASMtmyw==", "path": "microsoft.aspnetcore.server.kestrel.transport.abstractions/2.2.0", "hashPath": "microsoft.aspnetcore.server.kestrel.transport.abstractions.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-qTACI0wePgAKCH+YKrMgChyfqJpjwgGZEtSuwBw6TjWLQ66THGasleia/7EZz2t2eAjwWxw8RA/D8ODrBqpj9A==", "path": "microsoft.aspnetcore.server.kestrel.transport.sockets/2.2.0", "hashPath": "microsoft.aspnetcore.server.kestrel.transport.sockets.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.WebUtilities/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-9ErxAAKaDzxXASB/b5uLEkLgUWv1QbeVxyJYEHQwMaxXOeFFVkQxiq8RyfVcifLU7NR0QY0p3acqx4ZpYfhHDg==", "path": "microsoft.aspnetcore.webutilities/2.2.0", "hashPath": "microsoft.aspnetcore.webutilities.2.2.0.nupkg.sha512"}, "Microsoft.Bcl.AsyncInterfaces/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-3WA9q9yVqJp222P3x1wYIGDAkpjAku0TMUaaQV22g6L67AI0LdOIrVS7Ht2vJfLHGSPVuqN94vIr15qn+HEkHw==", "path": "microsoft.bcl.asyncinterfaces/8.0.0", "hashPath": "microsoft.bcl.asyncinterfaces.8.0.0.nupkg.sha512"}, "Microsoft.CodeAnalysis.Analyzers/1.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-HS3iRWZKcUw/8eZ/08GXKY2Bn7xNzQPzf8gRPHGSowX7u7XXu9i9YEaBeBNKUXWfI7qjvT2zXtLUvbN0hds8vg==", "path": "microsoft.codeanalysis.analyzers/1.1.0", "hashPath": "microsoft.codeanalysis.analyzers.1.1.0.nupkg.sha512"}, "Microsoft.CodeAnalysis.Common/2.8.0": {"type": "package", "serviceable": true, "sha512": "sha512-06AzG7oOLKTCN1EnoVYL1bQz+Zwa10LMpUn7Kc+PdpN8CQXRqXTyhfxuKIz6t0qWfoatBNXdHD0OLcEYp5pOvQ==", "path": "microsoft.codeanalysis.common/2.8.0", "hashPath": "microsoft.codeanalysis.common.2.8.0.nupkg.sha512"}, "Microsoft.CodeAnalysis.CSharp/2.8.0": {"type": "package", "serviceable": true, "sha512": "sha512-RizcFXuHgGmeuZhxxE1qQdhFA9lGOHlk0MJlCUt6LOnYsevo72gNikPcbANFHY02YK8L/buNrihchY0TroGvXQ==", "path": "microsoft.codeanalysis.csharp/2.8.0", "hashPath": "microsoft.codeanalysis.csharp.2.8.0.nupkg.sha512"}, "Microsoft.CodeAnalysis.Razor/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-2qL0Qyu5qHzg6/JzF80mLgsqn9NP/Q0mQwjH+Z+DiqcuODJx8segjN4un2Tnz6bEAWv8FCRFNXR/s5wzlxqA8A==", "path": "microsoft.codeanalysis.razor/2.2.0", "hashPath": "microsoft.codeanalysis.razor.2.2.0.nupkg.sha512"}, "Microsoft.CSharp/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-pTj+D3uJWyN3My70i2Hqo+OXixq3Os2D1nJ2x92FFo6sk8fYS1m1WLNTs0Dc1uPaViH0YvEEwvzddQ7y4rhXmA==", "path": "microsoft.csharp/4.7.0", "hashPath": "microsoft.csharp.4.7.0.nupkg.sha512"}, "Microsoft.DiaSymReader.Native/1.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-vIzndj0MoWW2Mp/iztUSKvmR9vZTqOVQ6PBvwA57+CDoiz7eUMU15rrAX+/QA0bkmwQ08GRibFBB9LNOl0EiBA==", "path": "microsoft.diasymreader.native/1.7.0", "hashPath": "microsoft.diasymreader.native.1.7.0.nupkg.sha512"}, "Microsoft.DotNet.PlatformAbstractions/2.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-9KPDwvb/hLEVXYruVHVZ8BkebC8j17DmPb56LnqRF74HqSPLjCkrlFUjOtFpQPA2DeADBRTI/e69aCfRBfrhxw==", "path": "microsoft.dotnet.platformabstractions/2.1.0", "hashPath": "microsoft.dotnet.platformabstractions.2.1.0.nupkg.sha512"}, "Microsoft.Extensions.Caching.Abstractions/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-spsJkYo8gGJapaxTSQFN/wqA+ghpJMLwB4ZyTB+fSdpd7AmMFP/YSpIcGmczcw4KggpxLGhLk7lCkSIlgvHaqQ==", "path": "microsoft.extensions.caching.abstractions/2.2.0", "hashPath": "microsoft.extensions.caching.abstractions.2.2.0.nupkg.sha512"}, "Microsoft.Extensions.Caching.Memory/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-yFs44RzB2Pzfoj4uk+mEz3MTTQKyeWb8gDhv5GyVPfHnLv0eQhGwzbw/5WpxAcVyOgG/H3/0ULY6g0/7/B+r7w==", "path": "microsoft.extensions.caching.memory/2.2.0", "hashPath": "microsoft.extensions.caching.memory.2.2.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-nOP8R1mVb/6mZtm2qgAJXn/LFm/2kMjHDAg/QJLFG6CuWYJtaD3p1BwQhufBVvRzL9ceJ/xF0SQ0qsI2GkDQAA==", "path": "microsoft.extensions.configuration/2.2.0", "hashPath": "microsoft.extensions.configuration.2.2.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Abstractions/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-65MrmXCziWaQFrI0UHkQbesrX5wTwf9XPjY5yFm/VkgJKFJ5gqvXRoXjIZcf2wLi5ZlwGz/oMYfyURVCWbM5iw==", "path": "microsoft.extensions.configuration.abstractions/2.2.0", "hashPath": "microsoft.extensions.configuration.abstractions.2.2.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Binder/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-vJ9xvOZCnUAIHcGC3SU35r3HKmHTVIeHzo6u/qzlHAqD8m6xv92MLin4oJntTvkpKxVX3vI1GFFkIQtU3AdlsQ==", "path": "microsoft.extensions.configuration.binder/2.2.0", "hashPath": "microsoft.extensions.configuration.binder.2.2.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.CommandLine/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-4kJIGOSRqD1Ccqerst4t/zsNs51plR7BIxbdKO1J/9rL+2DuNT+ieAuEv+HROelqTam3yOpKFR7TtHBt3oLpOA==", "path": "microsoft.extensions.configuration.commandline/2.2.0", "hashPath": "microsoft.extensions.configuration.commandline.2.2.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.EnvironmentVariables/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-gIqt9PkKO01hZ0zmHnWrZ1E45MDreZTVoyDbL1kMWKtDgxxWTJpYtESTEcgpvR1uB1iex1zKGYzJpOMgmuP5TQ==", "path": "microsoft.extensions.configuration.environmentvariables/2.2.0", "hashPath": "microsoft.extensions.configuration.environmentvariables.2.2.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.FileExtensions/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-H1qCpWBC8Ed4tguTR/qYkbb3F6DI5Su3t8xyFo3/5MzAd8PwPpHzgX8X04KbBxKmk173Pb64x7xMHarczVFQUA==", "path": "microsoft.extensions.configuration.fileextensions/2.2.0", "hashPath": "microsoft.extensions.configuration.fileextensions.2.2.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Json/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-jUDdmLyFmLf9V3mqnMzSAzAv4QigJ67tZh5Q7HBXeBnESL2UyeesNG6jSBti+b63JpxZf+EDyn+anx3gyrNxug==", "path": "microsoft.extensions.configuration.json/2.2.0", "hashPath": "microsoft.extensions.configuration.json.2.2.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.UserSecrets/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-2/N2xo6/sNbVshnKktmq5lwaQbsAR2SrzCVrJEeMP8OKZVI7SzT8P6/WXZF8/YC7dTYsMe3nrHzgl1cF9i5ZKQ==", "path": "microsoft.extensions.configuration.usersecrets/2.2.0", "hashPath": "microsoft.extensions.configuration.usersecrets.2.2.0.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-MZtBIwfDFork5vfjpJdG5g8wuJFt7d/y3LOSVVtDK/76wlbtz6cjltfKHqLx2TKVqTj5/c41t77m1+h20zqtPA==", "path": "microsoft.extensions.dependencyinjection/2.2.0", "hashPath": "microsoft.extensions.dependencyinjection.2.2.0.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection.Abstractions/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-f9hstgjVmr6rmrfGSpfsVOl2irKAgr1QjrSi3FgnS7kulxband50f2brRLwySAQTADPZeTdow0mpSMcoAdadCw==", "path": "microsoft.extensions.dependencyinjection.abstractions/2.2.0", "hashPath": "microsoft.extensions.dependencyinjection.abstractions.2.2.0.nupkg.sha512"}, "Microsoft.Extensions.DependencyModel/2.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-nS2XKqi+1A1umnYNLX2Fbm/XnzCxs5i+zXVJ3VC6r9t2z0NZr9FLnJN4VQpKigdcWH/iFTbMuX6M6WQJcTjVIg==", "path": "microsoft.extensions.dependencymodel/2.1.0", "hashPath": "microsoft.extensions.dependencymodel.2.1.0.nupkg.sha512"}, "Microsoft.Extensions.FileProviders.Abstractions/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-EcnaSsPTqx2MGnHrmWOD0ugbuuqVT8iICqSqPzi45V5/MA1LjUNb0kwgcxBGqizV1R+WeBK7/Gw25Jzkyk9bIw==", "path": "microsoft.extensions.fileproviders.abstractions/2.2.0", "hashPath": "microsoft.extensions.fileproviders.abstractions.2.2.0.nupkg.sha512"}, "Microsoft.Extensions.FileProviders.Composite/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-Az/RxWB+UlyVN/TvQFaGXx8XAXVZN5WQnnuJOsjwBzghSJc1i8zqNjIypPHOedcuIXs2XSWgOSL6YQ3BlCnoJA==", "path": "microsoft.extensions.fileproviders.composite/2.2.0", "hashPath": "microsoft.extensions.fileproviders.composite.2.2.0.nupkg.sha512"}, "Microsoft.Extensions.FileProviders.Physical/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-tbDHZnBJkjYd9NjlRZ9ondDiv1Te3KYCTW2RWpR1B0e1Z8+EnFRo7qNnHkkSCixLdlPZzhjlX24d/PixQ7w2dA==", "path": "microsoft.extensions.fileproviders.physical/2.2.0", "hashPath": "microsoft.extensions.fileproviders.physical.2.2.0.nupkg.sha512"}, "Microsoft.Extensions.FileSystemGlobbing/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-ZSsHZp3PyW6vk37tDEdypjgGlNtpJ0EixBMOfUod2Thx7GtwfFSAQXUQx8a8BN8vfWKGGMbp7jPWdoHx/At4wQ==", "path": "microsoft.extensions.filesystemglobbing/2.2.0", "hashPath": "microsoft.extensions.filesystemglobbing.2.2.0.nupkg.sha512"}, "Microsoft.Extensions.Hosting.Abstractions/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-+k4AEn68HOJat5gj1TWa6X28WlirNQO9sPIIeQbia+91n03esEtMSSoekSTpMjUzjqtJWQN3McVx0GvSPFHF/Q==", "path": "microsoft.extensions.hosting.abstractions/2.2.0", "hashPath": "microsoft.extensions.hosting.abstractions.2.2.0.nupkg.sha512"}, "Microsoft.Extensions.Localization/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-3nBQLeBrcd4Rgd9vQi4gF5NgAWxnQrHekjjwlgww4wyLNfJDizjiex2resOLoAuAgy3y2IIAWjOpbr0UKR2ykw==", "path": "microsoft.extensions.localization/2.2.0", "hashPath": "microsoft.extensions.localization.2.2.0.nupkg.sha512"}, "Microsoft.Extensions.Localization.Abstractions/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-FQzXG/lYR9UOM2zHpqsjTRpp3EghIYo3FCsQpfmtbp+glPaU0WXZfNmMjyqBRmMj1Sq93fPnC+G9zzYRauuRQA==", "path": "microsoft.extensions.localization.abstractions/2.2.0", "hashPath": "microsoft.extensions.localization.abstractions.2.2.0.nupkg.sha512"}, "Microsoft.Extensions.Logging/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-Nxqhadc9FCmFHzU+fz3oc8sFlE6IadViYg8dfUdGzJZ2JUxnCsRghBhhOWdM4B2zSZqEc+0BjliBh/oNdRZuig==", "path": "microsoft.extensions.logging/2.2.0", "hashPath": "microsoft.extensions.logging.2.2.0.nupkg.sha512"}, "Microsoft.Extensions.Logging.Abstractions/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-B2WqEox8o+4KUOpL7rZPyh6qYjik8tHi2tN8Z9jZkHzED8ElYgZa/h6K+xliB435SqUcWT290Fr2aa8BtZjn8A==", "path": "microsoft.extensions.logging.abstractions/2.2.0", "hashPath": "microsoft.extensions.logging.abstractions.2.2.0.nupkg.sha512"}, "Microsoft.Extensions.Logging.Configuration/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-ukU1mQGX9+xBsEzpNd13yl4deFVYI+fxxnmKpOhvNZsF+/trCrAUQh+9QM5pPGHbfYkz3lLQ4BXfKCP0502dLw==", "path": "microsoft.extensions.logging.configuration/2.2.0", "hashPath": "microsoft.extensions.logging.configuration.2.2.0.nupkg.sha512"}, "Microsoft.Extensions.Logging.Console/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-1eGgcOJ++PMxW6sn++j6U7wsWvhEBm/5ScqBUUBGLRE8M7AHahi9tsxivDMqEXVM3F0/pshHl3kEpMXtw4BeFg==", "path": "microsoft.extensions.logging.console/2.2.0", "hashPath": "microsoft.extensions.logging.console.2.2.0.nupkg.sha512"}, "Microsoft.Extensions.Logging.Debug/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-JjqWtshxUujSnxslFccCRAaH8uFOciqXkYdRw+h5MwpC4sUc+ju9yZzvVi6PA5vW09ckv26EkasEvXrofGiaJg==", "path": "microsoft.extensions.logging.debug/2.2.0", "hashPath": "microsoft.extensions.logging.debug.2.2.0.nupkg.sha512"}, "Microsoft.Extensions.Logging.EventSource/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-oOa5H+vdNgpsxE6vgtX4U/godKtX2edVi+QjlWb2PBQfavGIQ3WxtjxN+B0DQAjwBNdV4mW8cgOiDEZ8KdR7Ig==", "path": "microsoft.extensions.logging.eventsource/2.2.0", "hashPath": "microsoft.extensions.logging.eventsource.2.2.0.nupkg.sha512"}, "Microsoft.Extensions.ObjectPool/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-gA8H7uQOnM5gb+L0uTNjViHYr+hRDqCdfugheGo/MxQnuHzmhhzCBTIPm19qL1z1Xe0NEMabfcOBGv9QghlZ8g==", "path": "microsoft.extensions.objectpool/2.2.0", "hashPath": "microsoft.extensions.objectpool.2.2.0.nupkg.sha512"}, "Microsoft.Extensions.Options/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-UpZLNLBpIZ0GTebShui7xXYh6DmBHjWM8NxGxZbdQh/bPZ5e6YswqI+bru6BnEL5eWiOdodsXtEz3FROcgi/qg==", "path": "microsoft.extensions.options/2.2.0", "hashPath": "microsoft.extensions.options.2.2.0.nupkg.sha512"}, "Microsoft.Extensions.Options.ConfigurationExtensions/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-d4WS6yVXaw43ffiUnHj8oG1t2B6RbDDiQcgdA+Eq//NlPa3Wd+GTJFKj4OM4eDF3GjVumGr/CEVRS/jcYoF5LA==", "path": "microsoft.extensions.options.configurationextensions/2.2.0", "hashPath": "microsoft.extensions.options.configurationextensions.2.2.0.nupkg.sha512"}, "Microsoft.Extensions.Primitives/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-azyQtqbm4fSaDzZHD/J+V6oWMFaf2tWP4WEGIYePLCMw3+b2RQdj9ybgbQyjCshcitQKQ4lEDOZjmSlTTrHxUg==", "path": "microsoft.extensions.primitives/2.2.0", "hashPath": "microsoft.extensions.primitives.2.2.0.nupkg.sha512"}, "Microsoft.Extensions.WebEncoders/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-V8XcqYcpcdBAxUhLeyYcuKmxu4CtNQA9IphTnARpQGhkop4A93v2XgM3AtaVVJo3H2cDWxWM6aeO8HxkifREqw==", "path": "microsoft.extensions.webencoders/2.2.0", "hashPath": "microsoft.extensions.webencoders.2.2.0.nupkg.sha512"}, "Microsoft.Graph/3.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-0NbdSC4SdL0nCw5dSZkXh7UttA5gUPs6dA5Sg59u6qHnA5mpmiebBdHlp8M2Up3mzAxGYNDt3gx0/x+oLoOm5Q==", "path": "microsoft.graph/3.1.0", "hashPath": "microsoft.graph.3.1.0.nupkg.sha512"}, "Microsoft.Graph.Communications.Calls/1.2.0.850": {"type": "package", "serviceable": true, "sha512": "sha512-hxnZVze5894DfXS36U06KCnX5jZTzx0c4Sjp7AHDnF94xJ/Ecl2di2IYZn5HYkUzpwRwRHxzDukFrLW2fUefiA==", "path": "microsoft.graph.communications.calls/1.2.0.850", "hashPath": "microsoft.graph.communications.calls.1.2.0.850.nupkg.sha512"}, "Microsoft.Graph.Communications.Calls.Media/1.2.0.850": {"type": "package", "serviceable": true, "sha512": "sha512-hYxsuenjQfdMGOvc0GZDTwM7CKeFyvxNBrr1yaxPeUfKZLa5qRGQkSH2x44FNP6I6pGfWUKqUm2Och/FYW8Dmg==", "path": "microsoft.graph.communications.calls.media/1.2.0.850", "hashPath": "microsoft.graph.communications.calls.media.1.2.0.850.nupkg.sha512"}, "Microsoft.Graph.Communications.Client/1.2.0.850": {"type": "package", "serviceable": true, "sha512": "sha512-dZzpe+7W9hRWpL1FxHu+w+Xf0Qkp2QK19ogwgqf8PpxXDOghzu7g5jKUAx7UvIPZoQKDVdJMauCa34KKR9nKbg==", "path": "microsoft.graph.communications.client/1.2.0.850", "hashPath": "microsoft.graph.communications.client.1.2.0.850.nupkg.sha512"}, "Microsoft.Graph.Communications.Common/1.2.0.850": {"type": "package", "serviceable": true, "sha512": "sha512-2P61WKePt4c4tDGPH540HhVQl16QqTsi03rTPo+RPR1CYedYu+09J/1JxCqGfCgpFirXu+b9YjpbwCCF6xr76Q==", "path": "microsoft.graph.communications.common/1.2.0.850", "hashPath": "microsoft.graph.communications.common.1.2.0.850.nupkg.sha512"}, "Microsoft.Graph.Communications.Core/1.2.0.850": {"type": "package", "serviceable": true, "sha512": "sha512-jGpiX3T9Um/Vvei3SDaRoxM3r5bFAgxpaS4DBSOc58eumaA5mN5ambBMTVjrc15mJ5VbPW9+n99VDvZDxvCoMw==", "path": "microsoft.graph.communications.core/1.2.0.850", "hashPath": "microsoft.graph.communications.core.1.2.0.850.nupkg.sha512"}, "Microsoft.Graph.Core/1.20.1": {"type": "package", "serviceable": true, "sha512": "sha512-CZBtxAar6kY/hmciP34tqxOVRNrk0y8w4CSW7BrFX672NLCRGNh04m/2pFIwqW+HFwSFeqkLPxeKM6WhhoVriQ==", "path": "microsoft.graph.core/1.20.1", "hashPath": "microsoft.graph.core.1.20.1.nupkg.sha512"}, "Microsoft.IdentityModel.Abstractions/8.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-OtlIWcyX01olfdevPKZdIPfBEvbcioDyBiE/Z2lHsopsMD7twcKtlN9kMevHmI5IIPhFpfwCIiR6qHQz1WHUIw==", "path": "microsoft.identitymodel.abstractions/8.0.1", "hashPath": "microsoft.identitymodel.abstractions.8.0.1.nupkg.sha512"}, "Microsoft.IdentityModel.Clients.ActiveDirectory/5.2.4": {"type": "package", "serviceable": true, "sha512": "sha512-UDn9cidGDrE46jRxyhFtsxN7CQ0uFIYmlLDsguWvRnhqlBgDugsmVVUH2jyyds2rxrfPl7EvQfyBFjfibLX8eA==", "path": "microsoft.identitymodel.clients.activedirectory/5.2.4", "hashPath": "microsoft.identitymodel.clients.activedirectory.5.2.4.nupkg.sha512"}, "Microsoft.IdentityModel.JsonWebTokens/8.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-s6++gF9x0rQApQzOBbSyp4jUaAlwm+DroKfL8gdOHxs83k8SJfUXhuc46rDB3rNXBQ1MVRxqKUrqFhO/M0E97g==", "path": "microsoft.identitymodel.jsonwebtokens/8.0.1", "hashPath": "microsoft.identitymodel.jsonwebtokens.8.0.1.nupkg.sha512"}, "Microsoft.IdentityModel.Logging/8.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-UCPF2exZqBXe7v/6sGNiM6zCQOUXXQ9+v5VTb9gPB8ZSUPnX53BxlN78v2jsbIvK9Dq4GovQxo23x8JgWvm/Qg==", "path": "microsoft.identitymodel.logging/8.0.1", "hashPath": "microsoft.identitymodel.logging.8.0.1.nupkg.sha512"}, "Microsoft.IdentityModel.Protocols/8.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-uA2vpKqU3I2mBBEaeJAWPTjT9v1TZrGWKdgK6G5qJd03CLx83kdiqO9cmiK8/n1erkHzFBwU/RphP83aAe3i3g==", "path": "microsoft.identitymodel.protocols/8.0.1", "hashPath": "microsoft.identitymodel.protocols.8.0.1.nupkg.sha512"}, "Microsoft.IdentityModel.Protocols.OpenIdConnect/8.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-AQDbfpL+yzuuGhO/mQhKNsp44pm5Jv8/BI4KiFXR7beVGZoSH35zMV3PrmcfvSTsyI6qrcR898NzUauD6SRigg==", "path": "microsoft.identitymodel.protocols.openidconnect/8.0.1", "hashPath": "microsoft.identitymodel.protocols.openidconnect.8.0.1.nupkg.sha512"}, "Microsoft.IdentityModel.Tokens/8.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-kDimB6Dkd3nkW2oZPDkMkVHfQt3IDqO5gL0oa8WVy3OP4uE8Ij+8TXnqg9TOd9ufjsY3IDiGz7pCUbnfL18tjg==", "path": "microsoft.identitymodel.tokens/8.0.1", "hashPath": "microsoft.identitymodel.tokens.8.0.1.nupkg.sha512"}, "Microsoft.Net.Http.Headers/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-iZNkjYqlo8sIOI0bQfpsSoMTmB/kyvmV2h225ihyZT33aTp48ZpF6qYnXxzSXmHt8DpBAwBTX+1s1UFLbYfZKg==", "path": "microsoft.net.http.headers/2.2.0", "hashPath": "microsoft.net.http.headers.2.2.0.nupkg.sha512"}, "Microsoft.Psi.Audio/**********-beta": {"type": "package", "serviceable": true, "sha512": "sha512-kg2KyuZkn7EAQy+y9Ux2gME6gGnopIzi5S4ei7x8YqtUD4Baamhzx+nO0LqjQMehiUCxpgIT5Io2teDP3ZoWbQ==", "path": "microsoft.psi.audio/**********-beta", "hashPath": "microsoft.psi.audio.**********-beta.nupkg.sha512"}, "Microsoft.Psi.Data/**********-beta": {"type": "package", "serviceable": true, "sha512": "sha512-vYCfg5ZH+2h6n7vfCLijPFAxJRG3iE4XRZoBx1X/NsrqR1hO6QqtqGdCjt3Jyo8MbyLXl5GDHJildPOqfP0hFw==", "path": "microsoft.psi.data/**********-beta", "hashPath": "microsoft.psi.data.**********-beta.nupkg.sha512"}, "Microsoft.Psi.Imaging/*********-beta": {"type": "package", "serviceable": true, "sha512": "sha512-d20vP8XjzyVn21Rq91bpDhVO2jaVLeDj/JMwMiMwaBtfSAiPunYs++lNMbvZF1+SZmAE862TwfJO7rbmf94LYg==", "path": "microsoft.psi.imaging/*********-beta", "hashPath": "microsoft.psi.imaging.*********-beta.nupkg.sha512"}, "Microsoft.Psi.Imaging.Windows/*********-beta": {"type": "package", "serviceable": true, "sha512": "sha512-e37NzU+LUG+jZCujdO6/+RMRCPn5Re5uSVRlGiBasPvBaivip4d8tuMSmbSE3PDFoQxXgPuJpMtUyCrP8263pA==", "path": "microsoft.psi.imaging.windows/*********-beta", "hashPath": "microsoft.psi.imaging.windows.*********-beta.nupkg.sha512"}, "Microsoft.Psi.Runtime/**********-beta": {"type": "package", "serviceable": true, "sha512": "sha512-<PERSON><PERSON><PERSON><PERSON>uza/OD/UnJNR2dv/YCZzl9r3cK838Ji7Aaf1Eqtac03rjrarB6AGO4pF4iER+GEItpLlV0qxoxqfQ06BUQ==", "path": "microsoft.psi.runtime/**********-beta", "hashPath": "microsoft.psi.runtime.**********-beta.nupkg.sha512"}, "Microsoft.Skype.Bots.Media/*********-alpha": {"type": "package", "serviceable": true, "sha512": "sha512-j4r19cTjX3/H3UGz75l0rIsGbwgDDcxu20gqyeJVo1CbDqC1zPUVOQgBPY4xdL01SsPsZCa4mhNymZyJAva4OA==", "path": "microsoft.skype.bots.media/*********-alpha", "hashPath": "microsoft.skype.bots.media.*********-alpha.nupkg.sha512"}, "Microsoft.Win32.Registry/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-+FWlwd//+Tt56316p00hVePBCouXyEzT86Jb3+AuRotTND0IYn0OO3obs1gnQEs/txEnt+rF2JBGLItTG+Be6A==", "path": "microsoft.win32.registry/4.5.0", "hashPath": "microsoft.win32.registry.4.5.0.nupkg.sha512"}, "Newtonsoft.Json/13.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-R2pZ3B0UjeyHShm9vG+Tu0EBb2lC8b0dFzV9gVn50ofHXh9Smjk6kTn7A/FdAsC8B5cKib1OnGYOXxRBz5XQDg==", "path": "newtonsoft.json/13.0.2", "hashPath": "newtonsoft.json.13.0.2.nupkg.sha512"}, "Newtonsoft.Json.Bson/1.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-5PYT/IqQ+UK31AmZiSS102R6EsTo+LGTSI8bp7WAUqDKaF4wHXD8U9u4WxTI1vc64tYi++8p3dk3WWNqPFgldw==", "path": "newtonsoft.json.bson/1.0.1", "hashPath": "newtonsoft.json.bson.1.0.1.nupkg.sha512"}, "System.AppContext/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-fKC+rmaLfeIzUhagxY17Q9siv/sPrjjKcfNg1Ic8IlQkZLipo8ljcaZQu4VtI4Jqbzjc2VTjzGLF6WmsRXAEgA==", "path": "system.appcontext/4.3.0", "hashPath": "system.appcontext.4.3.0.nupkg.sha512"}, "System.Buffers/4.5.1": {"type": "package", "serviceable": true, "sha512": "sha512-Rw7ijyl1qqRS0YQD/WycNst8hUUMgrMH4FCn1nNm27M4VxchZ1js3fVjQaANHO5f3sN4isvP4a+Met9Y4YomAg==", "path": "system.buffers/4.5.1", "hashPath": "system.buffers.4.5.1.nupkg.sha512"}, "System.Collections/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-3Dcj85/TBdVpL5Zr+gEEBUuFe2icOnLalmEh9hfck1PTYbbyWuZgh4fmm2ysCLTrqLQw6t3TgTyJ+VLp+Qb+Lw==", "path": "system.collections/4.3.0", "hashPath": "system.collections.4.3.0.nupkg.sha512"}, "System.Collections.Concurrent/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-ztl69Xp0Y/UXCL+3v3tEU+lIy+bvjKNUmopn1wep/a291pVPK7dxBd6T7WnlQqRog+d1a/hSsgRsmFnIBKTPLQ==", "path": "system.collections.concurrent/4.3.0", "hashPath": "system.collections.concurrent.4.3.0.nupkg.sha512"}, "System.Collections.Immutable/1.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-RVSM6wZUo6L2y6P3vN6gjUtyJ2IF2RVtrepF3J7nrDKfFQd5u/SnSUFclchYQis8/k5scHy9E+fVeKVQLnnkzw==", "path": "system.collections.immutable/1.7.0", "hashPath": "system.collections.immutable.1.7.0.nupkg.sha512"}, "System.ComponentModel.Annotations/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-UxYQ3FGUOtzJ7LfSdnYSFd7+oEv6M8NgUatatIN2HxNtDdlcvFAf+VIq4Of9cDMJEJC0aSRv/x898RYhB4Yppg==", "path": "system.componentmodel.annotations/4.5.0", "hashPath": "system.componentmodel.annotations.4.5.0.nupkg.sha512"}, "System.Console/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-DHDrIxiqk1h03m6khKWV2X8p/uvN79rgSqpilL6uzpmSfxfU5ng8VcPtW4qsDsQDHiTv6IPV9TmD5M/vElPNLg==", "path": "system.console/4.3.0", "hashPath": "system.console.4.3.0.nupkg.sha512"}, "System.Diagnostics.Debug/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-ZUhUOdqmaG5Jk3Xdb8xi5kIyQYAA4PnTNlHx1mu9ZY3qv4ELIdKbnL/akbGaKi2RnNUWaZsAs31rvzFdewTj2g==", "path": "system.diagnostics.debug/4.3.0", "hashPath": "system.diagnostics.debug.4.3.0.nupkg.sha512"}, "System.Diagnostics.DiagnosticSource/4.6.0": {"type": "package", "serviceable": true, "sha512": "sha512-mbBgoR0rRfl2uimsZ2avZY8g7Xnh1Mza0rJZLPcxqiMWlkGukjmRkuMJ/er+AhQuiRIh80CR/Hpeztr80seV5g==", "path": "system.diagnostics.diagnosticsource/4.6.0", "hashPath": "system.diagnostics.diagnosticsource.4.6.0.nupkg.sha512"}, "System.Diagnostics.FileVersionInfo/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-omCF64wzQ3Q2CeIqkD6lmmxeMZtGHUmzgFMPjfVaOsyqpR66p/JaZzManMw1s33osoAb5gqpncsjie67+yUPHQ==", "path": "system.diagnostics.fileversioninfo/4.3.0", "hashPath": "system.diagnostics.fileversioninfo.4.3.0.nupkg.sha512"}, "System.Diagnostics.StackTrace/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-BiHg0vgtd35/DM9jvtaC1eKRpWZxr0gcQd643ABG7GnvSlf5pOkY2uyd42mMOJoOmKvnpNj0F4tuoS1pacTwYw==", "path": "system.diagnostics.stacktrace/4.3.0", "hashPath": "system.diagnostics.stacktrace.4.3.0.nupkg.sha512"}, "System.Diagnostics.Tools/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-UUvkJfSYJMM6x527dJg2VyWPSRqIVB0Z7dbjHst1zmwTXz5CcXSYJFWRpuigfbO1Lf7yfZiIaEUesfnl/g5EyA==", "path": "system.diagnostics.tools/4.3.0", "hashPath": "system.diagnostics.tools.4.3.0.nupkg.sha512"}, "System.Drawing.Common/4.7.2": {"type": "package", "serviceable": true, "sha512": "sha512-I2y4KBK3VCvU/WqE2xv7NjQ67maXHttkFSHYKgU2evrG9Yqh0oFjfORXt5hZTk+BVjdyFo2h0/YQZsca33BGmg==", "path": "system.drawing.common/4.7.2", "hashPath": "system.drawing.common.4.7.2.nupkg.sha512"}, "System.Dynamic.Runtime/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-SNVi1E/vfWUAs/WYKhE9+qlS6KqK0YVhnlT0HQtr8pMIA8YX3lwy3uPMownDwdYISBdmAF/2holEIldVp85Wag==", "path": "system.dynamic.runtime/4.3.0", "hashPath": "system.dynamic.runtime.4.3.0.nupkg.sha512"}, "System.Globalization/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-kYdVd2f2PAdFGblzFswE4hkNANJBKRmsfa2X5LG2AcWE1c7/4t0pYae1L8vfZ5xvE2nK/R9JprtToA61OSHWIg==", "path": "system.globalization/4.3.0", "hashPath": "system.globalization.4.3.0.nupkg.sha512"}, "System.IdentityModel.Tokens.Jwt/8.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-GJw3bYkWpOgvN3tJo5X4lYUeIFA2HD293FPUhKmp7qxS+g5ywAb34Dnd3cDAFLkcMohy5XTpoaZ4uAHuw0uSPQ==", "path": "system.identitymodel.tokens.jwt/8.0.1", "hashPath": "system.identitymodel.tokens.jwt.8.0.1.nupkg.sha512"}, "System.IO/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-3qjaHvxQPDpSOYICjUoTsmoq5u6QJAFRUITgeT/4gqkF1bajbSmb1kwSxEA8AHlofqgcKJcM8udgieRNhaJ5Cg==", "path": "system.io/4.3.0", "hashPath": "system.io.4.3.0.nupkg.sha512"}, "System.IO.Compression/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-YHndyoiV90iu4iKG115ibkhrG+S3jBm8Ap9OwoUAzO5oPDAWcr0SFwQFm0HjM8WkEZWo0zvLTyLmbvTkW1bXgg==", "path": "system.io.compression/4.3.0", "hashPath": "system.io.compression.4.3.0.nupkg.sha512"}, "System.IO.FileSystem/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-3wEMARTnuio+ulnvi+hkRNROYwa1kylvYahhcLk4HSoVdl+xxTFVeVlYOfLwrDPImGls0mDqbMhrza8qnWPTdA==", "path": "system.io.filesystem/4.3.0", "hashPath": "system.io.filesystem.4.3.0.nupkg.sha512"}, "System.IO.FileSystem.Primitives/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-6QOb2XFLch7bEc4lIcJH49nJN2HV+OC3fHDgsLVsBVBk3Y4hFAnOBGzJ2lUu7CyDDFo9IBWkSsnbkT6IBwwiMw==", "path": "system.io.filesystem.primitives/4.3.0", "hashPath": "system.io.filesystem.primitives.4.3.0.nupkg.sha512"}, "System.IO.Pipelines/4.5.2": {"type": "package", "serviceable": true, "sha512": "sha512-NOC/SO4gSX6t0tB25xxDPqPEzkksuzW7NVFBTQGAkjXXUPQl7ZtyE83T7tUCP2huFBbPombfCKvq1Ox1aG8D9w==", "path": "system.io.pipelines/4.5.2", "hashPath": "system.io.pipelines.4.5.2.nupkg.sha512"}, "System.Linq/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-5DbqIUpsDp0dFftytzuMmc0oeMdQwjcP/EWxsksIz/w1TcFRkZ3yKKz0PqiYFMmEwPSWw+qNVqD7PJ889JzHbw==", "path": "system.linq/4.3.0", "hashPath": "system.linq.4.3.0.nupkg.sha512"}, "System.Linq.Expressions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-PGKkrd2khG4CnlyJwxwwaWWiSiWFNBGlgXvJpeO0xCXrZ89ODrQ6tjEWS/kOqZ8GwEOUATtKtzp1eRgmYNfclg==", "path": "system.linq.expressions/4.3.0", "hashPath": "system.linq.expressions.4.3.0.nupkg.sha512"}, "System.Memory/4.5.5": {"type": "package", "serviceable": true, "sha512": "sha512-XIWiDvKPXaTveaB7HVganDlOCRoj03l+jrwNvcge/t8vhGYKvqV+dMv6G4SAX2NoNmN0wZfVPTAlFwZcZvVOUw==", "path": "system.memory/4.5.5", "hashPath": "system.memory.4.5.5.nupkg.sha512"}, "System.Net.Http/4.3.4": {"type": "package", "serviceable": true, "sha512": "sha512-aOa2d51SEbmM+H+Csw7yJOuNZoHkrP2XnAurye5HWYgGVVU54YZDvsLUYRv6h18X3sPnjNCANmN7ZhIPiqMcjA==", "path": "system.net.http/4.3.4", "hashPath": "system.net.http.4.3.4.nupkg.sha512"}, "System.Numerics.Vectors/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-QQTlPTl06J/iiDbJCiepZ4H//BVraReU4O4EoRw1U02H5TLUIT7xn3GnDp9AXPSlJUDyFs4uWjWafNX6WrAojQ==", "path": "system.numerics.vectors/4.5.0", "hashPath": "system.numerics.vectors.4.5.0.nupkg.sha512"}, "System.Reflection/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-KMiAFoW7MfJGa9nDFNcfu+FpEdiHpWgTcS2HdMpDvt9saK3y/G4GwprPyzqjFH9NTaGPQeWNHU+iDlDILj96aQ==", "path": "system.reflection/4.3.0", "hashPath": "system.reflection.4.3.0.nupkg.sha512"}, "System.Reflection.Emit.ILGeneration/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-AucBYo3DSI0IDxdUjKksBcQJXPHyoPyrCXYURW1WDsLI4M65Ar/goSHjdnHOAY9MiYDNKqDlIgaYm+zL2hA1KA==", "path": "system.reflection.emit.ilgeneration/4.7.0", "hashPath": "system.reflection.emit.ilgeneration.4.7.0.nupkg.sha512"}, "System.Reflection.Emit.Lightweight/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-a4OLB4IITxAXJeV74MDx49Oq2+PsF6Sml54XAFv+2RyWwtDBcabzoxiiJRhdhx+gaohLh4hEGCLQyBozXoQPqA==", "path": "system.reflection.emit.lightweight/4.7.0", "hashPath": "system.reflection.emit.lightweight.4.7.0.nupkg.sha512"}, "System.Reflection.Metadata/1.6.0": {"type": "package", "serviceable": true, "sha512": "sha512-COC1aiAJjCoA5GBF+QKL2uLqEBew4JsCkQmoHKbN3TlOZKa2fKLz5CpiRQKDz0RsAOEGsVKqOD5bomsXq/4STQ==", "path": "system.reflection.metadata/1.6.0", "hashPath": "system.reflection.metadata.1.6.0.nupkg.sha512"}, "System.Resources.ResourceManager/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-/zrcPkkWdZmI4F92gL/TPumP98AVDu/Wxr3CSJGQQ+XN6wbRZcyfSKVoPo17ilb3iOr0cCRqJInGwNMolqhS8A==", "path": "system.resources.resourcemanager/4.3.0", "hashPath": "system.resources.resourcemanager.4.3.0.nupkg.sha512"}, "System.Runtime/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-JufQi0vPQ0xGnAczR13AUFglDyVYt4Kqnz1AZaiKZ5+GICq0/1MH/mO/eAJHt/mHW1zjKBJd7kV26SrxddAhiw==", "path": "system.runtime/4.3.0", "hashPath": "system.runtime.4.3.0.nupkg.sha512"}, "System.Runtime.CompilerServices.Unsafe/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-/iUeP3tq1S0XdNNoMz5C9twLSrM/TH+qElHkXWaPvuNOt+99G75NrV0OS2EqHx5wMN7popYjpc8oTjC1y16DLg==", "path": "system.runtime.compilerservices.unsafe/6.0.0", "hashPath": "system.runtime.compilerservices.unsafe.6.0.0.nupkg.sha512"}, "System.Runtime.Extensions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-guW0uK0fn5fcJJ1tJVXYd7/1h5F+pea1r7FLSOz/f8vPEqbR2ZAknuRDvTQ8PzAilDveOxNjSfr0CHfIQfFk8g==", "path": "system.runtime.extensions/4.3.0", "hashPath": "system.runtime.extensions.4.3.0.nupkg.sha512"}, "System.Runtime.InteropServices/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-uv1ynXqiMK8mp1GM3jDqPCFN66eJ5w5XNomaK2XD+TuCroNTLFGeZ+WCmBMcBDyTFKou3P6cR6J/QsaqDp7fGQ==", "path": "system.runtime.interopservices/4.3.0", "hashPath": "system.runtime.interopservices.4.3.0.nupkg.sha512"}, "System.Runtime.InteropServices.RuntimeInformation/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-cbz4YJMqRDR7oLeMRbdYv7mYzc++17lNhScCX0goO2XpGWdvAt60CGN+FHdePUEHCe/Jy9jUlvNAiNdM+7jsOw==", "path": "system.runtime.interopservices.runtimeinformation/4.3.0", "hashPath": "system.runtime.interopservices.runtimeinformation.4.3.0.nupkg.sha512"}, "System.Runtime.Numerics/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-yMH+MfdzHjy17l2KESnPiF2dwq7T+xLnSJar7slyimAkUh/gTrS9/UQOtv7xarskJ2/XDSNvfLGOBQPjL7PaHQ==", "path": "system.runtime.numerics/4.3.0", "hashPath": "system.runtime.numerics.4.3.0.nupkg.sha512"}, "System.Security.AccessControl/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-vW8Eoq0TMyz5vAG/6ce483x/CP83fgm4SJe5P8Tb1tZaobcvPrbMEL7rhH1DRdrYbbb6F0vq3OlzmK0Pkwks5A==", "path": "system.security.accesscontrol/4.5.0", "hashPath": "system.security.accesscontrol.4.5.0.nupkg.sha512"}, "System.Security.Cryptography.Algorithms/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-W1kd2Y8mYSCgc3ULTAZ0hOP2dSdG5YauTb1089T0/kRcN2MpSAW1izOFROrJgxSlMn3ArsgHXagigyi+ibhevg==", "path": "system.security.cryptography.algorithms/4.3.0", "hashPath": "system.security.cryptography.algorithms.4.3.0.nupkg.sha512"}, "System.Security.Cryptography.Cng/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-WG3r7EyjUe9CMPFSs6bty5doUqT+q9pbI80hlNzo2SkPkZ4VTuZkGWjpp77JB8+uaL4DFPRdBsAY+DX3dBK92A==", "path": "system.security.cryptography.cng/4.5.0", "hashPath": "system.security.cryptography.cng.4.5.0.nupkg.sha512"}, "System.Security.Cryptography.Encoding/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-1DEWjZZly9ae9C79vFwqaO5kaOlI5q+3/55ohmq/7dpDyDfc8lYe7YVxJUZ5MF/NtbkRjwFRo14yM4OEo9EmDw==", "path": "system.security.cryptography.encoding/4.3.0", "hashPath": "system.security.cryptography.encoding.4.3.0.nupkg.sha512"}, "System.Security.Cryptography.Primitives/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-7bDIyVFNL/xKeFHjhobUAQqSpJq9YTOpbEs6mR233Et01STBMXNAc/V+BM6dwYGc95gVh/Zf+iVXWzj3mE8DWg==", "path": "system.security.cryptography.primitives/4.3.0", "hashPath": "system.security.cryptography.primitives.4.3.0.nupkg.sha512"}, "System.Security.Cryptography.X509Certificates/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-t2Tmu6Y2NtJ2um0RtcuhP7ZdNNxXEgUm2JeoA/0NvlMjAhKCnM1NX07TDl3244mVp3QU6LPEhT3HTtH1uF7IYw==", "path": "system.security.cryptography.x509certificates/4.3.0", "hashPath": "system.security.cryptography.x509certificates.4.3.0.nupkg.sha512"}, "System.Security.Cryptography.Xml/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-i2Jn6rGXR63J0zIklImGRkDIJL4b1NfPSEbIVHBlqoIb12lfXIigCbDRpDmIEzwSo/v1U5y/rYJdzZYSyCWxvg==", "path": "system.security.cryptography.xml/4.5.0", "hashPath": "system.security.cryptography.xml.4.5.0.nupkg.sha512"}, "System.Security.Permissions/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-9gdyuARhUR7H+p5CjyUB/zPk7/Xut3wUSP8NJQB6iZr8L3XUXTMdoLeVAg9N4rqF8oIpE7MpdqHdDHQ7XgJe0g==", "path": "system.security.permissions/4.5.0", "hashPath": "system.security.permissions.4.5.0.nupkg.sha512"}, "System.Security.Principal.Windows/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-U77HfRXlZlOeIXd//Yoj6Jnk8AXlbeisf1oq1os+hxOGVnuG+lGSfGqTwTZBoORFF6j/0q7HXIl8cqwQ9aUGqQ==", "path": "system.security.principal.windows/4.5.0", "hashPath": "system.security.principal.windows.4.5.0.nupkg.sha512"}, "System.Text.Encoding/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-BiIg+KWaSDOITze6jGQynxg64naAPtqGHBwDrLaCtixsa5bKiR8dpPOHA7ge3C0JJQizJE+sfkz1wV+BAKAYZw==", "path": "system.text.encoding/4.3.0", "hashPath": "system.text.encoding.4.3.0.nupkg.sha512"}, "System.Text.Encoding.CodePages/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-IRiEFUa5b/Gs5Egg8oqBVoywhtOeaO2KOx3j0RfcYY/raxqBuEK7NXRDgOwtYM8qbi+7S4RPXUbNt+ZxyY0/NQ==", "path": "system.text.encoding.codepages/4.3.0", "hashPath": "system.text.encoding.codepages.4.3.0.nupkg.sha512"}, "System.Text.Encoding.Extensions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-YVMK0Bt/A43RmwizJoZ22ei2nmrhobgeiYwFzC4YAN+nue8RF6djXDMog0UCn+brerQoYVyaS+ghy9P/MUVcmw==", "path": "system.text.encoding.extensions/4.3.0", "hashPath": "system.text.encoding.extensions.4.3.0.nupkg.sha512"}, "System.Text.Encodings.Web/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-yev/k9GHAEGx2Rg3/tU6MQh4HGBXJs70y7j1LaM1i/ER9po+6nnQ6RRqTJn1E7Xu0fbIFK80Nh5EoODxrbxwBQ==", "path": "system.text.encodings.web/8.0.0", "hashPath": "system.text.encodings.web.8.0.0.nupkg.sha512"}, "System.Text.Json/8.0.4": {"type": "package", "serviceable": true, "sha512": "sha512-bAkhgDJ88XTsqczoxEMliSrpijKZHhbJQldhAmObj/RbrN3sU5dcokuXmWJWsdQAhiMJ9bTayWsL1C9fbbCRhw==", "path": "system.text.json/8.0.4", "hashPath": "system.text.json.8.0.4.nupkg.sha512"}, "System.Threading/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-VkUS0kOBcUf3Wwm0TSbrevDDZ6BlM+b/HRiapRFWjM5O0NS0LviG0glKmFK+hhPDd1XFeSdU1GmlLhb2CoVpIw==", "path": "system.threading/4.3.0", "hashPath": "system.threading.4.3.0.nupkg.sha512"}, "System.Threading.Tasks/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-LbSxKEdOUhVe8BezB/9uOGGppt+nZf6e1VFyw6v3DN6lqitm0OSn2uXMOdtP0M3W4iMcqcivm2J6UgqiwwnXiA==", "path": "system.threading.tasks/4.3.0", "hashPath": "system.threading.tasks.4.3.0.nupkg.sha512"}, "System.Threading.Tasks.Dataflow/4.9.0": {"type": "package", "serviceable": true, "sha512": "sha512-dTS+3D/GtG2/Pvc3E5YzVvAa7aQJgLDlZDIzukMOJjYudVOQOUXEU68y6Zi3Nn/jqIeB5kOCwrGbQFAKHVzXEQ==", "path": "system.threading.tasks.dataflow/4.9.0", "hashPath": "system.threading.tasks.dataflow.4.9.0.nupkg.sha512"}, "System.Threading.Tasks.Extensions/4.5.4": {"type": "package", "serviceable": true, "sha512": "sha512-zteT+G8xuGu6mS+mzDzYXbzS7rd3K6Fjb9RiZlYlJPam2/hU7JCBZBVEcywNuR+oZ1ncTvc/cq0faRr3P01OVg==", "path": "system.threading.tasks.extensions/4.5.4", "hashPath": "system.threading.tasks.extensions.4.5.4.nupkg.sha512"}, "System.Threading.Tasks.Parallel/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-cbjBNZHf/vQCfcdhzx7knsiygoCKgxL8mZOeocXZn5gWhCdzHIq6bYNKWX0LAJCWYP7bds4yBK8p06YkP0oa0g==", "path": "system.threading.tasks.parallel/4.3.0", "hashPath": "system.threading.tasks.parallel.4.3.0.nupkg.sha512"}, "System.Threading.Thread/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-OHmbT+Zz065NKII/ZHcH9XO1dEuLGI1L2k7uYss+9C1jLxTC9kTZZuzUOyXHayRk+dft9CiDf3I/QZ0t8JKyBQ==", "path": "system.threading.thread/4.3.0", "hashPath": "system.threading.thread.4.3.0.nupkg.sha512"}, "System.ValueTuple/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-okurQJO6NRE/apDIP23ajJ0hpiNmJ+f0BwOlB/cSqTLQlw5upkf+5+96+iG2Jw40G1fCVCyPz/FhIABUjMR+RQ==", "path": "system.valuetuple/4.5.0", "hashPath": "system.valuetuple.4.5.0.nupkg.sha512"}, "System.Xml.ReaderWriter/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-GrprA+Z0RUXaR4N7/eW71j1rgMnEnEVlgii49GZyAjTH7uliMnrOU3HNFBr6fEDBCJCIdlVNq9hHbaDR621XBA==", "path": "system.xml.readerwriter/4.3.0", "hashPath": "system.xml.readerwriter.4.3.0.nupkg.sha512"}, "System.Xml.XDocument/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-5zJ0XDxAIg8iy+t4aMnQAu0MqVbqyvfoUVl1yDV61xdo3Vth45oA2FoY4pPkxYAH5f8ixpmTqXeEIya95x0aCQ==", "path": "system.xml.xdocument/4.3.0", "hashPath": "system.xml.xdocument.4.3.0.nupkg.sha512"}, "System.Xml.XmlDocument/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-lJ8AxvkX7GQxpC6GFCeBj8ThYVyQczx2+f/cWHJU8tjS7YfI6Cv6bon70jVEgs2CiFbmmM8b9j1oZVx0dSI2Ww==", "path": "system.xml.xmldocument/4.3.0", "hashPath": "system.xml.xmldocument.4.3.0.nupkg.sha512"}, "System.Xml.XPath/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-v1JQ5SETnQusqmS3RwStF7vwQ3L02imIzl++sewmt23VGygix04pEH+FCj1yWb+z4GDzKiljr1W7Wfvrx0YwgA==", "path": "system.xml.xpath/4.3.0", "hashPath": "system.xml.xpath.4.3.0.nupkg.sha512"}, "System.Xml.XPath.XDocument/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-jw9oHHEIVW53mHY9PgrQa98Xo2IZ0ZjrpdOTmtvk+Rvg4tq7dydmxdNqUvJ5YwjDqhn75mBXWttWjiKhWP53LQ==", "path": "system.xml.xpath.xdocument/4.3.0", "hashPath": "system.xml.xpath.xdocument.4.3.0.nupkg.sha512"}, "Microsoft.CSharp.Reference/4.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "mscorlib/4.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "PresentationCore/4.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.ComponentModel.Composition/4.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.ComponentModel.DataAnnotations/4.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Data.DataSetExtensions/4.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Data.OracleClient/4.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.IdentityModel/4.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Net/4.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Security/4.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.ServiceProcess/4.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Transactions/4.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Windows.Forms/4.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "WindowsBase/4.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.AppContext.Reference/4.1.2.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Console.Reference/4.0.2.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Core/4.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Data/4.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System/4.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Drawing/4.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.IO.Compression.Reference/4.2.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.IO.Compression.FileSystem/4.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.IO.Reference/4.1.2.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.IO.FileSystem.Reference/4.0.3.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.IO.FileSystem.Primitives.Reference/4.0.3.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Linq.Reference/4.1.2.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Linq.Expressions.Reference/4.1.2.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Net.Http.Reference/4.2.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Numerics/4.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Reflection.Reference/4.1.2.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Runtime.Reference/4.1.2.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Runtime.Extensions.Reference/4.1.2.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Runtime.InteropServices.Reference/4.1.2.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Runtime.InteropServices.RuntimeInformation.Reference/4.0.2.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Runtime.Serialization/4.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Security.Cryptography.Algorithms.Reference/4.3.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Security.Cryptography.Encoding.Reference/4.0.2.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Security.Cryptography.Primitives.Reference/4.0.2.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Security.Cryptography.X509Certificates.Reference/4.1.2.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Xml/4.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Xml.Linq/4.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Xml.ReaderWriter.Reference/4.1.1.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Win32.Primitives/0.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "netstandard/0.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Collections.Concurrent.Reference/0.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Collections.Reference/0.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Collections.NonGeneric/0.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Collections.Specialized/0.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.ComponentModel/0.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.ComponentModel.EventBasedAsync/0.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.ComponentModel.Primitives/0.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.ComponentModel.TypeConverter/0.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Data.Common/0.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Diagnostics.Contracts/0.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Diagnostics.Debug.Reference/0.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Diagnostics.FileVersionInfo.Reference/0.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Diagnostics.Process/0.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Diagnostics.StackTrace.Reference/0.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Diagnostics.TextWriterTraceListener/0.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Diagnostics.Tools.Reference/0.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Diagnostics.TraceSource/0.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Drawing.Primitives/0.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Dynamic.Runtime.Reference/0.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Globalization.Calendars/0.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Globalization.Reference/0.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Globalization.Extensions/0.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.IO.Compression.ZipFile/0.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.IO.FileSystem.DriveInfo/0.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.IO.FileSystem.Watcher/0.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.IO.IsolatedStorage/0.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.IO.MemoryMappedFiles/0.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.IO.Pipes/0.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.IO.UnmanagedMemoryStream/0.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Linq.Parallel/0.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Linq.Queryable/0.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Net.Http.Rtc/0.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Net.NameResolution/0.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Net.NetworkInformation/0.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Net.Ping/0.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Net.Primitives/0.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Net.Requests/0.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Net.Security/0.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Net.Sockets/0.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Net.WebHeaderCollection/0.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Net.WebSockets.Client/0.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Net.WebSockets/0.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.ObjectModel/0.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Reflection.Emit/0.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Reflection.Emit.ILGeneration.Reference/0.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Reflection.Emit.Lightweight.Reference/0.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Reflection.Extensions/0.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Reflection.Primitives/0.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Resources.Reader/0.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Resources.ResourceManager.Reference/0.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Resources.Writer/0.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Runtime.CompilerServices.VisualC/0.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Runtime.Handles/0.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Runtime.InteropServices.WindowsRuntime/0.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Runtime.Numerics.Reference/0.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Runtime.Serialization.Formatters/0.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Runtime.Serialization.Json/0.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Runtime.Serialization.Primitives/0.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Runtime.Serialization.Xml/0.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Security.Claims/0.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Security.Cryptography.Csp/0.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Security.Principal/0.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Security.SecureString/0.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.ServiceModel.Duplex/0.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.ServiceModel.Http/0.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.ServiceModel.NetTcp/0.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.ServiceModel.Primitives/0.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.ServiceModel.Security/0.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Text.Encoding.Reference/0.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Text.Encoding.Extensions.Reference/0.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Text.RegularExpressions/0.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Threading.Reference/0.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Threading.Overlapped/0.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Threading.Tasks.Reference/0.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Threading.Tasks.Parallel.Reference/0.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Threading.Thread.Reference/0.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Threading.ThreadPool/0.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Threading.Timer/0.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Xml.XDocument.Reference/0.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Xml.XmlDocument.Reference/0.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Xml.XmlSerializer/0.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Xml.XPath.Reference/0.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Xml.XPath.XDocument.Reference/0.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}}}