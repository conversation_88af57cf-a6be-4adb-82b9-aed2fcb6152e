{"MicrosoftAppType": "SingleTenant", "MicrosoftAppId": "24a397f4-16dd-4dae-8b8f-5368c3a81fed", "MicrosoftAppPassword": "****************************************", "MicrosoftAppTenantId": "653f2dfa-0545-4c9f-be75-7c35da274877", "BaseUrl": "https://accuremd.eastus.cloudapp.azure.com", "Teams": {"AppId": "24a397f4-16dd-4dae-8b8f-5368c3a81fed", "AppSecret": "****************************************", "TenantId": "653f2dfa-0545-4c9f-be75-7c35da274877"}, "MediaPlatform": {"InstanceInternalPort": 8445, "InstancePublicPort": 8445, "ServiceDnsName": "accuremd.eastus.cloudapp.azure.com", "ServiceCname": "accuremd.eastus.cloudapp.azure.com", "CertificateThumbprint": "2134d1e5132ba98473409be3e661d18238611118"}, "ConnectionStrings": {"DefaultConnection": "User Id=postgres.msnqzbbgpsyxjmywllkb;Password=*********;Server=aws-0-us-east-1.pooler.supabase.com;Port=5432;Database=postgres"}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Information", "Microsoft.Graph.Communications": "Information", "AccureMD.TeamsBot.MediaService": "Information"}, "Console": {"LogLevel": {"Default": "Information"}}}, "AllowedHosts": "*"}