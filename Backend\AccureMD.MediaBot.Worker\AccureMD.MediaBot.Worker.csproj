<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFrameworks>net48</TargetFrameworks>
    <RootNamespace>AccureMD.MediaBot.Worker</RootNamespace>
  </PropertyGroup>

  <PropertyGroup>
    <LangVersion>latest</LangVersion>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)|$(TargetFramework)|$(Platform)'=='Release|net48|AnyCPU'">
    <AllowUnsafeBlocks>true</AllowUnsafeBlocks>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)|$(TargetFramework)|$(Platform)'=='Debug|net48|AnyCPU'">
    <AllowUnsafeBlocks>true</AllowUnsafeBlocks>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.AspNetCore" Version="2.2.0" />
    <PackageReference Include="Microsoft.AspNetCore.HttpsPolicy" Version="2.2.0" />
    <PackageReference Include="Microsoft.AspNetCore.Mvc" Version="2.2.0" />
    <PackageReference Include="Microsoft.Graph.Communications.Calls" Version="1.2.0.850" />
    <PackageReference Include="Microsoft.Graph.Communications.Client" Version="1.2.0.850" />
    <PackageReference Include="Microsoft.Graph.Communications.Common" Version="1.2.0.850" />
    <PackageReference Include="Microsoft.Graph.Communications.Core" Version="1.2.0.850" />
    <PackageReference Include="Microsoft.IdentityModel.Clients.ActiveDirectory" Version="5.2.4" />
    <PackageReference Include="Microsoft.IdentityModel.Protocols.OpenIdConnect" Version="8.0.1" />
    <PackageReference Include="Microsoft.Psi.Imaging.Windows" Version="0.15.49.1-beta" />
    <PackageReference Include="Microsoft.Psi.Audio" Version="0.19.100.1-beta" />
    <PackageReference Include="Microsoft.Psi.Imaging" Version="0.18.72.1-beta" />

    <PackageReference Include="Microsoft.Skype.Bots.Media" Version="1.19.0.25-alpha" />
    <PackageReference Include="Microsoft.Graph.Communications.Calls.Media" Version="1.2.0.850" />
  </ItemGroup>

</Project>
