using Microsoft.Graph.Communications.Common;
using Microsoft.Graph.Communications.Common.Telemetry;
using System;
using System.Threading.Tasks;
using System.Timers;

namespace AccureMD.MediaBot.Worker.Bot
{
    /// <summary>
    /// The base class for handling heartbeats.
    /// </summary>
    public abstract class HeartbeatHandler : ObjectRootDisposable
    {
        /// <summary>
        /// The heartbeat timer
        /// </summary>
        private Timer heartbeatTimer;

        /// <summary>
        /// Initializes a new instance of the <see cref="HeartbeatHandler"/> class.
        /// </summary>
        /// <param name="frequency">The frequency of the heartbeat.</param>
        /// <param name="logger">The graph logger.</param>
        public HeartbeatHandler(TimeSpan frequency, IGraphLogger logger)
            : base(logger)
        {
            // initialize the timer
            var timer = new Timer(frequency.TotalMilliseconds);
            timer.Enabled = true;
            timer.AutoReset = true;
            timer.Elapsed += HeartbeatDetected;
            heartbeatTimer = timer;
        }

        /// <summary>
        /// This function is called whenever the heartbeat frequency has ellapsed.
        /// </summary>
        /// <param name="args">The elapsed event args.</param>
        /// <returns>The <see cref="Task"/>.</returns>
        protected abstract Task HeartbeatAsync(ElapsedEventArgs args);

        /// <inheritdoc/>
        protected override void Dispose(bool disposing)
        {
            base.Dispose(disposing);
            if (disposing)
            {
                heartbeatTimer?.Dispose();
                heartbeatTimer = null;
            }
        }

        /// <summary>
        /// The heartbeat detected.
        /// </summary>
        /// <param name="sender">The sender.</param>
        /// <param name="e">The elapsed event args.</param>
        private async void HeartbeatDetected(object sender, ElapsedEventArgs e)
        {
            try
            {
                await HeartbeatAsync(e).ConfigureAwait(false);
            }
            catch (Exception ex)
            {
                GraphLogger.Error(ex, "Heartbeat failed");
            }
        }
    }
}
