using Microsoft.Graph.Communications.Client;
using System.Collections.Concurrent;
using System.Threading.Tasks;

namespace AccureMD.MediaBot.Worker.Bot
{
    /// <summary>
    /// Interface for the bot service.
    /// </summary>
    public interface IBotService
    {
        /// <summary>
        /// Gets the collection of call handlers.
        /// </summary>
        ConcurrentDictionary<string, CallHandler> CallHandlers { get; }

        /// <summary>
        /// Gets the entry point for stateful bot.
        /// </summary>
        ICommunicationsClient Client { get; }

        /// <summary>
        /// End a particular call.
        /// </summary>
        /// <param name="callLegId">The call leg id.</param>
        /// <returns>The <see cref="Task" />.</returns>
        Task EndCallByCallLegIdAsync(string callLegId);

        /// <summary>
        /// Joins a call asynchronously.
        /// </summary>
        /// <param name="joinCallBody">The join call body.</param>
        /// <returns>The call that was joined.</returns>
        Task<Microsoft.Graph.Communications.Calls.ICall> JoinCallAsync(JoinCallBody joinCallBody);
    }

    /// <summary>
    /// Join call body.
    /// </summary>
    public class JoinCallBody
    {
        /// <summary>
        /// Gets or sets the join URL.
        /// </summary>
        public string JoinURL { get; set; } = string.Empty;

        /// <summary>
        /// Gets or sets the display name.
        /// </summary>
        public string DisplayName { get; set; } = string.Empty;
    }
}
