using Microsoft.Graph.Communications.Common.Telemetry;
using Microsoft.Psi;
using Microsoft.Psi.Audio;
using Microsoft.Psi.Components;
using Microsoft.Psi.Imaging;
using Microsoft.Skype.Bots.Media;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.InteropServices;
using Microsoft.Graph.Communications.Calls;
using Microsoft.Graph.Communications.Resources;

namespace AccureMD.MediaBot.Worker.Bot
{
    /// <summary>
    /// Media frame source component.
    /// </summary>
    public class MediaFrameSourceComponent : ISourceComponent
    {
        private readonly Pipeline pipeline;
        private readonly CallHandler callHandler;
        private readonly IGraphLogger logger;
        private readonly string logPrefix;
        private bool started = false;

        /// <summary>
        /// Initializes a new instance of the <see cref="MediaFrameSourceComponent"/> class.
        /// </summary>
        /// <param name="pipeline">The pipeline.</param>
        /// <param name="callHandler">The call handler.</param>
        /// <param name="logger">The logger.</param>
        public MediaFrameSourceComponent(Pipeline pipeline, CallHandler callHandler, IGraphLogger logger)
        {
            this.pipeline = pipeline;
            this.callHandler = callHandler;
            this.logger = logger;
            this.logPrefix = $"[MediaFrameSourceComponent {callHandler.Call.Id}]";

            this.Audio = this.pipeline.CreateEmitter<Dictionary<string, (AudioBuffer, DateTime)>>(this, nameof(this.Audio));
            this.Video = this.pipeline.CreateEmitter<Dictionary<string, (Shared<Image>, DateTime)>>(this, nameof(this.Video));
        }

        /// <summary>
        /// Gets audio stream.
        /// </summary>
        public Emitter<Dictionary<string, (AudioBuffer, DateTime)>> Audio { get; }

        /// <summary>
        /// Gets video stream.
        /// </summary>
        public Emitter<Dictionary<string, (Shared<Image>, DateTime)>> Video { get; }

        /// <inheritdoc />
        public void Start(Action<DateTime> notifyCompletionTime)
        {
            this.logger.Verbose($"{this.logPrefix} (started = {this.started}) Start called.");
            notifyCompletionTime(DateTime.MaxValue); // Notify that this is an infinite source component
            this.started = true;
        }

        /// <inheritdoc />
        public void Stop(DateTime finalOriginatingTime, Action notifyCompleted)
        {
            this.logger.Verbose($"{this.logPrefix} (started = {this.started}) Stop called.");
            this.started = false;
            notifyCompleted(); // No more messages will be posted after this.started = false
        }

        /// <summary>
        /// Received audio.
        /// </summary>
        /// <param name="audioFrame">Audio buffer.</param>
        public void Received(AudioMediaBuffer audioFrame)
        {
            if (audioFrame.Timestamp == 0)
            {
                this.logger.Warn($"Audio buffer timestamp is zero.");
                return;
            }

            var audioFormat = audioFrame.AudioFormat == Microsoft.Skype.Bots.Media.AudioFormat.Pcm44KStereo ?
                Microsoft.Psi.Audio.WaveFormat.Create16BitPcm(44000, 2) :
                Microsoft.Psi.Audio.WaveFormat.Create16kHz1Channel16BitPcm();

            var buffers = new Dictionary<string, (AudioBuffer, DateTime)>();
            var audioFrameTimestamp = new DateTime(1900, 1, 1, 0, 0, 0, DateTimeKind.Utc).AddTicks(audioFrame.Timestamp);

            if (audioFrame.UnmixedAudioBuffers != null)
            {
                foreach (var buffer in audioFrame.UnmixedAudioBuffers)
                {
                    var length = buffer.Length;
                    var data = new byte[length];
                    Marshal.Copy(buffer.Data, data, 0, (int)length);

                    var participant = GetParticipantFromMSI(this.callHandler.Call, buffer.ActiveSpeakerId);
                    var identity = TryGetParticipantIdentity(participant);
                    if (identity != null)
                    {
                        buffers.Add(identity.Id, (new AudioBuffer(data, audioFormat), audioFrameTimestamp));
                    }
                    else
                    {
                        this.logger.Warn($"Couldn't find participant for ActiveSpeakerId: {buffer.ActiveSpeakerId}");
                    }
                }
            }

            lock (this.Audio)
            {
                this.Audio.Post(buffers, this.pipeline.GetCurrentTime());
            }
        }

        /// <summary>
        /// Received image frame.
        /// </summary>
        /// <param name="videoFrame">Video frame.</param>
        /// <param name="id">Video frame MSI.</param>
        public void Received(VideoMediaBuffer videoFrame, uint id)
        {
            if (videoFrame.Timestamp == 0)
            {
                this.logger.Warn($"Video buffer timestamp is zero from sender: {id}");
                return;
            }

            var videoFormat = videoFrame.VideoFormat;
            var videoFrameTimestamp = new DateTime(1900, 1, 1, 0, 0, 0, DateTimeKind.Utc).AddTicks(videoFrame.Timestamp);

            var length = videoFormat.Width * videoFormat.Height * 12 / 8; // This is how to calculate NV12 buffer size
            if (length > videoFrame.Length)
            {
                this.logger.Warn($"Length of video frame not as expected: {id}");
                return;
            }

            byte[] data = new byte[length];
            Marshal.Copy(videoFrame.Data, data, 0, (int)length);

            using var sharedImage = Microsoft.Psi.Imaging.ImagePool.GetOrCreate(
                videoFormat.Width,
                videoFormat.Height,
                Microsoft.Psi.Imaging.PixelFormat.BGR_24bpp);

            var bgr = NV12toBGR(data, videoFormat.Width, videoFormat.Height);
            sharedImage.Resource.CopyFrom(bgr);

            var streams = new Dictionary<string, (Shared<Image>, DateTime)>();
            var participant = GetParticipantFromMSI(this.callHandler.Call, id);
            var identity = TryGetParticipantIdentity(participant);
            if (identity != null)
            {
                streams.Add(identity.Id, (sharedImage, videoFrameTimestamp));
            }
            else
            {
                this.logger.Warn($"Couldn't find participant for media source ID: {id}");
            }

            lock(this.Video)
            {
                this.Video.Post(streams, this.pipeline.GetCurrentTime());
            }
        }

        /// <summary>
        /// Gets the participant from MSI.
        /// </summary>
        /// <param name="call">The call.</param>
        /// <param name="msi">The MSI.</param>
        /// <returns>The participant.</returns>
        public static IParticipant GetParticipantFromMSI(ICall call, uint msi)
        {
            return call.Participants.SingleOrDefault(x => x.Resource.IsInLobby == false && x.Resource.MediaStreams.Any(y => y.SourceId == msi.ToString()));
        }

        /// <summary>
        /// Try to get the participant identity.
        /// </summary>
        /// <param name="participant">The participant.</param>
        /// <returns>The identity.</returns>
        public static Microsoft.Graph.Identity TryGetParticipantIdentity(IParticipant participant)
        {
            var identitySet = participant?.Resource?.Info?.Identity;
            var identity = identitySet?.User;

            if (identity == null &&
                identitySet != null &&
                identitySet.AdditionalData.Any(kvp => kvp.Value is Microsoft.Graph.Identity))
            {
                identity = identitySet.AdditionalData.Values.First(v => v is Microsoft.Graph.Identity) as Microsoft.Graph.Identity;
            }

            return identity;
        }

        /// <summary>
        /// Convert NV12 to BGR format.
        /// </summary>
        /// <param name="array">The NV12 data.</param>
        /// <param name="width">The width.</param>
        /// <param name="height">The height.</param>
        /// <returns>BGR data.</returns>
        private static byte[] NV12toBGR(byte[] array, int width, int height)
        {
            byte[] bgr = new byte[width * height * 3];
            int strideUV = width;
            int startUV = width * height;

            for (int i = 0; i < height; i++)
            {
                int row = i * width;
                for (int j = 0; j < width; j++)
                {
                    byte y = array[row + j];
                    byte u = array[startUV + ((i / 2) * strideUV) + (2 * (j / 2))];
                    byte v = array[startUV + ((i / 2) * strideUV) + (2 * (j / 2)) + 1];

                    // YUV to RGB conversion
                    byte b = (byte)Math.Max(0, Math.Min(255, (1.164 * (y - 16)) + (2.018 * (u - 128))));
                    byte g = (byte)Math.Max(0, Math.Min(255, (1.164 * (y - 16)) - (0.813 * (v - 128)) - (0.391 * (u - 128))));
                    byte r = (byte)Math.Max(0, Math.Min(255, (1.164 * (y - 16)) + (1.596 * (v - 128))));

                    int index = (i * width + j) * 3;
                    bgr[index] = b;
                    bgr[index + 1] = g;
                    bgr[index + 2] = r;
                }
            }

            return bgr;
        }
    }
}
