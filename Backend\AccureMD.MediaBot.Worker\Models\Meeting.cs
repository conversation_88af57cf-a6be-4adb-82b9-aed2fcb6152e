using System.Runtime.Serialization;

namespace AccureMD.MediaBot.Worker.Models
{
    /// <summary>
    /// Meeting context information.
    /// </summary>
    [DataContract]
    public class Meeting
    {
        /// <summary>
        /// Gets or sets the Tenant Id.
        /// </summary>
        [DataMember]
        public string Tid { get; set; } = string.Empty;

        /// <summary>
        /// Gets or sets the AAD object id of the user.
        /// </summary>
        [DataMember]
        public string Oid { get; set; } = string.Empty;

        /// <summary>
        /// Gets or sets the chat message id.
        /// </summary>
        [DataMember]
        public string MessageId { get; set; } = string.Empty;
    }
}
