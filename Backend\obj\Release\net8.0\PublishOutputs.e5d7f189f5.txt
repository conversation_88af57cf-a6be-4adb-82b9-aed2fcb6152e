D:\publish\AccureMD.TeamsBot.exe
D:\publish\appsettings.Development.json
D:\publish\appsettings.json
D:\publish\TeamsAppManifest\manifest.json
D:\publish\TeamsAppManifest\new4\manifest.json
D:\publish\test-auth.json
D:\publish\test-database.json
D:\publish\AccureMD.TeamsBot.dll
D:\publish\AccureMD.TeamsBot.deps.json
D:\publish\AccureMD.TeamsBot.runtimeconfig.json
D:\publish\AccureMD.TeamsBot.pdb
D:\publish\AdaptiveCards.dll
D:\publish\Azure.Core.dll
D:\publish\Azure.Identity.dll
D:\publish\Azure.Storage.Blobs.dll
D:\publish\Azure.Storage.Common.dll
D:\publish\Humanizer.dll
D:\publish\Microsoft.AspNetCore.Authentication.JwtBearer.dll
D:\publish\Microsoft.Bcl.AsyncInterfaces.dll
D:\publish\Microsoft.Bcl.Memory.dll
D:\publish\Microsoft.Bot.Builder.dll
D:\publish\Microsoft.Bot.Builder.Integration.AspNet.Core.dll
D:\publish\Microsoft.Bot.Configuration.dll
D:\publish\Microsoft.Bot.Connector.dll
D:\publish\Microsoft.Bot.Connector.Streaming.dll
D:\publish\Microsoft.Bot.Schema.dll
D:\publish\Microsoft.Bot.Streaming.dll
D:\publish\Microsoft.CodeAnalysis.dll
D:\publish\Microsoft.CodeAnalysis.CSharp.dll
D:\publish\Microsoft.CodeAnalysis.CSharp.Workspaces.dll
D:\publish\Microsoft.CodeAnalysis.Workspaces.dll
D:\publish\Microsoft.CognitiveServices.Speech.csharp.dll
D:\publish\Microsoft.EntityFrameworkCore.dll
D:\publish\Microsoft.EntityFrameworkCore.Abstractions.dll
D:\publish\Microsoft.EntityFrameworkCore.Design.dll
D:\publish\Microsoft.EntityFrameworkCore.Relational.dll
D:\publish\Microsoft.Extensions.DependencyModel.dll
D:\publish\Microsoft.Graph.dll
D:\publish\Microsoft.Graph.Core.dll
D:\publish\Microsoft.Identity.Client.dll
D:\publish\Microsoft.Identity.Client.Extensions.Msal.dll
D:\publish\Microsoft.IdentityModel.Abstractions.dll
D:\publish\Microsoft.IdentityModel.JsonWebTokens.dll
D:\publish\Microsoft.IdentityModel.Logging.dll
D:\publish\Microsoft.IdentityModel.Protocols.dll
D:\publish\Microsoft.IdentityModel.Protocols.OpenIdConnect.dll
D:\publish\Microsoft.IdentityModel.Tokens.dll
D:\publish\Microsoft.IdentityModel.Validators.dll
D:\publish\Microsoft.Kiota.Abstractions.dll
D:\publish\Microsoft.Kiota.Authentication.Azure.dll
D:\publish\Microsoft.Kiota.Http.HttpClientLibrary.dll
D:\publish\Microsoft.Kiota.Serialization.Form.dll
D:\publish\Microsoft.Kiota.Serialization.Json.dll
D:\publish\Microsoft.Kiota.Serialization.Multipart.dll
D:\publish\Microsoft.Kiota.Serialization.Text.dll
D:\publish\Microsoft.Rest.ClientRuntime.dll
D:\publish\Mono.TextTemplating.dll
D:\publish\Newtonsoft.Json.dll
D:\publish\Npgsql.dll
D:\publish\Npgsql.EntityFrameworkCore.PostgreSQL.dll
D:\publish\Std.UriTemplate.dll
D:\publish\System.ClientModel.dll
D:\publish\System.CodeDom.dll
D:\publish\System.Composition.AttributedModel.dll
D:\publish\System.Composition.Convention.dll
D:\publish\System.Composition.Hosting.dll
D:\publish\System.Composition.Runtime.dll
D:\publish\System.Composition.TypedParts.dll
D:\publish\System.IdentityModel.Tokens.Jwt.dll
D:\publish\System.IO.Hashing.dll
D:\publish\System.Memory.Data.dll
D:\publish\System.Security.Cryptography.ProtectedData.dll
D:\publish\cs\Microsoft.CodeAnalysis.resources.dll
D:\publish\de\Microsoft.CodeAnalysis.resources.dll
D:\publish\es\Microsoft.CodeAnalysis.resources.dll
D:\publish\fr\Microsoft.CodeAnalysis.resources.dll
D:\publish\it\Microsoft.CodeAnalysis.resources.dll
D:\publish\ja\Microsoft.CodeAnalysis.resources.dll
D:\publish\ko\Microsoft.CodeAnalysis.resources.dll
D:\publish\pl\Microsoft.CodeAnalysis.resources.dll
D:\publish\pt-BR\Microsoft.CodeAnalysis.resources.dll
D:\publish\ru\Microsoft.CodeAnalysis.resources.dll
D:\publish\tr\Microsoft.CodeAnalysis.resources.dll
D:\publish\zh-Hans\Microsoft.CodeAnalysis.resources.dll
D:\publish\zh-Hant\Microsoft.CodeAnalysis.resources.dll
D:\publish\cs\Microsoft.CodeAnalysis.CSharp.resources.dll
D:\publish\de\Microsoft.CodeAnalysis.CSharp.resources.dll
D:\publish\es\Microsoft.CodeAnalysis.CSharp.resources.dll
D:\publish\fr\Microsoft.CodeAnalysis.CSharp.resources.dll
D:\publish\it\Microsoft.CodeAnalysis.CSharp.resources.dll
D:\publish\ja\Microsoft.CodeAnalysis.CSharp.resources.dll
D:\publish\ko\Microsoft.CodeAnalysis.CSharp.resources.dll
D:\publish\pl\Microsoft.CodeAnalysis.CSharp.resources.dll
D:\publish\pt-BR\Microsoft.CodeAnalysis.CSharp.resources.dll
D:\publish\ru\Microsoft.CodeAnalysis.CSharp.resources.dll
D:\publish\tr\Microsoft.CodeAnalysis.CSharp.resources.dll
D:\publish\zh-Hans\Microsoft.CodeAnalysis.CSharp.resources.dll
D:\publish\zh-Hant\Microsoft.CodeAnalysis.CSharp.resources.dll
D:\publish\cs\Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll
D:\publish\de\Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll
D:\publish\es\Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll
D:\publish\fr\Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll
D:\publish\it\Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll
D:\publish\ja\Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll
D:\publish\ko\Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll
D:\publish\pl\Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll
D:\publish\pt-BR\Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll
D:\publish\ru\Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll
D:\publish\tr\Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll
D:\publish\zh-Hans\Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll
D:\publish\zh-Hant\Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll
D:\publish\cs\Microsoft.CodeAnalysis.Workspaces.resources.dll
D:\publish\de\Microsoft.CodeAnalysis.Workspaces.resources.dll
D:\publish\es\Microsoft.CodeAnalysis.Workspaces.resources.dll
D:\publish\fr\Microsoft.CodeAnalysis.Workspaces.resources.dll
D:\publish\it\Microsoft.CodeAnalysis.Workspaces.resources.dll
D:\publish\ja\Microsoft.CodeAnalysis.Workspaces.resources.dll
D:\publish\ko\Microsoft.CodeAnalysis.Workspaces.resources.dll
D:\publish\pl\Microsoft.CodeAnalysis.Workspaces.resources.dll
D:\publish\pt-BR\Microsoft.CodeAnalysis.Workspaces.resources.dll
D:\publish\ru\Microsoft.CodeAnalysis.Workspaces.resources.dll
D:\publish\tr\Microsoft.CodeAnalysis.Workspaces.resources.dll
D:\publish\zh-Hans\Microsoft.CodeAnalysis.Workspaces.resources.dll
D:\publish\zh-Hant\Microsoft.CodeAnalysis.Workspaces.resources.dll
D:\publish\runtimes\android-arm\native\libMicrosoft.CognitiveServices.Speech.core.so
D:\publish\runtimes\android-arm\native\libMicrosoft.CognitiveServices.Speech.extension.audio.sys.so
D:\publish\runtimes\android-arm\native\libMicrosoft.CognitiveServices.Speech.extension.kws.ort.so
D:\publish\runtimes\android-arm\native\libMicrosoft.CognitiveServices.Speech.extension.kws.so
D:\publish\runtimes\android-arm\native\libMicrosoft.CognitiveServices.Speech.extension.lu.so
D:\publish\runtimes\android-arm64\native\libMicrosoft.CognitiveServices.Speech.core.so
D:\publish\runtimes\android-arm64\native\libMicrosoft.CognitiveServices.Speech.extension.audio.sys.so
D:\publish\runtimes\android-arm64\native\libMicrosoft.CognitiveServices.Speech.extension.kws.ort.so
D:\publish\runtimes\android-arm64\native\libMicrosoft.CognitiveServices.Speech.extension.kws.so
D:\publish\runtimes\android-arm64\native\libMicrosoft.CognitiveServices.Speech.extension.lu.so
D:\publish\runtimes\android-x64\native\libMicrosoft.CognitiveServices.Speech.core.so
D:\publish\runtimes\android-x64\native\libMicrosoft.CognitiveServices.Speech.extension.audio.sys.so
D:\publish\runtimes\android-x64\native\libMicrosoft.CognitiveServices.Speech.extension.kws.ort.so
D:\publish\runtimes\android-x64\native\libMicrosoft.CognitiveServices.Speech.extension.kws.so
D:\publish\runtimes\android-x64\native\libMicrosoft.CognitiveServices.Speech.extension.lu.so
D:\publish\runtimes\android-x86\native\libMicrosoft.CognitiveServices.Speech.core.so
D:\publish\runtimes\android-x86\native\libMicrosoft.CognitiveServices.Speech.extension.audio.sys.so
D:\publish\runtimes\android-x86\native\libMicrosoft.CognitiveServices.Speech.extension.kws.ort.so
D:\publish\runtimes\android-x86\native\libMicrosoft.CognitiveServices.Speech.extension.kws.so
D:\publish\runtimes\android-x86\native\libMicrosoft.CognitiveServices.Speech.extension.lu.so
D:\publish\runtimes\centos7-x64\native\libMicrosoft.CognitiveServices.Speech.core.so
D:\publish\runtimes\centos7-x64\native\libMicrosoft.CognitiveServices.Speech.extension.audio.sys.so
D:\publish\runtimes\centos7-x64\native\libMicrosoft.CognitiveServices.Speech.extension.codec.so
D:\publish\runtimes\centos7-x64\native\libMicrosoft.CognitiveServices.Speech.extension.kws.ort.so
D:\publish\runtimes\centos7-x64\native\libMicrosoft.CognitiveServices.Speech.extension.kws.so
D:\publish\runtimes\centos7-x64\native\libMicrosoft.CognitiveServices.Speech.extension.lu.so
D:\publish\runtimes\ios-arm64\native\libMicrosoft.CognitiveServices.Speech.core.a
D:\publish\runtimes\iossimulator-arm64\native\libMicrosoft.CognitiveServices.Speech.core.a
D:\publish\runtimes\iossimulator-x64\native\libMicrosoft.CognitiveServices.Speech.core.a
D:\publish\runtimes\linux-arm\lib\netstandard2.0\Microsoft.CognitiveServices.Speech.csharp.dll
D:\publish\runtimes\linux-arm\native\libMicrosoft.CognitiveServices.Speech.core.so
D:\publish\runtimes\linux-arm\native\libMicrosoft.CognitiveServices.Speech.extension.audio.sys.so
D:\publish\runtimes\linux-arm\native\libMicrosoft.CognitiveServices.Speech.extension.codec.so
D:\publish\runtimes\linux-arm\native\libMicrosoft.CognitiveServices.Speech.extension.kws.ort.so
D:\publish\runtimes\linux-arm\native\libMicrosoft.CognitiveServices.Speech.extension.kws.so
D:\publish\runtimes\linux-arm\native\libMicrosoft.CognitiveServices.Speech.extension.lu.so
D:\publish\runtimes\linux-arm64\lib\netstandard2.0\Microsoft.CognitiveServices.Speech.csharp.dll
D:\publish\runtimes\linux-arm64\native\libMicrosoft.CognitiveServices.Speech.core.so
D:\publish\runtimes\linux-arm64\native\libMicrosoft.CognitiveServices.Speech.extension.audio.sys.so
D:\publish\runtimes\linux-arm64\native\libMicrosoft.CognitiveServices.Speech.extension.codec.so
D:\publish\runtimes\linux-arm64\native\libMicrosoft.CognitiveServices.Speech.extension.kws.ort.so
D:\publish\runtimes\linux-arm64\native\libMicrosoft.CognitiveServices.Speech.extension.kws.so
D:\publish\runtimes\linux-arm64\native\libMicrosoft.CognitiveServices.Speech.extension.lu.so
D:\publish\runtimes\linux-x64\lib\netstandard2.0\Microsoft.CognitiveServices.Speech.csharp.dll
D:\publish\runtimes\linux-x64\native\libMicrosoft.CognitiveServices.Speech.core.so
D:\publish\runtimes\linux-x64\native\libMicrosoft.CognitiveServices.Speech.extension.audio.sys.so
D:\publish\runtimes\linux-x64\native\libMicrosoft.CognitiveServices.Speech.extension.codec.so
D:\publish\runtimes\linux-x64\native\libMicrosoft.CognitiveServices.Speech.extension.kws.ort.so
D:\publish\runtimes\linux-x64\native\libMicrosoft.CognitiveServices.Speech.extension.kws.so
D:\publish\runtimes\linux-x64\native\libMicrosoft.CognitiveServices.Speech.extension.lu.so
D:\publish\runtimes\maccatalyst-arm64\native\libMicrosoft.CognitiveServices.Speech.core.a
D:\publish\runtimes\maccatalyst-x64\native\libMicrosoft.CognitiveServices.Speech.core.a
D:\publish\runtimes\osx-arm64\lib\netstandard2.0\Microsoft.CognitiveServices.Speech.csharp.dll
D:\publish\runtimes\osx-arm64\native\libMicrosoft.CognitiveServices.Speech.core.dylib
D:\publish\runtimes\osx-x64\lib\netstandard2.0\Microsoft.CognitiveServices.Speech.csharp.dll
D:\publish\runtimes\osx-x64\native\libMicrosoft.CognitiveServices.Speech.core.dylib
D:\publish\runtimes\win-arm\native\Microsoft.CognitiveServices.Speech.core.dll
D:\publish\runtimes\win-arm\native\Microsoft.CognitiveServices.Speech.extension.audio.sys.dll
D:\publish\runtimes\win-arm\native\Microsoft.CognitiveServices.Speech.extension.kws.dll
D:\publish\runtimes\win-arm\native\Microsoft.CognitiveServices.Speech.extension.kws.ort.dll
D:\publish\runtimes\win-arm\native\Microsoft.CognitiveServices.Speech.extension.lu.dll
D:\publish\runtimes\win-arm64\native\Microsoft.CognitiveServices.Speech.core.dll
D:\publish\runtimes\win-arm64\native\Microsoft.CognitiveServices.Speech.extension.audio.sys.dll
D:\publish\runtimes\win-arm64\native\Microsoft.CognitiveServices.Speech.extension.kws.dll
D:\publish\runtimes\win-arm64\native\Microsoft.CognitiveServices.Speech.extension.kws.ort.dll
D:\publish\runtimes\win-arm64\native\Microsoft.CognitiveServices.Speech.extension.lu.dll
D:\publish\runtimes\win-x64\native\Microsoft.CognitiveServices.Speech.core.dll
D:\publish\runtimes\win-x64\native\Microsoft.CognitiveServices.Speech.extension.audio.sys.dll
D:\publish\runtimes\win-x64\native\Microsoft.CognitiveServices.Speech.extension.codec.dll
D:\publish\runtimes\win-x64\native\Microsoft.CognitiveServices.Speech.extension.kws.dll
D:\publish\runtimes\win-x64\native\Microsoft.CognitiveServices.Speech.extension.kws.ort.dll
D:\publish\runtimes\win-x64\native\Microsoft.CognitiveServices.Speech.extension.lu.dll
D:\publish\runtimes\win-x86\native\Microsoft.CognitiveServices.Speech.core.dll
D:\publish\runtimes\win-x86\native\Microsoft.CognitiveServices.Speech.extension.audio.sys.dll
D:\publish\runtimes\win-x86\native\Microsoft.CognitiveServices.Speech.extension.codec.dll
D:\publish\runtimes\win-x86\native\Microsoft.CognitiveServices.Speech.extension.kws.dll
D:\publish\runtimes\win-x86\native\Microsoft.CognitiveServices.Speech.extension.kws.ort.dll
D:\publish\runtimes\win-x86\native\Microsoft.CognitiveServices.Speech.extension.lu.dll
D:\publish\runtimes\win\lib\netstandard2.0\System.Security.Cryptography.ProtectedData.dll
