using Microsoft.Graph.Communications.Common.Telemetry;
using Microsoft.Skype.Bots.Media;
using System;
using System.Collections.Generic;
using System.Net;
using System.Security.Cryptography.X509Certificates;

namespace AccureMD.MediaBot.Worker.Models
{
    /// <summary>
    /// Bot configuration settings.
    /// </summary>
    public class BotConfiguration
    {
        /// <summary>
        /// Gets or sets the name of the bot.
        /// </summary>
        public string BotName { get; set; } = string.Empty;

        /// <summary>
        /// Gets or sets the AAD application ID.
        /// </summary>
        public string AadAppId { get; set; } = string.Empty;

        /// <summary>
        /// Gets or sets the AAD application secret.
        /// </summary>
        public string AadAppSecret { get; set; } = string.Empty;

        /// <summary>
        /// Gets or sets the service DNS name.
        /// </summary>
        public string ServiceDnsName { get; set; } = string.Empty;

        /// <summary>
        /// Gets or sets the service CNAME.
        /// </summary>
        public string ServiceCname { get; set; } = string.Empty;

        /// <summary>
        /// Gets or sets the certificate thumbprint.
        /// </summary>
        public string CertificateThumbprint { get; set; } = string.Empty;

        /// <summary>
        /// Gets or sets the instance public port.
        /// </summary>
        public int InstancePublicPort { get; set; }

        /// <summary>
        /// Gets or sets the instance internal port.
        /// </summary>
        public int InstanceInternalPort { get; set; }

        /// <summary>
        /// Gets or sets the call signaling port.
        /// </summary>
        public int CallSignalingPort { get; set; }

        /// <summary>
        /// Gets or sets the media service FQDN.
        /// </summary>
        public string MediaServiceFQDN { get; set; } = string.Empty;

        /// <summary>
        /// Gets or sets the place call endpoint URL.
        /// </summary>
        public string PlaceCallEndpointUrl { get; set; } = string.Empty;

        /// <summary>
        /// Gets or sets the Psi store directory.
        /// </summary>
        public string PsiStoreDirectory { get; set; } = string.Empty;

        /// <summary>
        /// Gets the call control base URL.
        /// </summary>
        public Uri CallControlBaseUrl { get; private set; }

        /// <summary>
        /// Gets the call control listening URLs.
        /// </summary>
        public IEnumerable<string> CallControlListeningUrls { get; private set; }

        /// <summary>
        /// Gets the media platform settings.
        /// </summary>
        public MediaPlatformSettings MediaPlatformSettings { get; private set; }

        /// <summary>
        /// Initializes the configuration.
        /// </summary>
        public void Initialize()
        {
            if (string.IsNullOrWhiteSpace(ServiceCname))
            {
                ServiceCname = ServiceDnsName;
            }

            X509Certificate2 defaultCertificate = this.GetCertificateFromStore();

            List<string> controlListenUris = new List<string>();
            var baseDomain = "+";

            // Create structured config objects for service.
            this.CallControlBaseUrl = new Uri($"https://{this.ServiceCname}/api/calling");

            controlListenUris.Add($"https://{baseDomain}:{CallSignalingPort}/");
            controlListenUris.Add($"http://{baseDomain}:{CallSignalingPort + 1}/"); // required for graceful termination

            this.CallControlListeningUrls = controlListenUris;

            this.MediaPlatformSettings = new MediaPlatformSettings()
            {
                MediaPlatformInstanceSettings = new MediaPlatformInstanceSettings()
                {
                    CertificateThumbprint = defaultCertificate.Thumbprint,
                    InstanceInternalPort = InstanceInternalPort,
                    InstancePublicIPAddress = IPAddress.Any,
                    InstancePublicPort = InstancePublicPort,
                    ServiceFqdn = this.MediaServiceFQDN
                },
                ApplicationId = this.AadAppId,
            };
        }

        /// <summary>
        /// Gets the certificate from the certificate store.
        /// </summary>
        /// <returns>The certificate.</returns>
        private X509Certificate2 GetCertificateFromStore()
        {
            X509Store store = new X509Store(StoreName.My, StoreLocation.LocalMachine);
            store.Open(OpenFlags.ReadOnly);
            try
            {
                X509Certificate2Collection certificateCollection = store.Certificates.Find(
                    X509FindType.FindByThumbprint,
                    this.CertificateThumbprint,
                    false);

                if (certificateCollection.Count == 0)
                {
                    throw new ArgumentException($"Could not find the certificate with thumbprint {this.CertificateThumbprint} in the local machine store.");
                }

                return certificateCollection[0];
            }
            finally
            {
                store.Close();
            }
        }
    }
}
