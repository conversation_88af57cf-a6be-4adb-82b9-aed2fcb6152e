# AccureMD MediaBot Setup Guide for Windows VM

This guide provides step-by-step instructions for setting up the AccureMD MediaBot on a Windows VM with proper hosting configuration.

## Prerequisites

### System Requirements
- Windows Server 2019 or later (or Windows 10/11 Pro)
- .NET Framework 4.8 or later
- IIS (Internet Information Services)
- SSL Certificate for HTTPS
- Public IP address with DNS configuration
- Minimum 4GB RAM, 2 CPU cores
- At least 10GB free disk space

### Azure/Network Configuration
- VM with public IP: `*************`
- DNS name: `accuremd.eastus.cloudapp.azure.com`
- Required ports open:
  - 443 (HTTPS)
  - 9441 (Call Signaling)
  - 8445 (Internal Media)

## Step 1: Install Prerequisites

### 1.1 Install .NET Framework 4.8
1. Download from Microsoft official site
2. Install and restart if required

### 1.2 Install IIS
1. Open "Turn Windows features on or off"
2. Enable "Internet Information Services"
3. Enable "ASP.NET 4.8" under IIS features
4. Restart if required

### 1.3 Install SSL Certificate
1. Obtain SSL certificate for `accuremd.eastus.cloudapp.azure.com`
2. Install certificate in Local Machine > Personal store
3. Note the certificate thumbprint for configuration

## Step 2: Configure Azure App Registration

### 2.1 App Registration Settings
- App ID: `24a397f4-16dd-4dae-8b8f-5368c3a81fed`
- Tenant ID: `653f2dfa-0545-4c9f-be75-7c35da274877`
- App Secret: `****************************************`

### 2.2 Required API Permissions
- Microsoft Graph:
  - Calls.AccessMedia.All (Application)
  - Calls.Initiate.All (Application)
  - Calls.JoinGroupCall.All (Application)
  - Calls.JoinGroupCallAsGuest.All (Application)

### 2.3 Redirect URIs
Add the following redirect URIs:
- `https://accuremd.eastus.cloudapp.azure.com:9441/api/calling`
- `https://accuremd.eastus.cloudapp.azure.com:9441/api/calling/notification`

## Step 3: Deploy MediaBot Application

### 3.1 Build the Application
```bash
# On development machine
cd Backend/AccureMD.MediaBot.Worker
dotnet publish -c Release -f net48 -o ./publish
```

### 3.2 Copy Files to VM
1. Create directory: `C:\MediaBot\`
2. Copy all files from `./publish` to `C:\MediaBot\`
3. Create directory: `C:\MediaBot\Stores` (for Psi data storage)

### 3.3 Configure Application Settings
Edit `C:\MediaBot\appsettings.json`:

```json
{
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft.AspNetCore": "Warning",
      "Microsoft.Graph": "Information",
      "AccureMD.MediaBot": "Information"
    }
  },
  "AllowedHosts": "*",
  "BotConfiguration": {
    "BotName": "AccureMD MediaBot",
    "AadAppId": "24a397f4-16dd-4dae-8b8f-5368c3a81fed",
    "AadAppSecret": "****************************************",
    "ServiceCname": "accuremd.eastus.cloudapp.azure.com",
    "MediaServiceFQDN": "accuremd.eastus.cloudapp.azure.com",
    "ServiceDnsName": "accuremd.eastus.cloudapp.azure.com",
    "CertificateThumbprint": "YOUR_CERTIFICATE_THUMBPRINT_HERE",
    "InstancePublicPort": 443,
    "CallSignalingPort": 9441,
    "InstanceInternalPort": 8445,
    "PlaceCallEndpointUrl": "https://graph.microsoft.com/v1.0",
    "PsiStoreDirectory": "C:\\MediaBot\\Stores"
  },
  "Kestrel": {
    "Endpoints": {
      "Http": {
        "Url": "http://0.0.0.0:8445"
      },
      "Https": {
        "Url": "https://0.0.0.0:9441"
      }
    }
  }
}
```

## Step 4: Configure Windows Service

### 4.1 Install as Windows Service
1. Open PowerShell as Administrator
2. Navigate to `C:\MediaBot\`
3. Run the following commands:

```powershell
# Install the service
sc create "AccureMD MediaBot" binPath="C:\MediaBot\AccureMD.MediaBot.Worker.exe" start=auto
sc description "AccureMD MediaBot" "AccureMD Teams MediaBot for audio/video processing and transcription"

# Configure service recovery
sc failure "AccureMD MediaBot" reset=86400 actions=restart/5000/restart/5000/restart/5000

# Start the service
sc start "AccureMD MediaBot"
```

### 4.2 Verify Service Status
```powershell
# Check service status
sc query "AccureMD MediaBot"

# View service logs
Get-EventLog -LogName Application -Source "AccureMD MediaBot" -Newest 10
```

## Step 5: Configure Firewall

### 5.1 Windows Firewall Rules
```powershell
# Allow inbound traffic on required ports
New-NetFirewallRule -DisplayName "MediaBot HTTPS" -Direction Inbound -Protocol TCP -LocalPort 9441 -Action Allow
New-NetFirewallRule -DisplayName "MediaBot HTTP" -Direction Inbound -Protocol TCP -LocalPort 8445 -Action Allow
```

### 5.2 Azure Network Security Group
Ensure the following ports are open in Azure NSG:
- 443/TCP (HTTPS)
- 9441/TCP (Call Signaling)
- 8445/TCP (Internal Media)

## Step 6: Configure Backend Integration

### 6.1 Update Backend Configuration
In the main backend `appsettings.json`, update:

```json
{
  "MediaService": {
    "BaseUrl": "https://accuremd.eastus.cloudapp.azure.com:9441",
    "AllowSelfSigned": false
  }
}
```

### 6.2 Test Integration
1. Start the main backend application
2. Trigger a meeting join request
3. Verify MediaBot receives the request at `/api/calling/join`

## Step 7: Monitoring and Troubleshooting

### 7.1 Health Check Endpoints
- Health: `https://accuremd.eastus.cloudapp.azure.com:9441/api/calling/health`
- Active Calls: `https://accuremd.eastus.cloudapp.azure.com:9441/api/calling/calls`

### 7.2 Log Locations
- Application logs: Windows Event Viewer > Application
- Psi store data: `C:\MediaBot\Stores\`
- Service logs: `C:\MediaBot\logs\` (if configured)

### 7.3 Common Issues

#### Issue: Certificate not found
**Solution**: Verify certificate thumbprint in configuration matches installed certificate

#### Issue: Port binding errors
**Solution**: Ensure no other applications are using ports 8445 or 9441

#### Issue: Authentication failures
**Solution**: Verify Azure app registration permissions and secrets

#### Issue: Media processing errors
**Solution**: Check Psi store directory permissions and disk space

## Step 8: Production Considerations

### 8.1 Security
- Use strong SSL certificates
- Regularly rotate app secrets
- Monitor access logs
- Implement network segmentation

### 8.2 Performance
- Monitor CPU and memory usage
- Configure appropriate Psi store cleanup
- Scale VM resources as needed
- Implement load balancing for multiple instances

### 8.3 Backup and Recovery
- Backup configuration files
- Monitor service health
- Implement automated restart policies
- Plan for disaster recovery

## Step 9: Maintenance

### 9.1 Regular Tasks
- Update SSL certificates before expiration
- Monitor disk space in Psi store directory
- Review and rotate Azure app secrets
- Update Windows and .NET Framework

### 9.2 Monitoring
- Set up alerts for service failures
- Monitor call success rates
- Track media processing performance
- Monitor network connectivity

## Support and Troubleshooting

For issues with the MediaBot setup:
1. Check Windows Event Viewer for application errors
2. Verify network connectivity and firewall rules
3. Test Azure app registration permissions
4. Review configuration file syntax
5. Check certificate installation and thumbprint

## API Endpoints

The MediaBot exposes the following endpoints:

- `POST /api/calling/join` - Join a Teams meeting
- `DELETE /api/calling/calls/{callId}` - End a specific call
- `GET /api/calling/calls` - List active calls
- `GET /api/calling/health` - Health check
- `POST /api/calling` - Handle incoming call notifications
- `POST /api/calling/notification` - Handle call state notifications

## Configuration Reference

### Required Configuration Values
- `AadAppId`: Azure app registration ID
- `AadAppSecret`: Azure app secret
- `ServiceCname`: Public DNS name
- `CertificateThumbprint`: SSL certificate thumbprint
- `CallSignalingPort`: Port for call signaling (9441)
- `InstanceInternalPort`: Internal media port (8445)
- `PsiStoreDirectory`: Directory for storing media data

### Optional Configuration Values
- `BotName`: Display name for the bot
- `InstancePublicPort`: Public port (443)
- `PlaceCallEndpointUrl`: Microsoft Graph endpoint

This completes the setup guide for the AccureMD MediaBot on Windows VM.
