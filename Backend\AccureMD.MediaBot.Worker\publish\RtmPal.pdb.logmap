007b0bd2:"[cid:%s] pal_audio: removing virtual device, eType = %d, privateId = %ls, device = %p, hr = 0x%x","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDevicePlatformWASAPI.cpp";"RtcPalDevicePlatformWASAPI::UnregisterVirtualAudioDevices"
01655400:"[cid:%s] Glitch occurs: QPCPos=%llu, device position=%llu, glitch info=%d","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/VirtualDeviceExtension.cpp";"RtcPalExtendedVirtualAudioRenderDeviceImpl::GetGlitchInfo"
0180eacb:"[cid:%s] ConnectEx: SO_UPDATE_CONNECT_CONTEXT fails 0x%x OVERLAPPED %p","D:/a/_work/1/s/MSRTC/msrtc/src/rtcpal/network/windows/WinSockNT.cpp";"RtcPalSocket::OnConnectExCompletion"
026071c5:"[cid:%s] Wrong device type %d!","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/VirtualPlatform.cpp";"RtcPalCreateVirtualAudioDevice"
0290b9c7:"[cid:%s] Flush ETW Event: EventsLost %lu LogBuffersLost %lu NumberOfBuffers %lu FreeBuffers %lu BuffersWritten %lu","D:/a/_work/1/s/MSRTC/msrtc/src/rtcpal/tracing/windows/WppConsumer.cpp";"DumpEtwStatistics"
029f68c6:"[cid:%s] Failed to create GetPnpNameAndRelatedFieldsThreadFuncParams","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDeviceAudioEnumerationAsyncHelpers.cpp";"RtcPalDeviceAudioEnumerationHelperWASAPI::GetPnpNameAndRelatedFieldsWithTimeout"
02bedf01:"[cid:%s] Invalid arg. !pTimeOutCountCurrentDevice. Not querying device gain fields. Will use default values","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDeviceAudioEnumerationHelperWASAPI.cpp";"RtcPalDeviceAudioEnumerationHelperWASAPI::PopulateNonCriticalAudioDeviceInfo"
02f7da5d:"[cid:%s] Fail to initialize trace route module: 0x%x","D:/a/_work/1/s/MSRTC/msrtc/src/rtcpal/network/windows/TraceRtSocket.cpp";"RtcPalTraceRtStartup"
030b7d10:"[cid:%s] MFStartup fail hr=0x%x","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/mfhelper/mfhelper.cpp";"RtcPalMFHasAVCapture"
03304dbb:"[cid:%s] skip one device since category check failed, type=%d, id=%ls, uRawFormFactor=%d, ulCategoryMask=%d","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDevicePlatformWASAPI.cpp";"RtcPalDevicePlatformWASAPI::EnumerateAudioDevicesInternal"
03a3e1b1:"[cid:%s] Failed to set client session volume. hr: %x","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalDeviceAudioWASAPI.cpp";"RtcPalDeviceAudioRenderWASAPI::Start"
03dd38e0:"[cid:%s] GetPnpNameAndRelatedFieldsThreadFunc failed with timeout error","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDeviceAudioEnumerationAsyncHelpers.cpp";"RtcPalDeviceAudioEnumerationHelperWASAPI::GetPnpNameAndRelatedFieldsWithTimeout"
03e63a74:"[cid:%s] GetWiFiNicInfo failed. hr=0x%08X","D:/a/_work/1/s/MSRTC/msrtc/src/rtcpal/SystemMetrics/RtcPalSystemMetricsClassic.cpp";"RtcPalGetSystemConfigurationInfo"
03f00740:"[cid:%s] Successfully read cached value for setting: %s. Value: %u","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/rtcpaldeviceaudioregistryhelper.cpp";"RtcPal::ReadDeviceSettingFromCache"
041c84e5:"[cid:%s] return OVERLAPPED %p","D:/a/_work/1/s/MSRTC/msrtc/src/rtcpal/network/windows/WinSockIOCPNT.cpp";"RtcPalIOCP::GetQueuedCompletionStatus"
049861d8:"[cid:%s] Failed to set context","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/common/windows/RtcPalMFAudioSourceReader.cpp";"IMFAudioSourceReader::OnReadSample"
04cd83c2:"[cid:%s] Failed to set probe source as it is null","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/VirtualPlatform.cpp";"RtcPalVirtualDevicePlatform::SetSourceProbeClientToSinkProbeClient"
04ea8a72:"[cid:%s] Format not Ok, suggested closest format: ch=%d, f=%d, bps=%d","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalWASAPIHelpers.cpp";"RtcPal::InitializeAudioClientInExclusiveMode"
052495e0:"[cid:%s] ConnectEx fails 0x%x OVERLAPPED %p","D:/a/_work/1/s/MSRTC/msrtc/src/rtcpal/network/windows/WinSockNT.cpp";"RtcPalSocket::OnConnectExCompletion"
05be96ea:"[cid:%s] m_pAudioDevice is nullptr","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDeviceAudioBoostControlImpl.cpp";"RtcPalDeviceAudioBoostControlImpl::RtcPalDeviceAudioBoostControlImpl"
0605f768:"[cid:%s] m_pAsyncParas->pGainControl->GetEnabled failed with hr = %#x ","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDeviceAudioBoostControlImpl.cpp";"RtcPalDeviceAudioBoostControlImpl::GetAutoBoostControlEnabled"
06af0d0b:"[cid:%s] pal_audio: UpdateVolumeControlSink[%ls] hr=0x%08X","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDevicePlatformWASAPI.cpp";"RtcPalDevicePlatformWASAPI::ActivateAudioDevice"
06f9c9f7:"[cid:%s] Error(%d), Failed to create resampler output type","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/common/windows/RtcPalMFAudioSourceReader.cpp";"IMFAudioSourceReader::OpenMFSourceReader"
071ba2ac:"[cid:%s] Failed to get file duration","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/common/windows/RtcPalMFAudioSourceReader.cpp";"IMFAudioSourceReader::Open"
07c501d8:"[cid:%s] Failed to lock sample buffer","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/common/windows/RtcPalMFAudioSourceReader.cpp";"IMFAudioSourceReader::ResampleData"
080b6c63:"[cid:%s] Format Ok","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalWASAPIHelpers.cpp";"RtcPal::InitializeAudioClient"
08db7297:"[cid:%s] Error(%d),Can\'t Set MF_MT_MAJOR_TYPE GUID","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/common/windows/RtcPalMFAudioSourceReader.cpp";"IMFAudioSourceReader::OpenMFSourceReader"
092e01f5:"[cid:%s] Failed to start trace OS log session. status=%lu\n","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalAudioOSLogHelper.cpp";"RtcPalAudioOSLogHelper::StartLogSession"
09328fbe:"[cid:%s] Failed to resample data","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/common/windows/RtcPalMFAudioSourceReader.cpp";"IMFAudioSourceReader::SynchronousRead"
0938d1a0:"[cid:%s] [ECS]: StorageKey %s is found, value is: %s","D:/a/_work/1/s/MSRTC/msrtc/src/rtcpal/common/common/RtcPalEcsSettings.cpp";"InitSettingValue"
09ddceb6:"[cid:%s] Caught system error %s","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDeviceAudioBoostControlImpl.cpp";"RtcPalDeviceAudioBoostControlImpl::RtcPalDeviceAudioBoostControlImpl"
09e895ad:"[cid:%s] OnDeviceStateChanged, device id=%ls, new state=%d","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalDevicePlatformWASAPICallback.cpp";"RtcPalDevicePlatformWASAPICallback::OnDeviceStateChanged"
09e9da81:"[cid:%s] %s","D:/a/_work/1/s/MSRTC/msrtc/src/rtcpal/tracing/common/RTCPalTraceImpl.cpp";"RtcPalPrintDefaultTrace"
0bab0748:"[cid:%s] Volume is set on active sink device. Using volume control source to set volume","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalDevicePlatformAPI.cpp";"RtcPalDevicePlatformSetVolumeInfoWithTimeout"
0c84ec1f:"[cid:%s] pal_audio: async enumeration of usb controller waiting time=0x%I64X(ms), hr=%x","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDevicePlatformWASAPI.cpp";"RtcPalDevicePlatformWASAPI::EnumerateUSBControllers"
0dba27d5:"[cid:%s] pal_audio: GetDefaultDeviceIdsThreadFunc starts","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDeviceAudioEnumerationAsyncHelpers.cpp";"RtcPalDeviceAudioEnumerationHelperWASAPI::GetDefaultDeviceIdWithTimeout"
0e350dc8:"[cid:%s] Error(%d), Failed to set Reader output to PCM","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/common/windows/RtcPalMFAudioSourceReader.cpp";"IMFAudioSourceReader::OpenMFSourceReader"
0f71bcf1:"[cid:%s] StopTraceW() failed: hr = 0x%x","D:/a/_work/1/s/MSRTC/msrtc/src/rtcpal/tracing/windows/WppConsumer.cpp";"RtcPalStopWppAndEtwInternal"
0fa511ed:"[cid:%s] File size larger than MAX_INT_ULONG","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/common/windows/RtcPalMFAudioSourceReader.cpp";"IMFAudioSourceReader::GetFileDuration"
100e5edc:"[cid:%s] AudioLoopback (%ls): Start","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalDeviceAudioWASAPI.cpp";"RtcPalDeviceAudioLoopbackWASAPI::Start"
1020a889:"[cid:%s] sample got re-used for production without consuming. ","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/VirtualAudioSample.cpp";"RtcPal::VirtualAudioSampleImpl::GetBuffer"
10bfb92a:"[cid:%s] Volume is queried on non-active device. Using default mode to return volume","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalDevicePlatformAPI.cpp";"RtcPalDevicePlatformGetVolumeInfoWithTimeout"
1126dd37:"[cid:%s] Failed GetBuffer on sample, hr = 0x%x","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/VirtualDeviceExtension.cpp";"RtcPalExtendedVirtualAudioRenderDeviceImpl::PRtcPALVADLockBufferCBackInternal"
125c47a8:"[cid:%s] Sample SetCallback to Func=0x%p, Context=0x%p","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/VirtualAudioSample.cpp";"RtcPal::VirtualAudioSampleImpl::SetCallback"
129c3578:"[cid:%s] Failed to get function pointer for DevCreateObjectQueryEx or DevCloseObjectQuery with hr = %8x","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDevQueryHelper.cpp";"RtcPalDevQueryHelper::LoadDllAndGetFuncPointers"
131c6b25:"[cid:%s] %s enumeration is in progress, platform metrics is not ready","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDevicePlatformWASAPI.cpp";"RtcPalDevicePlatformWASAPI::GetMetrics"
133ed4a3:"[cid:%s] Virtual audio device started","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/VirtualDeviceExtension.cpp";"RtcPalExtendedVirtualAudioRenderDeviceImpl::PRtcPALVADStartCBackInternal"
140044ce:"[cid:%s] Open registry key failed.","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/common/utils/windows/RtcPalDeviceUtilsWinClassic.cpp";"RtcPalDeviceUtils::GetMachineId"
14390331:"[cid:%s] failed removing event from socket: 0x%x","D:/a/_work/1/s/MSRTC/msrtc/src/rtcpal/network/windows/WinSockNT.cpp";"RtcPalSocket::DoAccept"
1450a39b:"[cid:%s] Setting session volume for device %ls to %d Scaling Factor: %d","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalDeviceAudioWASAPI.cpp";"RtcPalDeviceAudioWASAPI::SetAudioVolume"
14605e15:"[cid:%s] pal_audio: skip one device since it\'s failed to query device id, type=%d","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDevicePlatformWASAPI.cpp";"RtcPalDevicePlatformWASAPI::EnumerateAudioDevicesInternal"
1460b9d9:"[cid:%s] No sample in the lock free queue. time:%I64d, object:0x%I64X","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/VirtualAudioSample.cpp";"RtcPal::VirtualAudioSourceImpl::DequeueSample"
14efe2cd:"[cid:%s] couldn\'t create event with hr = %8x","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDevQueryHelper.cpp";"RtcPalDevQueryHelper::Query"
14fd8de9:"[cid:%s] AudioCapture (%ls): Destroyed","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalDeviceAudioWASAPI.cpp";"RtcPalDeviceAudioCaptureWASAPI::~RtcPalDeviceAudioCaptureWASAPI"
15f18ee0:"[cid:%s] Reset was required. start queue size: %d. end queue size: %d","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/VirtualAudioSample.cpp";"RtcPal::VirtualAudioSourceImpl::DequeueSample"
16a2d237:"[cid:%s] Failed to create GetPropStoreAndFriendlyNameThreadFuncParams","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDeviceAudioEnumerationAsyncHelpers.cpp";"RtcPalDeviceAudioEnumerationHelperWASAPI::GetPropStoreAndDeviceNameWithTimeout"
16f73a7f:"[cid:%s] Queue Close request OVERLAPPED %p for completion","D:/a/_work/1/s/MSRTC/msrtc/src/rtcpal/network/windows/WinSockNT.cpp";"RtcPalSocket::OnIoCompletion"
1718414a:"[cid:%s] Failed to pass sample to Resampler","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/common/windows/RtcPalMFAudioSourceReader.cpp";"IMFAudioSourceReader::ResampleData"
17218b0b:"[cid:%s] GetPnpNameAndRelatedFieldsWithTimeout failed with error hr=%x. Will use default values for pnp name and related fields","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDeviceAudioEnumerationAsyncHelpers.cpp";"RtcPalDeviceAudioEnumerationHelperWASAPI::GetInfoFromPropStoreWithTimeout"
17b03710:"[cid:%s] ActivateIAudioClientWithTimeout failed with error hr=%x. Will use default values for device format","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDeviceAudioEnumerationHelperWASAPI.cpp";"RtcPalDeviceAudioEnumerationHelperWASAPI::PopulateAudioDeviceInfoBasedOnAudioClient"
18846bc2:"[cid:%s] Failed to populate raw stream support info for device: type=%d, id=%ls. Error code returned: hr=%x. Using default values for raw support","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDeviceAudioEnumerationHelperWASAPI.cpp";"RtcPalDeviceAudioEnumerationHelperWASAPI::PopulateNonCriticalAudioDeviceInfo"
1926624b:"[cid:%s] RtcPalSystemMetricsCollector: Could not stop timer.","D:/a/_work/1/s/MSRTC/msrtc/src/rtcpal/SystemMetrics/RtcPalSystemMetricsBase.cpp";"RtcPalSystemMetricsCollector::SetStart"
198d0424:"[cid:%s] RegisterSocket socket=%p: fails 0x%x","D:/a/_work/1/s/MSRTC/msrtc/src/rtcpal/network/windows/WinSockIOCPNT.cpp";"RtcPalIOCP::RegisterSocket"
1a107227:"[cid:%s] ActivateIAudioEndpointVolumeWithTimeout failed with timeout error. Will use default values for gain fields.Time out counts for current device: %d Time out counts overall: %d","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDeviceAudioEnumerationHelperWASAPI.cpp";"RtcPalDeviceAudioEnumerationHelperWASAPI::PopulateGainControlInfo"
1a2e6018:"[cid:%s] Failed to create instance for RtcPalDevQueryHelper","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDevicePlatformWASAPI.cpp";"RtcPalDevicePlatformWASAPI::UpdateAudioDeviceList"
1a887e90:"[cid:%s] Failed to get RtcPalAudioMediaExtensionType_VirtualDeviceManager extension, hr = 0x%x","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDevicePlatformWASAPI.cpp";"RtcPalDevicePlatformWASAPI::RegisterVirtualAudioDevices"
1b7d17c7:"[cid:%s] Total samples produced=%llu","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/VirtualDeviceExtension.cpp";"RtcPalExtendedVirtualAudioRenderDeviceImpl::PRtcPALVADLockBufferCBackInternal"
1c1d582a:"[cid:%s] Invalid arg. !pTimeOutCountCurrentDevice. Not querying device format. Will use default values","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDeviceAudioEnumerationHelperWASAPI.cpp";"RtcPalDeviceAudioEnumerationHelperWASAPI::PopulateAudioDeviceInfoBasedOnAudioClient"
1c8077ab:"[cid:%s] Failed to get driver version for device: type=%d, id=%ls. Error code returned: hr=%x. Using default value for driver version","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDeviceAudioEnumerationHelperWASAPI.cpp";"RtcPalDeviceAudioEnumerationHelperWASAPI::PopulateNonCriticalAudioDeviceInfo"
1ca7e238:"[cid:%s] queuedBytes=%u","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalDeviceAudioWASAPI.cpp";"RtcPalDeviceAudioWASAPI::GetQueuedBytes"
1d8ff65d:"[cid:%s] GetPnpNameAndRelatedFieldsThreadFunc failed with error hr=%x","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDeviceAudioEnumerationAsyncHelpers.cpp";"RtcPalDeviceAudioEnumerationHelperWASAPI::GetPnpNameAndRelatedFieldsWithTimeout"
1db837cb:"[cid:%s] audio device thread was already initialized","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/VirtualDeviceExtension.cpp";"RtcPalExtendedVirtualAudioDeviceImpl::InitializeAudioDeviceThread"
1de60568:"[cid:%s] EmulatedAcceptEx: DoAccept succeeds: queue OVERLAPPED %p for completion","D:/a/_work/1/s/MSRTC/msrtc/src/rtcpal/network/windows/WinSockNT.cpp";"RtcPalSocket::OnEventSelectAccept"
1ef08397:"[cid:%s] Failed ReserveBuffer on sample, hr = 0x%x","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/VirtualDeviceExtension.cpp";"RtcPalExtendedVirtualAudioRenderDeviceImpl::PRtcPALVADLockBufferCBackInternal"
1fe28a14:"[cid:%s] Failed to start the WiFiIinspector. hr=0x%08X","D:/a/_work/1/s/MSRTC/msrtc/src/rtcpal/SystemMetrics/RtcPalSystemMetricsClassic.cpp";"RtcPalSystemMetricsCollectorClassic::OnTimerCallback"
2047418a:"[cid:%s] GetPropStoreAndDeviceNameWithTimeout failed with timeout error. Will use default values for friendly name and display name and other prop store fields","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDeviceAudioEnumerationAsyncHelpers.cpp";"RtcPalDeviceAudioEnumerationHelperWASAPI::GetInfoFromPropStoreWithTimeout"
206a8f51:"[cid:%s] ActivateMMDeviceInterfaceThreadFunc failed with timeout error","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDeviceAudioEnumerationAsyncHelpers.cpp";"RtcPalDeviceAudioEnumerationHelperWASAPI::ActivateIAudioClientWithTimeout"
20eae09b:"[cid:%s] Failed to get device display name. Will use default values for display name","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDeviceAudioEnumerationAsyncHelpers.cpp";"RtcPalDeviceAudioEnumerationHelperWASAPI::GetInfoFromPropStoreWithTimeout"
20f96e34:"[cid:%s] Queue EmulatedAccept OVERLAPPED %p for completion - cancelled","D:/a/_work/1/s/MSRTC/msrtc/src/rtcpal/network/windows/WinSockNT.cpp";"RtcPalSocket::Close"
2147099e:"[cid:%s] pal_audio: IAudioClient::GetDevicePeriod succeeded, hnsDefaultDevicePeriod: %llu, hnsMinimumDevicePeriod: %llu","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalWASAPIHelpers.cpp";"RtcPal::InitializeAudioClientInExclusiveMode"
22472f17:"[cid:%s] rt::persistent::Flush() failed. err=%s (%d)","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/rtcpaldeviceaudioregistryhelper.cpp";"RtcPal::WriteDeviceSettingToCache"
22be1fd1:"[cid:%s] Speaker session volume is set to: %u","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalDeviceAudioWASAPI.cpp";"RtcPalDeviceAudioRenderWASAPI::SetSessionVolume"
236d995d:"[cid:%s] OnAddrChangeEventSignaled: unexpected error: WSAIoctl return success: AddressFamily: %d","D:/a/_work/1/s/MSRTC/msrtc/src/rtcpal/network/windows/AddrChangeNotif.cpp";"CRtcPalAddressChangeNotification::OnAddrChangeEventSignaled"
23e5645d:"[cid:%s] Failed to get device friendly name. Will use default values for friendly name","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDeviceAudioEnumerationAsyncHelpers.cpp";"RtcPalDeviceAudioEnumerationHelperWASAPI::GetInfoFromPropStoreWithTimeout"
246cd05b:"[cid:%s] OnDeviceAdded, dvice id=%ls","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalDevicePlatformWASAPICallback.cpp";"RtcPalDevicePlatformWASAPICallback::OnDeviceAdded"
259ca9f6:"[cid:%s] Virtual Platform has been initialized!","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/VirtualPlatform.cpp";"RtcPalVirtualDevicePlatform::Initialize"
26af750f:"[cid:%s] Setting session mute for device %ls to %d","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalDeviceAudioWASAPI.cpp";"RtcPalDeviceAudioWASAPI::SetAudioVolume"
2703ca00:"[cid:%s] AudioLoopback (%ls): Stop - LockBuffer=%u FramesCaptured=%u","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalDeviceAudioWASAPI.cpp";"RtcPalDeviceAudioLoopbackWASAPI::Stop"
277f18eb:"[cid:%s] Render one sample to virtual sink. queued bytes=%d, device position = %llu, expected finished time=%llu, total samples delivered=%llu","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/VirtualDeviceExtension.cpp";"RtcPalExtendedVirtualAudioRenderDeviceImpl::AudioDeviceThreadProcOneTime"
290a439a:"[cid:%s] Releasing audio client","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalDeviceAudioWASAPI.cpp";"RtcPalDeviceAudioWASAPI::~RtcPalDeviceAudioWASAPI"
2a7bafe0:"[cid:%s] Cannot open file at %s","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalTestHelper.cpp";"RtcPalAudioLoggingHelper::RtcPalAudioLogging::DeleteExistingAndOpenNew"
2a9210e6:"[cid:%s] pal_audio: async enumeration waiting time for type: %d, time=0x%I64X, hr=%x","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDevicePlatformWASAPI.cpp";"RtcPalDevicePlatformWASAPI::EnumerateAudioDevices"
2bbfb1b0:"[cid:%s] pal_audio: async enumeration of usb controller starts","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDevicePlatformWASAPI.cpp";"RtcPalDevicePlatformWASAPI::EnumerateUSBControllers"
2bc97dbc:"[cid:%s] ReorderDeviceList ECS key is not set. Device list will not be sorted","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalWASAPIHelpers.cpp";"RtcPal::SortDeviceList"
2bdd0dcd:"[cid:%s] Failed to populate friendly name for device: type=%d, id=%ls. Error code returned: hr=%x. Using default value for device friendly name","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDeviceAudioEnumerationHelperWASAPI.cpp";"RtcPalDeviceAudioEnumerationHelperWASAPI::PopulateNonCriticalAudioDeviceInfo"
2c4cf8f7:"[cid:%s] created sample:0x%I64X, time:0x%I64X, object:0x%I64X","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/VirtualAudioSample.cpp";"RtcPal::VirtualAudioSourceImpl::CreateSample"
2e5d1111:"[cid:%s] Posting device property change notification","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalDevicePlatformPropertyChangeNotificationFilter.cpp";"RtcPalDevicePlatformPropertyChangeNotificationFilter::PostDevicePropertyChangedNotification"
2e9260c4:"[cid:%s] Device audio effects changed","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalDeviceAudio.cpp";"RtcPalDeviceAudio::OnDeviceEffectsChange"
2ea27a01:"[cid:%s] SetVolumeOnActiveSink failed with hr=0x%x","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDevicePlatformWASAPI.cpp";"RtcPalDevicePlatformWASAPI::SetVolumeOnActiveSink"
2eb518da:"[cid:%s] Setting system dB volume for device %ls to %d Scaling Factor: %d","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalDeviceAudioWASAPI.cpp";"RtcPalDeviceAudioWASAPI::SetAudioVolume"
2fb411ec:"[cid:%s] Virtual Platform has been deleted!","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/VirtualPlatform.cpp";"RtcPalVirtualDevicePlatform::~RtcPalVirtualDevicePlatform"
302f6bc2:"[cid:%s] Failed to read cache value for init spk session vol override  hr=%0x","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalDeviceAudioWASAPI.cpp";"RtcPalDeviceAudioRenderWASAPI::SetSessionVolume"
3053320d:"[cid:%s] EnableVirtualCaptureRequestSampleThread failed due to virtual audio platform has not been initialized yet","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalDevicePlatform.cpp";"RtcPalDevicePlatform::EnableVirtualCaptureRequestSampleThread"
305453bf:"[cid:%s] Master volume is 0 at end of call. Not caching user override of speaker master volume","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalDeviceAudioWASAPI.cpp";"RtcPalDeviceAudioRenderWASAPI::Stop"
3094bd8d:"[cid:%s] IsFormatSupported, hr=%#x","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalWASAPIHelpers.cpp";"RtcPal::InitializeAudioClientInExclusiveMode"
31dddb43:"[cid:%s] AudioRender: ReleaseBuffer=%u FramesRendered=%u","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalDeviceAudioWASAPI.cpp";"RtcPalDeviceAudioRenderWASAPI::ReleaseBuffer"
321d9bbf:"[cid:%s] asyncOp->QueryInterface failed with hr = %8x","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/common/windows/winrt/PnpAsyncCompletionHandler.h";"RtcPalAsyncCompleteHandler::Invoke"
32a81b02:"[cid:%s] Failed to create IMFSourceReader","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/common/windows/RtcPalMFAudioSourceReader.cpp";"IMFAudioSourceReader::Open"
3301c160:"[cid:%s] Zero duration","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/common/windows/RtcPalMFAudioSourceReader.cpp";"IMFAudioSourceReader::GetFileDuration"
33560b9f:"[cid:%s] EmulatedConnectEx: Connect request aborted: NetworkEvent %d","D:/a/_work/1/s/MSRTC/msrtc/src/rtcpal/network/windows/WinSockNT.cpp";"RtcPalSocket::OnEventSelectConnectCompletion"
33a6700c:"[cid:%s] Failed to populate jack subtype info for device: type=%d, id=%ls. Error code returned: hr=%x. Not refining device form factor","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDeviceAudioEnumerationAsyncHelpers.cpp";"RtcPalDeviceAudioEnumerationHelperWASAPI::PopulateInfoFromPropStoreThreadFunc"
342252fe:"[cid:%s] AcceptEx fails WSAEINVAL OVERLAPPED %p: name buffer is too small, input %d bytes required %d bytes","D:/a/_work/1/s/MSRTC/msrtc/src/rtcpal/network/windows/WinSockNT.cpp";"RtcPalSocket::OnAcceptExCompletion"
34a0e305:"[cid:%s] Failed to create RtcPalDevicePlatformPropertyChangeNotificationFilter, no enough memory","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalDevicePlatformWASAPICallback.cpp";"RtcPalDevicePlatformWASAPICallback::RegisterNotification"
354a3408:"[cid:%s] Closing: I/O count %d","D:/a/_work/1/s/MSRTC/msrtc/src/rtcpal/network/windows/WinSockNT.cpp";"RtcPalSocket::Close"
360b10b3:"[cid:%s] Deliver simple device event (%d) failed with hr:%x","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/VirtualDeviceExtension.cpp";"RtcPalExtendedVirtualAudioDeviceImpl::DeliverSimpleAudioDeviceEvent"
36ade1b3:"[cid:%s] AudioRender (%ls): Activate - FormatTag=%u Channels=%d SamplesPerSec=%d BitsPerSample=%u 0x%x","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalDeviceAudioWASAPI.cpp";"RtcPalDeviceAudioRenderWASAPI::Activate"
36b3c625:"[cid:%s] failed to create RVDDeviceInfo_t object from pDevInfo(%p) with error %s","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalDevicePlatform.cpp";"RtcPalDevicePlatform::AddDeviceInfoListToCollection"
3725a5ca:"[cid:%s] GetResult failed, m_spUnk is nullptr","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/ActivateAudio.cpp";"RtcPalActivateAudioInterfaceDispatcher::GetResult"
375a6b7d:"[cid:%s] Queue Close request OVERLAPPED %p for completion","D:/a/_work/1/s/MSRTC/msrtc/src/rtcpal/network/windows/WinSockNT.cpp";"RtcPalSocket::SendTo"
37e9a776:"[cid:%s] Create IOCP %p","D:/a/_work/1/s/MSRTC/msrtc/src/rtcpal/network/windows/WinSockIOCP.cpp";"RtcPalCreateSocketIOCP"
37f9cccf:"[cid:%s] Set probe source to probe sink successfully","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/VirtualPlatform.cpp";"RtcPalVirtualDevicePlatform::SetSourceProbeClientToSinkProbeClient"
3883c820:"[cid:%s] Frame sample jitter buffer recovered from starvation. time:%I64d, fFrameReady:%d, m_startTime:%I64d, m_lastStartTime:%I64d, object:0x%I64X","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/VirtualAudioSample.cpp";"RtcPal::VirtualAudioSourceImpl::DequeueSample"
3906086a:"[cid:%s] pal_audio: populate basic device information succeeded (id=%ls, friendlyname=%ls, pnpname=%ls).","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalDevicePlatformWinrtHelper.cpp";"RtcPalDevicePlatformWinRTHelper::PopulateWinRTDevicePropertiesDevInfo"
396fecc0:"[cid:%s] Failed to lock sample buffer","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/common/windows/RtcPalMFAudioSourceReader.cpp";"IMFAudioSourceReader::OnReadSample"
3ab195b7:"[cid:%s] MiscStats: MmcssErrorCount:%lu","D:/a/_work/1/s/MSRTC/msrtc/src/rtcpal/SystemMetrics/RtcPalSystemMetricsClassic.cpp";"RtcPalSystemMetricsCollectorClassic::OnTimerCallback"
3d1bb0c8:"[cid:%s] Failed to read cache value for init spk vol override  hr=%0x","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalDeviceAudioWASAPI.cpp";"RtcPalDeviceAudioRenderWASAPI::SetMasterVolumeFromEcsSetting"
3d355199:"[cid:%s] Error(%d), Failed to get resampler input properties","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/common/windows/RtcPalMFAudioSourceReader.cpp";"IMFAudioSourceReader::OpenMFSourceReader"
3d367a24:"[cid:%s] CertifiedListId:=%ls; device name=%ls","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalDevicePlatformWinrtHelper.cpp";"RtcPalDevicePlatformWinRTHelper::GetCertifiedListDeviceID"
3d583fb6:"[cid:%s] closesocket() failed with error 0x%x.","D:/a/_work/1/s/MSRTC/msrtc/src/rtcpal/network/windows/CloseSocketOffload.cpp";"CloseSocketOffload::CloseSocket"
3dc55f17:"[cid:%s] Error(%d),Can\'t get MediaType object","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/common/windows/RtcPalMFAudioSourceReader.cpp";"IMFAudioSourceReader::OpenMFSourceReader"
3e20a0aa:"[cid:%s] Failed while push samples while delivering sample","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/VirtualAudioSample.cpp";"RtcPal::VirtualAudioSourceImpl::DeliverSample"
3ed6bbda:"[cid:%s] Failed to get Presentation Attribute","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/common/windows/RtcPalMFAudioSourceReader.cpp";"IMFAudioSourceReader::GetFileDuration"
3edb5d1d:"[cid:%s] EmulatedConnectEx fails 0x%x OVERLAPPED %p","D:/a/_work/1/s/MSRTC/msrtc/src/rtcpal/network/windows/WinSockNT.cpp";"RtcPalSocket::AsyncConnectViaEventSelect"
3f0af5a1:"[cid:%s] Failed to collect system memory usage info from GlobalMemoryStatusEx()","D:/a/_work/1/s/MSRTC/msrtc/src/rtcpal/SystemMetrics/RtcPalSystemMetricsClassic.cpp";"RtcPalSystemMetricsCollectorClassic::OnTimerCallback"
3fac0558:"[cid:%s] ActivateAudioInterfaceAsync 0x%x","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/ActivateAudio.cpp";"RtcPalActivateAudioInterfaceDispatcher::Invoke"
4045a3c5:"[cid:%s] EnableVirtualCaptureRequestSampleThread succeeded, enable = %u","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalDevicePlatform.cpp";"RtcPalDevicePlatform::EnableVirtualCaptureRequestSampleThread"
40ae61c7:"[cid:%s] pal_audio: GetPropStoreAndDeviceNameThreadFunc starts","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDeviceAudioEnumerationAsyncHelpers.cpp";"RtcPalDeviceAudioEnumerationHelperWASAPI::GetPropStoreAndDeviceNameWithTimeout"
4110f216:"[cid:%s] EmulatedConnectEx: Status: 0x%x OVERLAPPED %p","D:/a/_work/1/s/MSRTC/msrtc/src/rtcpal/network/windows/WinSockNT.cpp";"RtcPalSocket::OnEventSelectConnectCompletion"
4181c5f6:"[cid:%s] PopulateInfoFromPropStoreThreadFuncParams failed with error hr=%x. Using default values for prop store fields","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDeviceAudioEnumerationAsyncHelpers.cpp";"RtcPalDeviceAudioEnumerationHelperWASAPI::GetInfoFromPropStoreWithTimeout"
41e87572:"[cid:%s] Existing queued samples got reset: %d","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/VirtualAudioSample.cpp";"RtcPal::DrainQueuedVirtualAudioSamples"
41ee2ad2:"[cid:%s] DeInit","D:/a/_work/1/s/MSRTC/msrtc/src/rtcpal/network/windows/AddrChangeNotif.cpp";"CRtcPalAddressChangeNotification::DeInit"
4275870f:"[cid:%s] Failed to populate WinRT device information and skip with hr=0x%08X. ","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDevicePlatformWASAPI.cpp";"RtcPalDevicePlatformWASAPI::EnumerateWinRTAudioDevicesAndUpdateWinClassicDeviceInfo"
4299e8f0:"[cid:%s] pal_audio: GetDefaultDeviceIdsThreadFunc waiting time, time=%llu, hr=%x","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDeviceAudioEnumerationAsyncHelpers.cpp";"RtcPalDeviceAudioEnumerationHelperWASAPI::GetDefaultDeviceIdWithTimeout"
4324539a:"[cid:%s] Using timeout mode to query device gain info. Overall timeout count: %d. Max allowed timeout count overall: %dTimeout count for current device: %d. Max allowed timeout count per device: %d","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDeviceAudioEnumerationHelperWASAPI.cpp";"RtcPalDeviceAudioEnumerationHelperWASAPI::PopulateGainControlInfo"
43afc689:"[cid:%s] !m_spEPVolume Not setting master volume","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalDeviceAudioWASAPI.cpp";"RtcPalDeviceAudioRenderWASAPI::SetMasterVolumeFromEcsSetting"
44155072:"[cid:%s] AudioCapture (%ls): Start","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalDeviceAudioWASAPI.cpp";"RtcPalDeviceAudioCaptureWASAPI::Start"
4549e8f2:"[cid:%s] SetAudioVolume failed with hr=%0x Not setting speaker master volume to %d","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalDeviceAudioWASAPI.cpp";"RtcPalDeviceAudioRenderWASAPI::SetMasterVolumeFromEcsSetting"
45e4fab3:"[cid:%s] Topology traversal of %ls: %s","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDeviceAudioBoostControlImpl.cpp";"RtcPalDeviceAudioBoostControlImpl::InitializeAsync"
46676824:"[cid:%s] Failed to get volume on active source. Using default mode to return volume","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalDevicePlatformAPI.cpp";"RtcPalDevicePlatformGetVolumeInfoWithTimeout"
467a41b2:"[cid:%s] pal_audio: VolumeControlThreadFunc waiting time, time=0x%I64X, hr=%x","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDevicePlatformWASAPI.cpp";"RtcPalDevicePlatformWASAPI::VolumeControlWithTimeout"
4691cfe8:"[cid:%s] Setting system mute for device %ls to %d","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalDeviceAudioWASAPI.cpp";"RtcPalDeviceAudioWASAPI::SetAudioVolume"
469c394c:"[cid:%s] EmulatedAcceptEx: WSAEventSelect fails 0x%x synchronously OVERLAPPED %p","D:/a/_work/1/s/MSRTC/msrtc/src/rtcpal/network/windows/WinSockNT.cpp";"RtcPalSocket::AsyncAcceptViaEventSelect"
46e27fdd:"[cid:%s] Failed to SetEvent on m_hCloseEvent","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalDevicePlatformPropertyChangeNotificationFilter.cpp";"RtcPalDevicePlatformPropertyChangeNotificationFilter::StopDevicePropertyChangeWorkItem"
46e3087b:"[cid:%s] Using timeout mode to query device gain fields. Overall timeout count: %d. Max allowed timeout count overall: %dTimeout count for current device: %d. Max allowed timeout count per device: %d","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDeviceAudioEnumerationHelperWASAPI.cpp";"RtcPalDeviceAudioEnumerationHelperWASAPI::PopulateNonCriticalAudioDeviceInfo"
472affa6:"[cid:%s] Format Ok","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalWASAPIHelpers.cpp";"RtcPal::InitializeAudioClientInExclusiveMode"
47b4bb19:"[cid:%s] Couldn\'t find a matching protocol entry, will rely on the defaults","D:/a/_work/1/s/MSRTC/msrtc/src/rtcpal/network/windows/WinSockNT.cpp";"RtcPalSocket::Initialize"
480bca99:"[cid:%s] The real error hr = %x","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalDevicePlatformAPI.cpp";"RtcPalDeviceErrorCodeFromHResult"
48c2dc07:"[cid:%s] Failed to create ActivateIEndpointVolumeThreadFuncParams","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDeviceAudioEnumerationAsyncHelpers.cpp";"RtcPalDeviceAudioEnumerationHelperWASAPI::ActivateIAudioEndpointVolumeWithTimeout"
48fe7910:"[cid:%s] Failed to populate guid container id for device: type=%d, id=%ls. Error code returned: hr=%x. Using default value for guid contrainer id","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDeviceAudioEnumerationAsyncHelpers.cpp";"RtcPalDeviceAudioEnumerationHelperWASAPI::PopulateInfoFromPropStoreThreadFunc"
4996c8b0:"[cid:%s] GetResult fail 0x%x","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/ActivateAudio.cpp";"RtcPalActivateAudioInterface"
4a2c0322:"[cid:%s] ActivateVolumeNotification successful","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalDeviceAudioWASAPI.cpp";"RtcPalDeviceAudioCaptureWASAPI::Activate"
4a316e55:"[cid:%s] Create audio device thread timer event failed with error:%x","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/VirtualDeviceExtension.cpp";"RtcPalExtendedVirtualAudioDeviceImpl::InitializeAudioDeviceThread"
4a61b215:"[cid:%s] failed to wait for query to complete, hr = %8x","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDevQueryHelper.cpp";"RtcPalDevQueryHelper::Query"
4ab819b9:"[cid:%s] GetVolumeOnActiveSource failed with hr=0x%x","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDevicePlatformWASAPI.cpp";"RtcPalDevicePlatformWASAPI::GetVolumeOnActiveSource"
4b9d53e5:"[cid:%s] Failed to Initialize COM and MF","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/common/windows/RtcPalMFAudioSourceReader.cpp";"IMFAudioSourceReader::Open"
4bc2fe5d:"[cid:%s] Error(%d),Can\'t Set MF_MT_SUBTYPE GUID","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/common/windows/RtcPalMFAudioSourceReader.cpp";"IMFAudioSourceReader::OpenMFSourceReader"
4bfee3b4:"[cid:%s] Error(%d),Failed to create SourceReader from URL","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/common/windows/RtcPalMFAudioSourceReader.cpp";"IMFAudioSourceReader::OpenMFSourceReader"
4c06e0f7:"[cid:%s] GetMixFormat failed and use default format. hr=%x","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDeviceAudioEnumerationHelperWASAPI.cpp";"RtcPalDeviceAudioEnumerationHelperWASAPI::PopulateAudioDeviceInfoBasedOnAudioClient"
4c12ea8d:"[cid:%s] Failed to populate pnp name for device: type=%d, id=%ls. Error code returned: hr=%x. Using default values for pnp name and connectinon type","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDeviceAudioEnumerationHelperWASAPI.cpp";"RtcPalDeviceAudioEnumerationHelperWASAPI::PopulateNonCriticalAudioDeviceInfo"
4c30c6f4:"[cid:%s] Failed to get device prop store. Will use default values for prop store fields","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDeviceAudioEnumerationAsyncHelpers.cpp";"RtcPalDeviceAudioEnumerationHelperWASAPI::GetInfoFromPropStoreWithTimeout"
4c488de5:"[cid:%s] ConnectEx OVERLAPPED %p","D:/a/_work/1/s/MSRTC/msrtc/src/rtcpal/network/windows/WinSockNT.cpp";"RtcPalSocket::AsyncConnectViaConnectEx"
4c58599a:"[cid:%s] pal_audio: VirtualDeviceVolumeControlThreadFunc waiting time, time=%llu, hr=%x","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/VirtualPlatform.cpp";"RtcPalVirtualDevicePlatform::VolumeControlWithTimeout"
4c9e9cdf:"[cid:%s] Failed to set Resampler Input Type","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/common/windows/RtcPalMFAudioSourceReader.cpp";"IMFAudioSourceReader::GenerateMFT"
4ce35560:"[cid:%s] AudioRender (%ls): Activate - Clock frequency from IAudioClock does not match device format. ClockFrequency=%llu, AvgBytesPerSec=%d","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalDeviceAudioWASAPI.cpp";"RtcPalDeviceAudioRenderWASAPI::Activate"
4cf37f5c:"[cid:%s] Force audio render happens","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/VirtualDeviceExtension.cpp";"RtcPalExtendedVirtualAudioDeviceImpl::AudioDeviceThreadProc"
4e23a8b0:"[cid:%s] RegisterSocket socket=%p: already registered","D:/a/_work/1/s/MSRTC/msrtc/src/rtcpal/network/windows/WinSockIOCPNT.cpp";"RtcPalIOCP::RegisterSocket"
4e7585cc:"[cid:%s] pDevInfo is nullptr","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDevicePlatformWASAPI.cpp";"RtcPalDevicePlatformWASAPI::UpdateWinrtDeviceInfoOnClassicDevice"
4ed2f0c2:"[cid:%s] Listen succeeds backlog=%d","D:/a/_work/1/s/MSRTC/msrtc/src/rtcpal/network/windows/WinSockNT.cpp";"RtcPalSocket::Listen"
4f932bcd:"[cid:%s] Failed to stop trace session. status=%lu\n","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalAudioOSLogHelper.cpp";"RtcPalAudioOSLogHelper::StartLogSession"
5016cc67:"[cid:%s] Priority score is not set for device %d. Device list will not be sorted","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalWASAPIHelpers.cpp";"RtcPal::SortDeviceList"
5115d12e:"[cid:%s] SetDuckingPreference for render device faild with 0x%x","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalDeviceAudioWASAPI.cpp";"RtcPalDeviceAudioRenderWASAPI::Activate"
514a972a:"[cid:%s] Enabled Audio Effects = %x","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalDevicePlatform.cpp";"RtcPalDevicePlatform::GetEnabledAudioProcessingFeatures"
51b235de:"[cid:%s] Delete device failed with return: %x","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/VirtualPlatform.cpp";"RtcPalVirtualDevicePlatform::RemoveDevice"
51e5428f:"[cid:%s] pal_audio: PopulateInfoFromPropStoreThreadFuncParams starts","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDeviceAudioEnumerationAsyncHelpers.cpp";"RtcPalDeviceAudioEnumerationHelperWASAPI::GetInfoFromPropStoreWithTimeout"
523274c2:"[cid:%s] OS logging: %s","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDevicePlatformWASAPI.cpp";"RtcPalDevicePlatformWASAPI::RtcPalDevicePlatformWASAPI"
52a240ca:"[cid:%s] MFStartup unexpectedly return","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/mfhelper/mfhelper.cpp";"RtcPalMFHasAVCapture"
5401faf6:"[cid:%s] RtcPalSystemMetricsCollector: Cancelling timer took - %lld ms ","D:/a/_work/1/s/MSRTC/msrtc/src/rtcpal/SystemMetrics/RtcPalSystemMetricsBase.cpp";"RtcPalSystemMetricsCollector::SetStart"
5482d7ea:"[cid:%s] [ECS]: StorageKey %s is found, value is: %u","D:/a/_work/1/s/MSRTC/msrtc/src/rtcpal/common/common/RtcPalEcsSettings.cpp";"InitSettingValue"
54c383ab:"[cid:%s] Failed to query the WiFi stats. hr=0x%08X","D:/a/_work/1/s/MSRTC/msrtc/src/rtcpal/SystemMetrics/RtcPalSystemMetricsClassic.cpp";"RtcPalSystemMetricsCollectorClassic::OnTimerCallback"
553c63b9:"[cid:%s] pal_audio: queried endpoints count: [type=%d,count=%d]","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDevicePlatformWASAPI.cpp";"RtcPalDevicePlatformWASAPI::EnumerateAudioDevicesInternal"
5564026c:"[cid:%s] VolumeControlThreadFunc failed with error %x","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDevicePlatformWASAPI.cpp";"RtcPalDevicePlatformWASAPI::VolumeControlWithTimeout"
55b40318:"[cid:%s] AudioRender: pui64QPCPos=%lu, pui64Pos=%lu, piPadding = %u","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalDeviceAudioWASAPI.cpp";"RtcPalDeviceAudioRenderWASAPI::GetGlitchInfo"
560c4127:"[cid:%s] SetWindowsHookEx fails with 0x%x","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalMsgThread.cpp";"RtcPalMsgThread::RtcPalMsgThreadProc"
57d80b4f:"[cid:%s] pal_audio: UpdateVolumeControlSource[%ls] hr=0x%08X","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDevicePlatformWASAPI.cpp";"RtcPalDevicePlatformWASAPI::ActivateAudioDevice"
5809b1a9:"[cid:%s] pal_audio: IAudioClient::Initialize succeeded","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalWASAPIHelpers.cpp";"RtcPal::InitializeAudioClient"
581edd7c:"[cid:%s] Failed to call CoInitializeEx","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/common/windows/RtcPalMFAudioSourceReader.cpp";"IMFAudioSourceReader::SynchronousRead"
595dd866:"[cid:%s] %s@%d: if_WARN(%s) == true","D:/a/_work/1/s/MSRTC/msrtc/src/rtcpal/tracing/windows/WppConsumer.cpp";"RtcPalTraceCondIfTrue"
59a638e0:"[cid:%s] Get IAudioSessionControl2 for render device faild with 0x%x","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalDeviceAudioWASAPI.cpp";"RtcPalDeviceAudioRenderWASAPI::Activate"
5b4a0844:"[cid:%s] Volume is set on active source device. Using volume control source to set volume","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalDevicePlatformAPI.cpp";"RtcPalDevicePlatformSetVolumeInfoWithTimeout"
5b539ffa:"[cid:%s] pal_audio: GetPropStoreAndDeviceNameThreadFunc waiting time, time=%llu, hr=%x","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDeviceAudioEnumerationAsyncHelpers.cpp";"RtcPalDeviceAudioEnumerationHelperWASAPI::GetPropStoreAndDeviceNameWithTimeout"
5b696378:"[cid:%s] pal_audio: device list [type=%d,name=%ls,ff=%d,dad=%d,dcd=%d,caps=0x%I64X,priorityScore=%d,isInternalDevice=%d]","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalDevicePlatform.cpp";"RtcPalDevicePlatform::GetDeviceList"
5c450d71:"[cid:%s] failed making socket a nonblocking socket: 0x%x","D:/a/_work/1/s/MSRTC/msrtc/src/rtcpal/network/windows/WinSockNT.cpp";"RtcPalSocket::DoAccept"
5c8c1df8:"[cid:%s] Fail to initialize UI work item: 0x%x","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/ActivateAudio.cpp";"RtcPalActivateAudioInterface"
5dbfc992:"[cid:%s] Invalid boost value, minLevelDb(%f) > maxLevelDb(%f)","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDeviceAudioBoostControlImpl.cpp";"RtcPalDeviceAudioBoostControlImpl::InitializeAsync"
5e06d83c:"[cid:%s] Failed to flush source reader","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/common/windows/RtcPalMFAudioSourceReader.cpp";"IMFAudioSourceReader::FlushSourceReader"
5e6bd626:"[cid:%s] device property change event proc has woken up, current time = %I64d ms","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalDevicePlatformPropertyChangeNotificationFilter.cpp";"RtcPalDevicePlatformPropertyChangeNotificationFilter::DevicePropertyChangedEventProc"
5e98537f:"[cid:%s] WaitForCompletion failed for %s with hr = %x","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalDevicePlatformAPI.cpp";"WaitForCompletionIfNeccessary"
5f26229d:"[cid:%s] Stop OS logging","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalAudioOSLogHelper.h";"ScopedRtcPalAudioOSLogHelper::~ScopedRtcPalAudioOSLogHelper"
5f4312c7:"[cid:%s] ControlTrace(stop) failed with %lu\n","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalAudioOSLogHelper.cpp";"RtcPalAudioOSLogHelper::StartAudioOSLoggingInternal"
5fae5d52:"[cid:%s] Failed to get vid pid for device: type=%d, id=%ls. Error code returned: hr=%x. Using default values for vid, pid","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDeviceAudioEnumerationHelperWASAPI.cpp";"RtcPalDeviceAudioEnumerationHelperWASAPI::PopulateNonCriticalAudioDeviceInfo"
604c7b84:"[cid:%s] Invalid arg. !pTimeOutCountCurrentDevice. Not querying device prop store fields. Will use default values","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDeviceAudioEnumerationHelperWASAPI.cpp";"RtcPalDeviceAudioEnumerationHelperWASAPI::PopulateNonCriticalAudioDeviceInfo"
614d850b:"[cid:%s] AudioRender (%ls): Stop - ReleaseBuffer=%u FramesRendered=%u","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalDeviceAudioWASAPI.cpp";"RtcPalDeviceAudioRenderWASAPI::Stop"
61cf4312:"[cid:%s] Sample Consumed callback invoked: func=0x%p, context=0x%p","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/VirtualAudioSample.cpp";"RtcPal::VirtualAudioSampleImpl::Release"
61facfd9:"[cid:%s] accept fails: 0x%x","D:/a/_work/1/s/MSRTC/msrtc/src/rtcpal/network/windows/WinSockNT.cpp";"RtcPalSocket::DoAccept"
621499ce:"[cid:%s] update raw stream support for device %ls on machine %s as %d.","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDevicePlatformWASAPI.cpp";"RtcPalDevicePlatformWASAPI::UpdateDeviceInfoForRawModeCapable"
6278a5e8:"[cid:%s] PdhQuery has no working counters at all, so it is useless","D:/a/_work/1/s/MSRTC/msrtc/src/rtcpal/SystemMetrics/RtcPalSystemMetricsClassic.cpp";"RtcPalSystemMetricsCollectorClassic::CollectPdhMetrics"
63289a79:"[cid:%s] ControlTrace(flush) failed with %lu\n","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalAudioOSLogHelper.cpp";"RtcPalAudioOSLogHelper::FlushAudioOSLogging"
636f7dc3:"[cid:%s] GetDefaultDeviceIdsThreadFunc failed with timeout error for eRole: %d. Will mark first device as default device. Timeout count overall: %d","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDeviceAudioEnumerationAsyncHelpers.cpp";"RtcPalDeviceAudioEnumerationHelperWASAPI::GetDefaultDeviceIdWithTimeout"
63e8d5ce:"[cid:%s] Failed to resample data","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/common/windows/RtcPalMFAudioSourceReader.cpp";"IMFAudioSourceReader::OnReadSample"
643c35f4:"[cid:%s] spEnumerator->GetDefaultAudioEndpoint failed. Will set first enumerated device as default device. hr: %x","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDeviceAudioEnumerationAsyncHelpers.cpp";"RtcPalDeviceAudioEnumerationHelperWASAPI::GetDefaultDeviceIdsThreadFunc"
655e908b:"[cid:%s] Volume is set on non-active device. Using default mode to set volume","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalDevicePlatformAPI.cpp";"RtcPalDevicePlatformSetVolumeInfoWithTimeout"
65d98698:"[cid:%s] ActivateIAudioEndpointVolumeThreadFunc failed with error hr=%x","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDeviceAudioEnumerationAsyncHelpers.cpp";"RtcPalDeviceAudioEnumerationHelperWASAPI::ActivateIAudioEndpointVolumeWithTimeout"
65ec7e8c:"[cid:%s] Using timeout mode to query device format. Overall timeout count: %d. Max allowed timeout count overall: %dTimeout count for current device: %d. Max allowed timeout count per device: %d","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDeviceAudioEnumerationHelperWASAPI.cpp";"RtcPalDeviceAudioEnumerationHelperWASAPI::PopulateAudioDeviceInfoBasedOnAudioClient"
6624149c:"[cid:%s] Inited: AddressFamily %d Win32 %d","D:/a/_work/1/s/MSRTC/msrtc/src/rtcpal/network/windows/AddrChangeNotif.cpp";"CRtcPalAddressChangeNotification::Init"
664ccbca:"[cid:%s] ActivateMMDeviceInterfaceThreadFunc failed with error hr=%x","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDeviceAudioEnumerationAsyncHelpers.cpp";"RtcPalDeviceAudioEnumerationHelperWASAPI::ActivateIAudioClientWithTimeout"
6826f6ef:"[cid:%s] Not initialized yet","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDeviceAudioBoostControlImpl.cpp";"RtcPalDeviceAudioBoostControlImpl::SetAutoBoostControlEnabled"
684daa95:"[cid:%s] WinRT device id %ls updated for device with classic device id %ls","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDevQueryHelper.cpp";"RtcPalDevQueryHelper::UpdateWinrtDeviceInfoOnClassicDevice"
69355a90:"[cid:%s] pal_audio: activate[nullptr] hr=0x%08X","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDevicePlatformWASAPI.cpp";"RtcPalDevicePlatformWASAPI::ActivateAudioDevice"
69c97a18:"[cid:%s] Enter Media Streaming Mode: guid(%s) Win32=0x%x","D:/a/_work/1/s/MSRTC/msrtc/src/rtcpal/network/windows/netsvc.cpp";"RtcPalWlanMSM::DelayedEnterMSM"
6ad3a29b:"[cid:%s] Failed to populate form factor info for device: type=%d, id=%ls. Error code returned: hr=%x. Using default value for device form factor","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDeviceAudioEnumerationHelperWASAPI.cpp";"RtcPalDeviceAudioEnumerationHelperWASAPI::PopulateNonCriticalAudioDeviceInfo"
6bdfc04f:"[cid:%s] AudioRender (%ls): Destroyed","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalDeviceAudioWASAPI.cpp";"RtcPalDeviceAudioRenderWASAPI::~RtcPalDeviceAudioRenderWASAPI"
6d52fe03:"[cid:%s] Deliver simple device event for EDeviceHostClosed","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/VirtualDeviceExtension.cpp";"RtcPalExtendedVirtualAudioDeviceImpl::PRtcPalVadDestructDeviceCBInternal"
6d5c229b:"[cid:%s] Failed to UpdateWinrtDeviceInfoOnClassicDevice with hr = %8x","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDevicePlatformWASAPI.cpp";"RtcPalDevicePlatformWASAPI::UpdateAudioDeviceList"
6d7b8b39:"[cid:%s] Failed to populate pnp name for device: id=%ls. Error code returned: hr=%x. Using default values for pnp name and connectinon type","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDeviceAudioEnumerationAsyncHelpers.cpp";"RtcPalDeviceAudioEnumerationHelperWASAPI::GetPnpNameAndRelatedFieldsThreadFunc"
6dbb0ff8:"[cid:%s] ControlTrace(stop) failed with %lu\n","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalAudioOSLogHelper.cpp";"RtcPalAudioOSLogHelper::StopAudioOSLogging"
6deb73f9:"[cid:%s] Error happens while running one time proc last error: %x","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/VirtualDeviceExtension.cpp";"RtcPalExtendedVirtualAudioDeviceImpl::AudioDeviceThreadProc"
6e320e4d:"[cid:%s] Winrt device info updated on classic device (ID=%ls):WinRTID = %ls, lid: %d, dock: %d, panel: 0x%08x, certifiedDeviceId = %ls","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDevicePlatformWASAPI.cpp";"RtcPalDevicePlatformWASAPI::UpdateWinrtDeviceInfoOnClassicDevice"
6e3e35d7:"[cid:%s] Volume is queried on active sink device. Using volume control source to return volume","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalDevicePlatformAPI.cpp";"RtcPalDevicePlatformGetVolumeInfoWithTimeout"
6e9761bf:"[cid:%s] Failed to copy instance id buffer with err = %d","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDevQueryHelper.cpp";"QueryWinRTDeviceIdsCallback"
6eec02ee:"[cid:%s] Invalid stepLevelDb, (maxLevelDb[%f] - minLevelDb[%f]) / stepLevelDb[%f] is not a integer","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDeviceAudioBoostControlImpl.cpp";"RtcPalDeviceAudioBoostControlImpl::InitializeAsync"
6fc610cb:"[cid:%s] Sample Ref=%d after release, Func=0x%p, Context=0x%p","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/VirtualAudioSample.cpp";"RtcPal::VirtualAudioSampleImpl::Release"
6fca2ee9:"[cid:%s] Skipping collection of WiFi stats as requested through metrics flags.","D:/a/_work/1/s/MSRTC/msrtc/src/rtcpal/SystemMetrics/RtcPalSystemMetricsClassic.cpp";"RtcPalSystemMetricsCollectorClassic::OnTimerCallback"
6fcb6d03:"[cid:%s] Unable to allocate %d bytes for properties structure.\n","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalAudioOSLogHelper.cpp";"RtcPalAudioOSLogHelper::StartAudioOSLoggingInternal"
70179b2b:"[cid:%s] OnAddrChangeEventSignaled: unexpected error: WSAIoctl return error %d: AddressFamily: %d","D:/a/_work/1/s/MSRTC/msrtc/src/rtcpal/network/windows/AddrChangeNotif.cpp";"CRtcPalAddressChangeNotification::OnAddrChangeEventSignaled"
7092d5e5:"[cid:%s] pal_audio: async enumeration starts","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDevicePlatformWASAPI.cpp";"RtcPalDevicePlatformWASAPI::EnumerateAudioDevices"
70a491b4:"[cid:%s] Found WinRT device id %ls for device with classic device id %ls","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDevQueryHelper.cpp";"QueryWinRTDeviceIdsCallback"
70d50d96:"[cid:%s] AudioLoopback (%ls): Destroyed","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalDeviceAudioWASAPI.cpp";"RtcPalDeviceAudioLoopbackWASAPI::~RtcPalDeviceAudioLoopbackWASAPI"
711066e7:"[cid:%s] Set speaker system volume to: %d","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalDeviceAudioWASAPI.cpp";"RtcPalDeviceAudioRenderWASAPI::SetMasterVolumeFromEcsSetting"
71bdb05c:"[cid:%s] Failed enqueue sample to the sample queue","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/VirtualDeviceExtension.cpp";"RtcPalExtendedVirtualAudioRenderDeviceImpl::PRtcPALVADReleaseBufferCBackInternal"
7252887b:"[cid:%s] boost info: autoBoostControlSupported = %d, manualBoostControlSupported = %d, minLevelDb = %f, maxLevelDb = %f, stepLevelDb = %f","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDeviceAudioBoostControlImpl.cpp";"RtcPalDeviceAudioBoostControlImpl::InitializeAsync"
72765c3b:"[cid:%s] pal_audio: VirtualDeviceVolumeControlThreadFunc starts","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/VirtualPlatform.cpp";"RtcPalVirtualDevicePlatform::VolumeControlWithTimeout"
7283abce:"[cid:%s] Num of enum operations timeouts exceeded max allowed count. Enum operations timeout count overall: %d Max allowed timeout count overall: %dEnum operations timeout count for current device: %d Max allowed timeout count per device: %dNot querying device format. Will use default values","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDeviceAudioEnumerationHelperWASAPI.cpp";"RtcPalDeviceAudioEnumerationHelperWASAPI::PopulateAudioDeviceInfoBasedOnAudioClient"
730bd1fd:"[cid:%s] Failed to populate raw stream support info for device: type=%d, id=%ls. Error code returned: hr=%x. Using default values for raw support","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDeviceAudioEnumerationAsyncHelpers.cpp";"RtcPalDeviceAudioEnumerationHelperWASAPI::PopulateInfoFromPropStoreThreadFunc"
7346a42c:"[cid:%s] pal_audio: populate more information for device (id=%ls, vid=%x, pid=%x, connection=%d).","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalDevicePlatformWinrtHelper.cpp";"RtcPalDevicePlatformWinRTHelper::PopulateWinRTDevicePropertiesDevInfo"
742fe702:"[cid:%s] Failed to get client session vol controller. hr: %x","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalDeviceAudioWASAPI.cpp";"RtcPalDeviceAudioRenderWASAPI::Start"
743f36d3:"[cid:%s] Volume is queried on active source device. Using volume control source to return volume","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalDevicePlatformAPI.cpp";"RtcPalDevicePlatformGetVolumeInfoWithTimeout"
74b420a7:"[cid:%s] pal_audio: after reordering, [type=%d,fn=%ls,pnp=%ls,ff=%d,dad=%d,dcd=%d,caps=0x%I64X, priorityScore=%u]","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalWASAPIHelpers.cpp";"RtcPal::SortDeviceList"
7542d7ce:"[cid:%s] Create audio device thread failed with error: %x","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/VirtualDeviceExtension.cpp";"RtcPalExtendedVirtualAudioDeviceImpl::InitializeAudioDeviceThread"
75508d71:"[cid:%s] GetNework failed. hr=0x%x","D:/a/_work/1/s/MSRTC/msrtc/src/rtcpal/network/windows/netsvc.cpp";"UpdateNetworkId"
7564981c:"[cid:%s] Closed","D:/a/_work/1/s/MSRTC/msrtc/src/rtcpal/network/windows/WinSockNT.cpp";"RtcPalSocket::Close"
75e91884:"[cid:%s] GetPropStoreAndDeviceNameThreadFunc failed with timeout error","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDeviceAudioEnumerationAsyncHelpers.cpp";"RtcPalDeviceAudioEnumerationHelperWASAPI::GetPropStoreAndDeviceNameWithTimeout"
764a2616:"[cid:%s] pal_audio: Retrying audio client initialization due to failure: hr = %x","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalDeviceAudioWASAPI.cpp";"RtcPalDeviceAudioWASAPI::InitializeAudioClient"
765bafb5:"[cid:%s] EmulatedAcceptEx fails 0x%x synchronously OVERLAPPED %p","D:/a/_work/1/s/MSRTC/msrtc/src/rtcpal/network/windows/WinSockNT.cpp";"RtcPalSocket::AsyncAcceptViaEventSelect"
7866e7e4:"[cid:%s] Failed to set mmcss thread characteristic with errorcode: %d","D:/a/_work/1/s/MSRTC/msrtc/src/rtcpal/kernel/windows/win32/thread.cpp";"RtcPalThreadContext::AttachMmcss"
7918706f:"[cid:%s] Session Vol: %d Scaling Factor: %d","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalDeviceAudio.cpp";"RtcPalDeviceAudio::UpdateVolume"
795053ca:"[cid:%s] pal_audio: we are enumerating only virtual devices","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDevicePlatformWASAPI.cpp";"RtcPalDevicePlatformWASAPI::EnumerateAudioDevices"
7a249ce2:"[cid:%s] Get IAudioSessionControl for loopback device faild with 0x%x","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalDeviceAudioWASAPI.cpp";"RtcPalDeviceAudioLoopbackWASAPI::Activate"
7a6dcc90:"[cid:%s] Failed to startup MF","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/common/windows/RtcPalMFAudioSourceReader.cpp";"IMFAudioSourceReader::InitMediaFoundation"
7b23d56e:"[cid:%s] WaitForCompletion hanging for %u times for %s, back to sync mode for this call","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalDevicePlatformAPI.cpp";"WaitForCompletionIfNeccessary"
7bb024b3:"[cid:%s] pal_audio: terminal type from usb controller: 0x%x","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDevicePlatformWASAPI.cpp";"RtcPalDevicePlatformWASAPI::EnumerateAudioDevicesInternal"
7bd0f986:"[cid:%s] VirtualDeviceVolumeControlThreadFunc failed with error %x","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/VirtualPlatform.cpp";"RtcPalVirtualDevicePlatform::VolumeControlWithTimeout"
7d317d3d:"[cid:%s] Session volume at end of call: %d is different from init setting: %d. Caching user override of speaker session volume","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalDeviceAudioWASAPI.cpp";"RtcPalDeviceAudioRenderWASAPI::Stop"
7d38ce1e:"[cid:%s] AcceptEx succeeds synchronously OVERLAPPED %p","D:/a/_work/1/s/MSRTC/msrtc/src/rtcpal/network/windows/WinSockNT.cpp";"RtcPalSocket::AsyncAcceptViaAcceptEx"
7e704f3d:"[cid:%s] GetGraphicDeviceInfo failed. hr=0x%08X","D:/a/_work/1/s/MSRTC/msrtc/src/rtcpal/SystemMetrics/RtcPalSystemMetricsClassic.cpp";"RtcPalGetSystemConfigurationInfo"
7ee53d2e:"[cid:%s] pal_audio: ActivateIAudioEndpointVolumeThreadFunc starts","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDeviceAudioEnumerationAsyncHelpers.cpp";"RtcPalDeviceAudioEnumerationHelperWASAPI::ActivateIAudioEndpointVolumeWithTimeout"
7f385018:"[cid:%s] Failed to get volume on active sink. Using default mode to return volume","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalDevicePlatformAPI.cpp";"RtcPalDevicePlatformGetVolumeInfoWithTimeout"
7f974dbe:"[cid:%s] Virtual Platform has not been initialized!","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/VirtualPlatform.cpp";"RtcPalCreateVirtualAudioDevice"
803a436b:"[cid:%s] No enough memory to create AsyncParas","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDeviceAudioBoostControlImpl.cpp";"RtcPalDeviceAudioBoostControlImpl::RtcPalDeviceAudioBoostControlImpl"
80e79139:"[cid:%s] Failed to create virtual device. Duplicate device ID found!","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/VirtualPlatform.cpp";"RtcPalVirtualDevicePlatform::CreateVirtualAudioDevice"
81fdff8e:"[cid:%s] Really stop logging for: %d, force flag: %d\n","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalAudioOSLogHelper.cpp";"RtcPalAudioOSLogHelper::StopAudioOSLogging"
8251bcdc:"[cid:%s] Device list is null","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalWASAPIHelpers.cpp";"RtcPal::SortDeviceList"
826bca95:"[cid:%s] clear device info collection of type RtcPalDeviceType_e(%d) failed with hr = 0x%x","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalDevicePlatform.cpp";"RtcPalDevicePlatform::AddDeviceInfoListToCollection"
82b6d7c9:"[cid:%s] ConnectEx fails 0x%x OVERLAPPED %p","D:/a/_work/1/s/MSRTC/msrtc/src/rtcpal/network/windows/WinSockNT.cpp";"RtcPalSocket::AsyncConnectViaConnectEx"
8328ce7d:"[cid:%s] Model Id for %ls is %ls","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalDevicePlatformWinrtHelper.cpp";"RtcPalDevicePlatformWinRTHelper::FillModelIdAndManufacturer"
832b34a8:"[cid:%s] GetName failed. hr=0x%x","D:/a/_work/1/s/MSRTC/msrtc/src/rtcpal/network/windows/netsvc.cpp";"UpdateNetworkId"
842f7224:"[cid:%s] Given data pointer can not be null.","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalTestHelper.cpp";"RtcPalAudioLoggingHelper::RtcPalAudioLogging::WriteRawData"
847a6422:"[cid:%s] Num of enum operations timeouts exceeded max allowed count. Enum operations timeout count overall: %d Max allowed timeout count overall: %dEnum operations timeout count for current device: %d Max allowed timeout count per device: %dNot querying device pnp name and related fields. Will use default values","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDeviceAudioEnumerationAsyncHelpers.cpp";"RtcPalDeviceAudioEnumerationHelperWASAPI::GetInfoFromPropStoreWithTimeout"
852b5976:"[cid:%s] GetNeworkId failed. hr=0x%x","D:/a/_work/1/s/MSRTC/msrtc/src/rtcpal/network/windows/netsvc.cpp";"UpdateNetworkId"
85871355:"[cid:%s] Using timeout mode to query device format. Overall timeout count: %d. Timeout count for current device: %d","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDevicePlatformWASAPI.cpp";"RtcPalDevicePlatformWASAPI::EnumerateAudioDevicesInternal"
85a792fa:"[cid:%s] socket close duration was longer than expected: %f seconds.","D:/a/_work/1/s/MSRTC/msrtc/src/rtcpal/network/windows/CloseSocketOffload.cpp";"CloseSocketOffload::CloseSocket"
85b92c29:"[cid:%s] EnableTrace for provider %d failed with %lu\n","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalAudioOSLogHelper.cpp";"RtcPalAudioOSLogHelper::StartAudioOSLoggingInternal"
8622f2b8:"[cid:%s] stop virtual device with return: %x","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/VirtualDeviceExtension.cpp";"RtcPalExtendedVirtualAudioCaptureDeviceImpl::PRtcPALVADStopCBackInternal"
8747e9dc:"[cid:%s] Failed to QI for IMFTransform interface","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/common/windows/RtcPalMFAudioSourceReader.cpp";"IMFAudioSourceReader::CreateResamplerMFT"
87875dff:"[cid:%s] Using timeout mode to query gain fields and device prop store info. Overall timeout count: %d. Timeout count for current device: %d","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDevicePlatformWASAPI.cpp";"RtcPalDevicePlatformWASAPI::EnumerateAudioDevicesInternal"
88035b8b:"[cid:%s] dequeue sample: time:%I64d, frame sample dequeued:%d, pSample:%p, totalSamplesDequeued:%I64d, samplesDeliveredToLMS:%I64d, totalSamplesDeliveredToLMS:%I64d, fFrameReady:%d, expectedFramesDelivered:%I64d, framesDelivered:%I64d, m_startTime:%I64d, m_lastStartTime:%I64d, object:0x%I64X","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/VirtualAudioSample.cpp";"RtcPal::VirtualAudioSourceImpl::DequeueSample"
8833d192:"[cid:%s] Request stop logging for: %d\n","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalAudioOSLogHelper.cpp";"RtcPalAudioOSLogHelper::StopAudioOSLogging"
897ba6f2:"[cid:%s] rtDevTS = %llu,  rtQPCTS = %llu, total samples queued = %llu","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/VirtualDeviceExtension.cpp";"RtcPalExtendedVirtualAudioRenderDeviceImpl::PRtcPALVADReleaseBufferCBackInternal"
8b8772d6:"[cid:%s] Failed to get vid pid for device: id=%ls. Error code returned: hr=%x. Using default values for vid, pid","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDeviceAudioEnumerationAsyncHelpers.cpp";"RtcPalDeviceAudioEnumerationHelperWASAPI::GetPnpNameAndRelatedFieldsThreadFunc"
8b9f55c6:"[cid:%s] Releasing session control","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalDeviceAudioWASAPI.cpp";"RtcPalDeviceAudioWASAPI::~RtcPalDeviceAudioWASAPI"
8c67ba53:"[cid:%s] pal_audio: there are multiple enumerations happening at same time, this is not expected (%d)!","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDevicePlatformWASAPI.cpp";"RtcPalDevicePlatformWASAPI::EnumerateAudioDevices"
8c7b1a36:"[cid:%s] AddressFamily=%u Protocol=%u","D:/a/_work/1/s/MSRTC/msrtc/src/rtcpal/network/windows/WinSockNT.cpp";"RtcPalSocket::Initialize"
8c961038:"[cid:%s] RemoveWinRTEnumInEffect = %d","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDevicePlatformWASAPI.cpp";"RtcPalDevicePlatformWASAPI::EnumerateAudioDevices"
8cc61d11:"[cid:%s] Flush WPP Event: EventsLost %lu LogBuffersLost %lu NumberOfBuffers %lu FreeBuffers %lu BuffersWritten %lu","D:/a/_work/1/s/MSRTC/msrtc/src/rtcpal/tracing/windows/WppConsumer.cpp";"DumpWppStatistics"
8df322a2:"[cid:%s] Session Mute: %d","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalDeviceAudio.cpp";"RtcPalDeviceAudio::UpdateVolume"
8ea0ef4d:"[cid:%s] EmulatedAcceptEx: DoAccept fails: 0x%x queue OVERLAPPED %p for completion","D:/a/_work/1/s/MSRTC/msrtc/src/rtcpal/network/windows/WinSockNT.cpp";"RtcPalSocket::OnEventSelectAccept"
8f256b7f:"[cid:%s] Virtual device created: type= %d pVdevice= %p, total: %d","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/VirtualPlatform.cpp";"RtcPalVirtualDevicePlatform::CreateVirtualAudioDevice"
8f6c1fb7:"[cid:%s] Failed to recieve sample from Resampler","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/common/windows/RtcPalMFAudioSourceReader.cpp";"IMFAudioSourceReader::ResampleData"
8f7c8660:"[cid:%s] Unmuted speaker session volume","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalDeviceAudioWASAPI.cpp";"RtcPalDeviceAudioRenderWASAPI::SetSessionVolume"
906394fc:"[cid:%s] pal_audio: ActivateMMDeviceInterfaceThreadFunc starts","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDeviceAudioEnumerationAsyncHelpers.cpp";"RtcPalDeviceAudioEnumerationHelperWASAPI::ActivateIAudioClientWithTimeout"
90ae7537:"[cid:%s] Query registry key value failed.","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/common/utils/windows/RtcPalDeviceUtilsWinClassic.cpp";"RtcPalDeviceUtils::GetMachineId"
90d07df8:"[cid:%s] Error(%d), Failed to get reader first audio stream type","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/common/windows/RtcPalMFAudioSourceReader.cpp";"IMFAudioSourceReader::OpenMFSourceReader"
9154b7fe:"[cid:%s] %s@%d: if_TRUE(%s) == true","D:/a/_work/1/s/MSRTC/msrtc/src/rtcpal/tracing/windows/WppConsumer.cpp";"RtcPalTraceCondIfTrue"
91637e1d:"[cid:%s] Failed to initialize Callback","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/common/windows/RtcPalMFAudioSourceReader.cpp";"IMFAudioSourceReader::Open"
91f6d551:"[cid:%s] SetVolumeOnActiveSource failed with hr=0x%x","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDevicePlatformWASAPI.cpp";"RtcPalDevicePlatformWASAPI::SetVolumeOnActiveSource"
92010127:"[cid:%s] Error(%d), Failed to create Resampler MFT","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/common/windows/RtcPalMFAudioSourceReader.cpp";"IMFAudioSourceReader::OpenMFSourceReader"
928e9398:"[cid:%s] pal_audio: reset the form actor from RPADFormFactor_End to RPADFormFactor_UnknownFormFactor","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDevicePlatformWASAPI.cpp";"RtcPalDevicePlatformWASAPI::EnumerateAudioDevicesInternal"
92dbf725:"[cid:%s] Failed to create GetDefaultDeviceIdThreadFuncParams","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDeviceAudioEnumerationAsyncHelpers.cpp";"RtcPalDeviceAudioEnumerationHelperWASAPI::GetDefaultDeviceIdWithTimeout"
945c6e79:"[cid:%s] Socket destroyed","D:/a/_work/1/s/MSRTC/msrtc/src/rtcpal/network/windows/WinSockNT.cpp";"RtcPalSocket::~RtcPalSocket"
955997fd:"[cid:%s] tries to decide on whether need to recover or not, hr=0x%08X, totalCountOfFailures=%d, continueoutsCountOfFailure=%d","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalDeviceAudioWASAPI.cpp";"RtcPalDeviceAudioWASAPI::IsInitializationFailureRecoverable"
961a018a:"[cid:%s] RtcPalSystemMetricsCollector: callback stats -> callbacks %d (completed %d), avg: %.2f, max: %lld, first: %lld, last: %lld","D:/a/_work/1/s/MSRTC/msrtc/src/rtcpal/SystemMetrics/RtcPalSystemMetricsClassic.cpp";"RtcPalSystemMetricsCollectorClassic::LogStatsOnStop"
96f154ec:"[cid:%s] pal_audio: enumeration updates device for type: %d, count: %d, hr=%x","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDevicePlatformWASAPI.cpp";"RtcPalDevicePlatformWASAPI::EnumerateAudioDevices"
972944ec:"[cid:%s] pal_audio: IAudioClient::Initialize succeeded","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalWASAPIHelpers.cpp";"RtcPal::InitializeAudioClientInExclusiveMode"
97979529:"[cid:%s] asyncInfo->get_ErrorCode failed with hr = %8x","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/common/windows/winrt/PnpAsyncCompletionHandler.h";"RtcPalAsyncCompleteHandler::Invoke"
979b28d9:"[cid:%s] pal_audio: populate volume control information succeeded (id=%ls, mingain=%d, maxgain=%d, gainstep=%d, scalingfactor= %d, hardwaregainsupport=%d).","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalDevicePlatformWinrtHelper.cpp";"RtcPalDevicePlatformWinRTHelper::PopulateWinRTDevicePropertiesDevInfo"
980f01a9:"[cid:%s] AcceptEx: SO_UPDATE_ACCEPT_CONTEXT fails 0x%x OVERLAPPED %p","D:/a/_work/1/s/MSRTC/msrtc/src/rtcpal/network/windows/WinSockNT.cpp";"RtcPalSocket::OnAcceptExCompletion"
982b24b3:"[cid:%s] Failed to create socket close offload workitem object with error 0x%x.","D:/a/_work/1/s/MSRTC/msrtc/src/rtcpal/network/windows/CloseSocketOffload.cpp";"CloseSocketOffload::Initialize"
98b152ec:"[cid:%s] GetMessage returns -1 %d times before current valid msg=%d.","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalMsgThread.cpp";"RtcPalMsgThread::RtcPalMsgThreadProc"
9971a1ec:"[cid:%s] pal_audio: populate device format succeeded (id=%ls, channel=%d, samplerate=%d, bitspersample=%d).","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalDevicePlatformWinrtHelper.cpp";"RtcPalDevicePlatformWinRTHelper::PopulateWinRTDevicePropertiesDevInfo"
997ead60:"[cid:%s] Inited: AddressFamily %d","D:/a/_work/1/s/MSRTC/msrtc/src/rtcpal/network/windows/AddrChangeNotif.cpp";"CRtcPalAddressChangeNotification::Init"
9996ac1c:"[cid:%s] ConnectEx succeed OVERLAPPED %p","D:/a/_work/1/s/MSRTC/msrtc/src/rtcpal/network/windows/WinSockNT.cpp";"RtcPalSocket::OnConnectExCompletion"
99cbace5:"[cid:%s] Master volume at end of call: %d is different from init setting: %d. Caching user override of speaker master volume","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalDeviceAudioWASAPI.cpp";"RtcPalDeviceAudioRenderWASAPI::Stop"
9a2a4eea:"[cid:%s] spDCDevice->GetId failed. Will set first enumerated device as default device. hr: %x","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDeviceAudioEnumerationAsyncHelpers.cpp";"RtcPalDeviceAudioEnumerationHelperWASAPI::GetDefaultDeviceIdsThreadFunc"
9b124814:"[cid:%s] enum[guidJackSubType=%08x-%04x-%04x-%02x%02x-%02x%02x%02x%02x%02x%02x],form factor=%d","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDeviceAudioEnumerationAsyncHelpers.cpp";"RtcPalDeviceAudioEnumerationHelperWASAPI::PopulateInfoFromPropStoreThreadFunc"
9b4c565a:"[cid:%s] pal_audio: IAudioClient::Initialize failed, hr=%u","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalWASAPIHelpers.cpp";"RtcPal::InitializeAudioClient"
9b889abf:"[cid:%s] DeviceEffects for device %ls before change = %x, after Change = %x, effect filled = %d","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalDeviceAudio.cpp";"RtcPalDeviceAudio::OnDeviceEffectsChange"
9bf78137:"[cid:%s] Given RVDAudioFormatDesc_t pointer can not be null.","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalTestHelper.cpp";"RtcPalAudioLoggingHelper::RtcPalAudioLogging::WriteFormat"
9cc1519a:"[cid:%s] pal_audio: replace the form factor with usb controller[tertype=0x%x, new ff=%d, updated ff=%d]","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDevicePlatformWASAPI.cpp";"RtcPalDevicePlatformWASAPI::EnumerateAudioDevicesInternal"
9e250035:"[cid:%s] pal_audio: adding virtual device, eType = %d, privateId = %ls, device = %p, hr = 0x%x","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDevicePlatformWASAPI.cpp";"RtcPalDevicePlatformWASAPI::RegisterVirtualAudioDevices"
9ee7f899:"[cid:%s] Failed to create Resampler","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/common/windows/RtcPalMFAudioSourceReader.cpp";"IMFAudioSourceReader::GenerateMFT"
9f9e8bc2:"[cid:%s] Failed to GetName with hr = %#x","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDeviceAudioBoostControlImpl.cpp";"RtcPalDeviceAudioBoostControlImpl::GetPart"
a0797fd7:"[cid:%s] Request start logging for: %d\n","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalAudioOSLogHelper.cpp";"RtcPalAudioOSLogHelper::StartAudioOSLogging"
a07acbea:"[cid:%s] Client session volume set to: %f","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalDeviceAudioWASAPI.cpp";"RtcPalDeviceAudioRenderWASAPI::Start"
a086e2c0:"[cid:%s] Can\'t dynamic_cast POSRcallback","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/common/windows/RtcPalMFAudioSourceReader.cpp";"IMFAudioSourceReader::InitializeCallback"
a1578474:"[cid:%s] Exception occurred when new RVDDeviceInfo_t, %s","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalDevicePlatformWinrtHelper.cpp";"RtcPalDevicePlatformWinRTHelper::PopulateWinRTDevicePropertiesDevInfo"
a15f0dee:"[cid:%s] Start virtual audio device failed: 0x%x","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/VirtualDeviceExtension.cpp";"RtcPalExtendedVirtualAudioRenderDeviceImpl::PRtcPALVADStartCBackInternal"
a19a4b95:"[cid:%s] Can\'t allocate destination buffer","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/common/windows/RtcPalMFAudioSourceReader.cpp";"IMFAudioSourceReader::SynchronousRead"
a1d3bae2:"[cid:%s] Another instance of RtcPalMsgThread already exists","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalMsgThread.cpp";"RtcPalMsgThread::CreateMsgThread"
a304e0fe:"[cid:%s] Releasing endpoint volume","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalDeviceAudioWASAPI.cpp";"RtcPalDeviceAudioWASAPI::~RtcPalDeviceAudioWASAPI"
a32ab834:"[cid:%s] Invalid value received for speaker session volume ECS setting: %d . Ignoring ECS value and not setting session volume","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalDeviceAudioWASAPI.cpp";"RtcPalDeviceAudioRenderWASAPI::Activate"
a333904f:"[cid:%s] Error while stopping device: %x","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/VirtualDeviceExtension.cpp";"RtcPalExtendedVirtualAudioRenderDeviceImpl::PRtcPALVADStopCBackInternal"
a3560b9f:"[cid:%s] AudioCapture (%ls): Activate - FormatTag=%u Channels=%d SamplesPerSec=%d BitsPerSample=%u 0x%x","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalDeviceAudioWASAPI.cpp";"RtcPalDeviceAudioCaptureWASAPI::Activate"
a36bc344:"[cid:%s] Failed to set Resampler Output Type","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/common/windows/RtcPalMFAudioSourceReader.cpp";"IMFAudioSourceReader::GenerateMFT"
a3981553:"[cid:%s] Client session volume before incall stream start: %f","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalDeviceAudioWASAPI.cpp";"RtcPalDeviceAudioRenderWASAPI::Start"
a4f8106e:"[cid:%s] exiting device property change event proc. dwStatus=%u","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalDevicePlatformPropertyChangeNotificationFilter.cpp";"RtcPalDevicePlatformPropertyChangeNotificationFilter::DevicePropertyChangedEventProc"
a522dc90:"[cid:%s] Invalid boost value, stepLevelDb = %f","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDeviceAudioBoostControlImpl.cpp";"RtcPalDeviceAudioBoostControlImpl::InitializeAsync"
a568c8b4:"[cid:%s] AudioLoopback (%ls): Activate - Clock frequency from IAudioClock does not match device format. ClockFrequency=%llu, AvgBytesPerSec=%d","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalDeviceAudioWASAPI.cpp";"RtcPalDeviceAudioLoopbackWASAPI::Activate"
a629e4f0:"[cid:%s] AcceptEx: SO_UPDATE_ACCEPT_CONTEXT succeeds OVERLAPPED %p","D:/a/_work/1/s/MSRTC/msrtc/src/rtcpal/network/windows/WinSockNT.cpp";"RtcPalSocket::OnAcceptExCompletion"
a6414f3d:"[cid:%s] Start audio OS tracing for %d","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalAudioOSLogHelper.cpp";"RtcPalAudioOSLogHelper::StartAudioOSLoggingInternal"
a6577ab8:"[cid:%s] RtcPalInitProtocols failed. WSA=0x%x","D:/a/_work/1/s/MSRTC/msrtc/src/rtcpal/network/windows/netsvc.cpp";"RtcPalInitProtocols"
a7057da4:"[cid:%s] Invalid arg. !pTimeOutCountCurrentDevice. Not querying device gain info. Will use default values","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDeviceAudioEnumerationHelperWASAPI.cpp";"RtcPalDeviceAudioEnumerationHelperWASAPI::PopulateGainControlInfo"
a77c6ad8:"[cid:%s] AcceptEx fails 0x%x OVERLAPPED %p","D:/a/_work/1/s/MSRTC/msrtc/src/rtcpal/network/windows/WinSockNT.cpp";"RtcPalSocket::OnAcceptExCompletion"
a78400a1:"[cid:%s] Failed to allocate output sample for Resampler","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/common/windows/RtcPalMFAudioSourceReader.cpp";"IMFAudioSourceReader::ResampleData"
a823873a:"[cid:%s] Failed to SetEvent on m_hCloseDoneEvent","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalDevicePlatformPropertyChangeNotificationFilter.cpp";"RtcPalDevicePlatformPropertyChangeNotificationFilter::DevicePropertyChangedEventProc"
a8692e35:"[cid:%s] pal_audio: ActivateIAudioEndpointVolumeThreadFunc waiting time, time=%llu, hr=%x","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDeviceAudioEnumerationAsyncHelpers.cpp";"RtcPalDeviceAudioEnumerationHelperWASAPI::ActivateIAudioEndpointVolumeWithTimeout"
a8a0a5a6:"[cid:%s] System dB Vol: %d Scaling Factor: %d","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalDeviceAudio.cpp";"RtcPalDeviceAudio::UpdateVolume"
a9e21bc8:"[cid:%s] Failed to set context","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/common/windows/RtcPalMFAudioSourceReader.cpp";"IMFAudioSourceReader::SynchronousRead"
ab08d32f:"[cid:%s] pal_audio: ActivateMMDeviceInterfaceThreadFunc waiting time, time=%llu, hr=%x","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDeviceAudioEnumerationAsyncHelpers.cpp";"RtcPalDeviceAudioEnumerationHelperWASAPI::ActivateIAudioClientWithTimeout"
ab1f7288:"[cid:%s] pal_audio: enum[type=%d,fn=%ls,pnp=%ls,ff=%d,dad=%d,dcd=%d,caps=0x%I64X,priorityScore=%d,uiIsInternalDevice=%d,channel=%d, samplerate=%d, bitspersample=%d]","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDevicePlatformWASAPI.cpp";"RtcPalDevicePlatformWASAPI::EnumerateAudioDevicesInternal"
abac17ee:"[cid:%s] m_pAsyncParas->pLoudnessControl->GetEnabled failed with hr = %#x ","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDeviceAudioBoostControlImpl.cpp";"RtcPalDeviceAudioBoostControlImpl::GetAutoBoostControlEnabled"
add3fe76:"[cid:%s] Audio session disconnected. Disconnect reason: %d","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalDeviceAudio.cpp";"RtcPalDeviceAudio::NotifySessionDisconnect"
adeea8d8:"[cid:%s] [myinfo] model = %ls","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalDevicePlatformWinrtHelper.cpp";"RtcPalDevicePlatformWinRTHelper::GetCertifiedListDeviceID"
ae4ba0a4:"[cid:%s] couldn\'t create object query with hr = %8x","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDevQueryHelper.cpp";"RtcPalDevQueryHelper::Query"
afa9997b:"[cid:%s] Failed to get the function name","D:/a/_work/1/s/MSRTC/msrtc/src/rtcpal/common/windows/RtcPalWinUtils.cpp";"wcsncpy_s_trace"
afb4beb0:"[cid:%s] Failed to allocate IMFMediaBuffer for Resampler","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/common/windows/RtcPalMFAudioSourceReader.cpp";"IMFAudioSourceReader::ResampleData"
b143fddb:"[cid:%s] Get ISimpleAudioVolume successful","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalDeviceAudioWASAPI.cpp";"RtcPalDeviceAudioCaptureWASAPI::Activate"
b146b89a:"[cid:%s] pal_audio: PopulateInfoFromPropStoreThreadFuncParams waiting time, time=%llu, hr=%x","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDeviceAudioEnumerationAsyncHelpers.cpp";"RtcPalDeviceAudioEnumerationHelperWASAPI::GetInfoFromPropStoreWithTimeout"
b1c931fc:"[cid:%s] %s@%d: if_ERROR(%s) == true","D:/a/_work/1/s/MSRTC/msrtc/src/rtcpal/tracing/windows/WppConsumer.cpp";"RtcPalTraceCondIfTrue"
b209f504:"[cid:%s] Successfully got function pointers for DevCreateObjectQueryEx and DevCloseObjectQuery","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDevQueryHelper.cpp";"RtcPalDevQueryHelper::LoadDllAndGetFuncPointers"
b30c737e:"[cid:%s] create audio client for format change failed and use default. hr=%x","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDeviceAudioEnumerationHelperWASAPI.cpp";"RtcPalDeviceAudioEnumerationHelperWASAPI::PopulateAudioDeviceInfoBasedOnAudioClient"
b317aa4d:"[cid:%s] Failed to open property store for device: type=%d, id=%ls. Error code returned: hr=%x. Using default values for non critical device properties","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDeviceAudioEnumerationHelperWASAPI.cpp";"RtcPalDeviceAudioEnumerationHelperWASAPI::PopulateNonCriticalAudioDeviceInfo"
b36af14f:"[cid:%s] Error happens while waiting for render event, last error: %x","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/VirtualDeviceExtension.cpp";"RtcPalExtendedVirtualAudioDeviceImpl::AudioDeviceThreadProc"
b3ca84df:"[cid:%s] async operation failed with hr = %8x","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/common/windows/winrt/PnpAsyncCompletionHandler.h";"RtcPalAsyncCompleteHandler::Invoke"
b4af5262:"[cid:%s] Failed to load %ls with hr = %8x","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDevQueryHelper.cpp";"RtcPalDevQueryHelper::LoadDllAndGetFuncPointers"
b4d37480:"[cid:%s] update capability as stereo capable for device: %ls, capabilities: %llu","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDevicePlatformWASAPI.cpp";"RtcPalDevicePlatformWASAPI::UpdateDeviceCapabilitiesIfCertifiedStereoCapable"
b4ed1b46:"[cid:%s] Get IAudioSessionControl successful","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalDeviceAudioWASAPI.cpp";"RtcPalDeviceAudioCaptureWASAPI::Activate"
b5916cea:"[cid:%s] Failed to create ActivateIAudioClientThreadFuncParams","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDeviceAudioEnumerationAsyncHelpers.cpp";"RtcPalDeviceAudioEnumerationHelperWASAPI::ActivateIAudioClientWithTimeout"
b656c592:"[cid:%s] StartTrace() failed with %lu\n","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalAudioOSLogHelper.cpp";"RtcPalAudioOSLogHelper::StartAudioOSLoggingInternal"
b6810ac7:"[cid:%s] Failed to add IMFMediaBuffer to sample","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/common/windows/RtcPalMFAudioSourceReader.cpp";"IMFAudioSourceReader::ResampleData"
b706b963:"[cid:%s] EmulatedConnectEx: fail to disable WSAEventSelect: 0x%x","D:/a/_work/1/s/MSRTC/msrtc/src/rtcpal/network/windows/WinSockNT.cpp";"RtcPalSocket::OnEventSelectConnectCompletion"
b763e63c:"[cid:%s] m_frameSampleJBQueueCapacity:%d, m_samplesPerFrame:%d, m_sampleSizeinBytes:%d, object:0x%I64X","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/VirtualAudioSample.cpp";"RtcPal::VirtualAudioSourceImpl::SetFrameSampleJBQueueCap"
b77fda5b:"Device id is %ls","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalAudioOSLogHelper.cpp";"DeviceIdLoggingComponent::logMachineId"
b90c6a4b:"[cid:%s] Queue EmulatedConnectEx OVERLAPPED %p for completion","D:/a/_work/1/s/MSRTC/msrtc/src/rtcpal/network/windows/WinSockNT.cpp";"RtcPalSocket::OnEventSelectConnectCompletion"
b9fc7f35:"[cid:%s] AcceptEx succeeds: connection OVERLAPPED %p","D:/a/_work/1/s/MSRTC/msrtc/src/rtcpal/network/windows/WinSockNT.cpp";"RtcPalSocket::OnAcceptExCompletion"
bafe7048:"[cid:%s] %s","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/common/windows/RtcPalDevice.cpp";"RtcPalDeviceTrace"
bb46a72b:"[cid:%s] Failed to CoCreate Resampler","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/common/windows/RtcPalMFAudioSourceReader.cpp";"IMFAudioSourceReader::CreateResamplerMFT"
bb4ebfc5:"[cid:%s] pal_audio: enum usb host controller failed: %x","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDevicePlatformWASAPI.cpp";"RtcPalDevicePlatformWASAPI::EnumerateAudioDevicesInternal"
bbcb54fd:"[cid:%s] GetPropStoreAndDeviceNameThreadFunc failed with error hr=%x","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDeviceAudioEnumerationAsyncHelpers.cpp";"RtcPalDeviceAudioEnumerationHelperWASAPI::GetPropStoreAndDeviceNameWithTimeout"
bbf53caf:"[cid:%s] RecvFrom error 0x%x","D:/a/_work/1/s/MSRTC/msrtc/src/rtcpal/network/windows/WinSockNT.cpp";"RtcPalSocket::RecvFrom"
bc0be62e:"[cid:%s] RtcPalGetBestSourceAddress: fails with %d","D:/a/_work/1/s/MSRTC/msrtc/src/rtcpal/network/windows/iphlpapi.cpp";"RtcPalGetBestSourceAddress"
c0790908:"[cid:%s] pal_audio: skip one device since it\'s failed to query device, type=%d","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDevicePlatformWASAPI.cpp";"RtcPalDevicePlatformWASAPI::EnumerateAudioDevicesInternal"
c11647cc:"[cid:%s] EmulatedConnectEx: fail to disable non-blocking mode: 0x%x","D:/a/_work/1/s/MSRTC/msrtc/src/rtcpal/network/windows/WinSockNT.cpp";"RtcPalSocket::OnEventSelectConnectCompletion"
c12bb739:"[cid:%s] pal_audio: VolumeControlThreadFunc starts","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDevicePlatformWASAPI.cpp";"RtcPalDevicePlatformWASAPI::VolumeControlWithTimeout"
c236b5df:"[cid:%s] CertifiedListId:=%ls","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalDevicePlatformWinrtHelper.cpp";"RtcPalDevicePlatformWinRTHelper::GetCertifiedListDeviceID"
c2cd14aa:"[cid:%s] ActivateIAudioClientWithTimeout failed with timeout error. Will use default values for device formatTime out counts for current device: %d Time out counts overall: %d","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDeviceAudioEnumerationHelperWASAPI.cpp";"RtcPalDeviceAudioEnumerationHelperWASAPI::PopulateAudioDeviceInfoBasedOnAudioClient"
c30d411c:"[cid:%s] MF installation check failed: MFReadWrite.dll missing.","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/common/windows/RtcPalMFAudioSourceReader.cpp";"IMFAudioSourceReader::Open"
c33e63b9:"[cid:%s] EmulatedConnectEx OVERLAPPED %p","D:/a/_work/1/s/MSRTC/msrtc/src/rtcpal/network/windows/WinSockNT.cpp";"RtcPalSocket::AsyncConnectViaEventSelect"
c34e2d0e:"[cid:%s] pal_audio: GetPnpNameAndRelatedFieldsThreadFunc waiting time, time=%llu, hr=%x","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDeviceAudioEnumerationAsyncHelpers.cpp";"RtcPalDeviceAudioEnumerationHelperWASAPI::GetPnpNameAndRelatedFieldsWithTimeout"
c35546d4:"[cid:%s] WifiStats: sendRate:%lu, recvRate:%lu, sendKbps:%lu, recvKbps:%lu, transFrameCount:%llu","D:/a/_work/1/s/MSRTC/msrtc/src/rtcpal/SystemMetrics/RtcPalSystemMetricsClassic.cpp";"RtcPalSystemMetricsCollectorClassic::OnTimerCallback"
c37793a9:"[cid:%s] spDCDevice->GetId failed. Will set first enumerated device as default communications device","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDevicePlatformWASAPI.cpp";"RtcPalDevicePlatformWASAPI::EnumerateAudioDevicesInternal"
c4332a2b:"[cid:%s] pal_audio: activate[%ls] hr=0x%08X","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDevicePlatformWASAPI.cpp";"RtcPalDevicePlatformWASAPI::ActivateAudioDevice"
c45dce6c:"[cid:%s] Start OS logging: %s","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalAudioOSLogHelper.h";"ScopedRtcPalAudioOSLogHelper::ScopedRtcPalAudioOSLogHelper"
c48525ba:"[cid:%s] Queue CloseSocket OVERLAPPED %p for completion","D:/a/_work/1/s/MSRTC/msrtc/src/rtcpal/network/windows/WinSockNT.cpp";"RtcPalSocket::Close"
c55b08d6:"[cid:%s] Failed to populate form factor info for device: type=%d, id=%ls. Error code returned: hr=%x. Using default value for device form factor","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDeviceAudioEnumerationAsyncHelpers.cpp";"RtcPalDeviceAudioEnumerationHelperWASAPI::PopulateInfoFromPropStoreThreadFunc"
c57efbc5:"[cid:%s] Working around by adjusting number of channels to %u","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalWASAPIHelpers.cpp";"RtcPal::InitializeAudioClient"
c59003c9:"[cid:%s] GetVolumeOnActiveSink failed with hr=0x%x","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDevicePlatformWASAPI.cpp";"RtcPalDevicePlatformWASAPI::GetVolumeOnActiveSink"
c593bcb1:"[cid:%s] error occurred while waiting for device property change work item close. dwStatus=%u","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalDevicePlatformPropertyChangeNotificationFilter.cpp";"RtcPalDevicePlatformPropertyChangeNotificationFilter::StopDevicePropertyChangeWorkItem"
c5caf72f:"[cid:%s] Format not Ok, suggested closest format: ch=%d, f=%d, bps=%d","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalWASAPIHelpers.cpp";"RtcPal::InitializeAudioClient"
c7db113f:"[cid:%s] Session volume is 0 at end of call. Not caching user override of speaker session volume","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalDeviceAudioWASAPI.cpp";"RtcPalDeviceAudioRenderWASAPI::Stop"
c8255858:"[cid:%s] Failed to set volume on active sink. Using default mode to set volume","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalDevicePlatformAPI.cpp";"RtcPalDevicePlatformSetVolumeInfoWithTimeout"
c857450b:"[cid:%s] GetDefaultDeviceIdsThreadFunc failed with error hr=%x eRole: %d. Will mark first device as default device","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDeviceAudioEnumerationAsyncHelpers.cpp";"RtcPalDeviceAudioEnumerationHelperWASAPI::GetDefaultDeviceIdWithTimeout"
c8a2cbc2:"[cid:%s] PdhAddEnglishCounterW for path %ls failed with error %ld","D:/a/_work/1/s/MSRTC/msrtc/src/rtcpal/SystemMetrics/RtcPalSystemMetricsClassic.cpp";"RtcPalSystemMetricsCollectorClassic::CollectPdhMetrics"
ca0d1e01:"[cid:%s] Enumerate and update WinRT Device ID failed with hr=0x%08X. ","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDevicePlatformWASAPI.cpp";"RtcPalDevicePlatformWASAPI::UpdateAudioDeviceList"
ca254df5:"[cid:%s] Failed to get client session volume. hr: %x","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalDeviceAudioWASAPI.cpp";"RtcPalDeviceAudioRenderWASAPI::Start"
cab912f1:"[cid:%s] This is a rendering device","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDeviceAudioBoostControlImpl.cpp";"RtcPalDeviceAudioBoostControlImpl::GetPart"
cca0a1e4:"[cid:%s] Starvation occurred in frame sample jitter buffer. time:%I64d, m_samplesDeliveredToLMS:%I64d, m_totalSamplesDeliveredToLMS:%I64d, object:0x%I64X","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/VirtualAudioSample.cpp";"RtcPal::VirtualAudioSourceImpl::DequeueSample"
ccd8feea:"[cid:%s] Device count is 0","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalWASAPIHelpers.cpp";"RtcPal::SortDeviceList"
cd4f13a4:"[cid:%s] Failed to set volume on active source. Using default mode to set volume","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalDevicePlatformAPI.cpp";"RtcPalDevicePlatformSetVolumeInfoWithTimeout"
cdc23a40:"[cid:%s] m_pAsyncParas->pGainControl->SetEnabled failed with hr = %#x","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDeviceAudioBoostControlImpl.cpp";"RtcPalDeviceAudioBoostControlImpl::SetAutoBoostControlEnabled"
cdf1c864:"[cid:%s] m_pAsyncParas->pLoudnessControl->SetEnabled failed with hr2 = %#x","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDeviceAudioBoostControlImpl.cpp";"RtcPalDeviceAudioBoostControlImpl::SetAutoBoostControlEnabled"
ce8c560f:"[cid:%s] No sample in the queue. glitch info=%d, device position = %llu, expected finished time=%llu","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/VirtualDeviceExtension.cpp";"RtcPalExtendedVirtualAudioRenderDeviceImpl::AudioDeviceThreadProcOneTime"
cef14222:"[cid:%s] PdhCollectQueryData failed with error %ld","D:/a/_work/1/s/MSRTC/msrtc/src/rtcpal/SystemMetrics/RtcPalSystemMetricsClassic.cpp";"RtcPalSystemMetricsCollectorClassic::CollectPdhMetrics"
cf4d63f3:"[cid:%s] Init spk vol override read from cache: %d","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalDeviceAudioWASAPI.cpp";"RtcPalDeviceAudioRenderWASAPI::SetMasterVolumeFromEcsSetting"
cf52877c:"[cid:%s] pWinrtDevInfo is nullptr","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDevicePlatformWASAPI.cpp";"RtcPalDevicePlatformWASAPI::UpdateWinrtDeviceInfoOnClassicDevice"
cf65e9c1:"[cid:%s] total samples created:%I64d, delivered:%I64d, enqueued:%I64d, dequeued:%I64d, deliveredToLMS:%I64d, unexpectedSize:%I64d, object:0x%I64X","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/VirtualAudioSample.cpp";"RtcPal::VirtualAudioSourceImpl::GetPerCallMetrics"
cf9bd3d3:"[cid:%s] EnableTrace failed with %lu\n","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalAudioOSLogHelper.cpp";"RtcPalAudioOSLogHelper::StopAudioOSLogging"
d02b0678:"[cid:%s] spDADevice->GetId failed. Will set first enumerated device as default audio device","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDevicePlatformWASAPI.cpp";"RtcPalDevicePlatformWASAPI::EnumerateAudioDevicesInternal"
d04bbf9e:"[cid:%s] Certified list deivce Id for %ls is %ls","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalDevicePlatformWinrtHelper.cpp";"RtcPalDevicePlatformWinRTHelper::FillCertifiedListDeviceIDForBTDevices"
d0b2bd2e:"[cid:%s] GetDevicePeriod, hr=%#x, hnsDefaultDevicePeriod: %llu, hnsMinimumDevicePeriod: %llu","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalWASAPIHelpers.cpp";"RtcPal::InitializeAudioClientInExclusiveMode"
d1095e7b:"[cid:%s] System Mute: %d","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalDeviceAudio.cpp";"RtcPalDeviceAudio::UpdateVolume"
d18c348d:"[cid:%s] enum[JackSubType=%u, guidJackSubType=%08x-%04x-%04x-%02x%02x-%02x%02x%02x%02x%02x%02x],form factor=%d","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDeviceAudioEnumerationHelperWASAPI.cpp";"RtcPalDeviceAudioEnumerationHelperWASAPI::PopulateNonCriticalAudioDeviceInfo"
d1a1f0fe:"[cid:%s] Successfully opted out of Windows audio ducking","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalDeviceAudioWASAPI.cpp";"RtcPalDeviceAudioRenderWASAPI::Activate"
d2172ae4:"[cid:%s] OnEventSelectAccept: WSAEnumNetworkEvents fail 0x%x","D:/a/_work/1/s/MSRTC/msrtc/src/rtcpal/network/windows/WinSockNT.cpp";"RtcPalSocket::OnEventSelectAccept"
d22690a8:"[cid:%s] IOCP destroyed","D:/a/_work/1/s/MSRTC/msrtc/src/rtcpal/network/windows/WinSockIOCPNT.cpp";"RtcPalIOCP::~RtcPalIOCP"
d2ed76f0:"[cid:%s] Failed to set context","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/common/windows/RtcPalMFAudioSourceReader.cpp";"IMFAudioSourceReader::ResampleData"
d36f6682:"[cid:%s] ReorderDeviceList ECS key is not set. Not setting device priority score","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalWASAPIHelpers.cpp";"RtcPal::SetDevicePriorityScore"
d4132116:"[cid:%s] pal_audio: IAudioClient::GetDevicePeriod failed, hr=%u. Using default device period","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalWASAPIHelpers.cpp";"RtcPal::InitializeAudioClientInExclusiveMode"
d432695c:"[cid:%s] OnAddrChangeEventSignaled: AddressFamily %d Win32 %d Cached %p CachedAll %p","D:/a/_work/1/s/MSRTC/msrtc/src/rtcpal/network/windows/AddrChangeNotif.cpp";"CRtcPalAddressChangeNotification::OnAddrChangeEventSignaled"
d4f5f3e7:"[cid:%s] RegisterSocket socket=%p: succeeds","D:/a/_work/1/s/MSRTC/msrtc/src/rtcpal/network/windows/WinSockIOCPNT.cpp";"RtcPalIOCP::RegisterSocket"
d5c36dc0:"[cid:%s] Stop existing trace session. status=%lu\n","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalAudioOSLogHelper.cpp";"RtcPalAudioOSLogHelper::StartLogSession"
d6a619f6:"[cid:%s] fFlag value is unexpected, SetMute to FALSE, hr=0x%08X","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalDeviceAudioWASAPI.cpp";"RtcPalDeviceAudioWASAPI::GetSystemVolAndUpdateInternalVolInfo"
d6c95c8d:"[cid:%s] PopulateInfoFromPropStoreThreadFuncParams failed with timeout error. Using default values for prop store fields","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDeviceAudioEnumerationAsyncHelpers.cpp";"RtcPalDeviceAudioEnumerationHelperWASAPI::GetInfoFromPropStoreWithTimeout"
d722f8e4:"[cid:%s] Can\'t allocate callback handler","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/common/windows/RtcPalMFAudioSourceReader.cpp";"IMFAudioSourceReader::InitializeCallback"
d731a4ff:"[cid:%s] Failed to call m_pAudioDevice->GetIMMDevice with hr = %8x","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDeviceAudioBoostControlImpl.cpp";"RtcPalDeviceAudioBoostControlImpl::RtcPalDeviceAudioBoostControlImpl"
d78e411a:"[cid:%s] CreateInstance (NetworkListManager) failed. hr=0x%x","D:/a/_work/1/s/MSRTC/msrtc/src/rtcpal/network/windows/netsvc.cpp";"UpdateNetworkId"
d7fc09b7:"[cid:%s] ReorderDeviceList ECS key is not set. Not setting device priority score","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDevicePlatformWASAPI.cpp";"RtcPalDevicePlatformWASAPI::EnumerateAudioDevicesInternal"
d8bbf37c:"[cid:%s] pal_audio: activate_loopback[nullptr] hr=0x%08X","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDevicePlatformWASAPI.cpp";"RtcPalDevicePlatformWASAPI::ActivateAudioLoopbackDevice"
d8f7cb25:"[cid:%s] Total Samples Produced: %llu, Queued: %llu, Delivered: %llu","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/VirtualDeviceExtension.cpp";"RtcPalExtendedVirtualAudioRenderDeviceImpl::PRtcPALVADStopCBackInternal"
d91f09d1:"[cid:%s] Failed to populate jack subtype info for device: type=%d, id=%ls. Error code returned: hr=%x. Not refining device form factor","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDeviceAudioEnumerationHelperWASAPI.cpp";"RtcPalDeviceAudioEnumerationHelperWASAPI::PopulateNonCriticalAudioDeviceInfo"
d9e1466b:"[cid:%s] GetMessage returns -1 10 times continuously. Break message pump.","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalMsgThread.cpp";"RtcPalMsgThread::RtcPalMsgThreadProc"
d9fdec42:"[cid:%s] Using timeout mode to query device prop store fields. Overall timeout count: %d. Max allowed timeout count overall: %dTimeout count for current device: %d. Max allowed timeout count per device: %d","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDeviceAudioEnumerationHelperWASAPI.cpp";"RtcPalDeviceAudioEnumerationHelperWASAPI::PopulateNonCriticalAudioDeviceInfo"
da6be99d:"[cid:%s] pal_audio: IAudioClient::Initialize failed, hr=%u","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalWASAPIHelpers.cpp";"RtcPal::InitializeAudioClientInExclusiveMode"
da8c4514:"[cid:%s] Released all COM interfaces","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalDeviceAudioWASAPI.cpp";"RtcPalDeviceAudioWASAPI::~RtcPalDeviceAudioWASAPI"
dbbcde2b:"[cid:%s] ConnectEx unexpectedly succeed OVERLAPPED %p","D:/a/_work/1/s/MSRTC/msrtc/src/rtcpal/network/windows/WinSockNT.cpp";"RtcPalSocket::AsyncConnectViaConnectEx"
dc123d48:"[cid:%s] Request start logging, but failed. %d\n","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalAudioOSLogHelper.cpp";"RtcPalAudioOSLogHelper::StartAudioOSLogging"
dcb62905:"[cid:%s] GetPnpNameAndRelatedFieldsWithTimeout failed with timeout error. Will use default values for pnp name and related fieldsTime out counts for current device: %d Time out counts overall: %d","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDeviceAudioEnumerationAsyncHelpers.cpp";"RtcPalDeviceAudioEnumerationHelperWASAPI::GetInfoFromPropStoreWithTimeout"
dceb83c6:"[cid:%s] AudioCapture (%ls): Stop - LockBuffer=%u FramesCaptured=%u","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalDeviceAudioWASAPI.cpp";"RtcPalDeviceAudioCaptureWASAPI::Stop"
dcfef9ff:"[cid:%s] Queue Close request OVERLAPPED %p for completion","D:/a/_work/1/s/MSRTC/msrtc/src/rtcpal/network/windows/WinSockNT.cpp";"RtcPalSocket::ReorderRecv"
dd420df9:"[cid:%s] Virtual device deleted:  type= %d pVdevice= %p, total: %d","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/VirtualPlatform.cpp";"RtcPalVirtualDevicePlatform::DeleteVirtualAudioDevice"
dd51a493:"[cid:%s] EmulatedConnectEx: succeed OVERLAPPED %p","D:/a/_work/1/s/MSRTC/msrtc/src/rtcpal/network/windows/WinSockNT.cpp";"RtcPalSocket::OnEventSelectConnectCompletion"
dd5334e0:"[cid:%s] Received session volume changed notification for device type: %d","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalDeviceAudio.cpp";"RtcPalDeviceAudio::UpdateVolume"
de4f9104:"[cid:%s] System Vol: %d Scaling Factor: %d","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalDeviceAudio.cpp";"RtcPalDeviceAudio::UpdateVolume"
de6fd346:"[cid:%s] Failed to get certified list deivce Id for %ls with hr=0x%08X","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalDevicePlatformWinrtHelper.cpp";"RtcPalDevicePlatformWinRTHelper::PopulateWinRTDevicePropertiesDevInfo"
deb9a6b0:"[cid:%s] Failed to lock sample buffer","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/common/windows/RtcPalMFAudioSourceReader.cpp";"IMFAudioSourceReader::SynchronousRead"
e1332578:"[cid:%s] Failed to create thread for func from %s","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/common/utils/windows/RtcPalThreadHelper.cpp";"WaitForCompletion"
e153e9b0:"[cid:%s] pal_audio: Client Initialization Finished. hr = %x, forceStreamModeAsDefault=%d, continuous failure=%d, total failure=%d, waiting time=%d, forceResetAudioClient=%d","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalDeviceAudioWASAPI.cpp";"RtcPalDeviceAudioWASAPI::InitializeAudioClient"
e1c8e51f:"[cid:%s] Fail to get post workitem to UI thread: 0x%x","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/ActivateAudio.cpp";"RtcPalActivateAudioInterface"
e1db3ed7:"[cid:%s] [ECS]: Set ecs key: %s/%s, value: %s","D:/a/_work/1/s/MSRTC/msrtc/src/rtcpal/common/common/RtcPalEcsSettings.cpp";"RtcPalSetEcsSetting"
e1f92fcc:"[cid:%s] PopulateGainControlInfo with timeout failed with error %x. Using default values. Overall timeout count: %d. Timeout count for current device: %d","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDeviceAudioEnumerationHelperWASAPI.cpp";"RtcPalDeviceAudioEnumerationHelperWASAPI::PopulateNonCriticalAudioDeviceInfo"
e26fa86e:"[cid:%s] %s@%d: if_TRACE(%s) == %s","D:/a/_work/1/s/MSRTC/msrtc/src/rtcpal/tracing/windows/WppConsumer.cpp";"RtcPalTraceCond"
e2828f56:"[cid:%s] Failed to query winrt device ids using dev query helper with hr = %8x","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDevicePlatformWASAPI.cpp";"RtcPalDevicePlatformWASAPI::UpdateAudioDeviceList"
e2b59bf8:"[cid:%s] Assert is hit at:%s:%d:%s","D:/a/_work/1/s/MSRTC/msrtc/src/rtcpal/instrument/RtcPalAssertReporter.cpp";"AssertReporter::ReportAssert"
e38f8253:"[cid:%s] OnDefaultDeviceChanged, data flow: %d, role: %d, default device id: %ls","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalDevicePlatformWASAPICallback.cpp";"RtcPalDevicePlatformWASAPICallback::OnDefaultDeviceChanged"
e38f9d80:"[cid:%s] Failed to GetLevelRange, hrVolumeControl = %#x","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDeviceAudioBoostControlImpl.cpp";"RtcPalDeviceAudioBoostControlImpl::InitializeAsync"
e3cd4afd:"[cid:%s] accept connection succeeds: AcceptSd=%p","D:/a/_work/1/s/MSRTC/msrtc/src/rtcpal/network/windows/WinSockNT.cpp";"RtcPalSocket::DoAccept"
e3d08128:"[cid:%s] Not setting master volume since current level (%f) is higher than ecs setting","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalDeviceAudioWASAPI.cpp";"RtcPalDeviceAudioRenderWASAPI::SetMasterVolumeFromEcsSetting"
e4063c0f:"[cid:%s] Failed to activate IAudioVolumeLevel, hrVolumeControl = %#x","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDeviceAudioBoostControlImpl.cpp";"RtcPalDeviceAudioBoostControlImpl::InitializeAsync"
e52ebc8a:"[cid:%s] Open regkey returns %d","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/mfhelper/mfhelper.cpp";"CheckMFCorePresent"
e5bab2d0:"[cid:%s] pal_audio: Initializing device[type=%d,dn=%ls,pnp=%ls] WindowsBlueOrHigher: %d, UsingRawMode: %d, UsingCommunicationsMode: %d","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDeviceAudioClassic.cpp";"RtcPalDeviceAudioWASAPI::InitializeAudioClientPlatformSpecific"
e5e7ff49:"[cid:%s] Not setting speaker session volume since user overrode it in previous call","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalDeviceAudioWASAPI.cpp";"RtcPalDeviceAudioRenderWASAPI::SetSessionVolume"
e6024a57:"[cid:%s] No sample available in the pool","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/VirtualDeviceExtension.cpp";"RtcPalExtendedVirtualAudioRenderDeviceImpl::PRtcPALVADLockBufferCBackInternal"
e63a26b2:"[cid:%s] Received ecs key to disable setting of speaker master volume. Not setting master volume","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalDeviceAudioWASAPI.cpp";"RtcPalDeviceAudioRenderWASAPI::SetMasterVolumeFromEcsSetting"
e649e493:"[cid:%s] Failed to create RtcPalDeviceAudioBoostControlImpl, out of memory","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalDeviceAudioWASAPI.cpp";"RtcPalDeviceAudioWASAPI::Initialize"
e65fa505:"[cid:%s] OnAddrChangeEventSignaled: AddressFamily %d Win32 %d","D:/a/_work/1/s/MSRTC/msrtc/src/rtcpal/network/windows/AddrChangeNotif.cpp";"CRtcPalAddressChangeNotification::OnAddrChangeEventSignaled"
e89b841b:"[cid:%s] Listen fails 0x%x, backlog=%d","D:/a/_work/1/s/MSRTC/msrtc/src/rtcpal/network/windows/WinSockNT.cpp";"RtcPalSocket::Listen"
e96c2faf:"[cid:%s] pal_audio: GetPnpNameAndRelatedFieldsThreadFunc starts","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDeviceAudioEnumerationAsyncHelpers.cpp";"RtcPalDeviceAudioEnumerationHelperWASAPI::GetPnpNameAndRelatedFieldsWithTimeout"
ea6abd0d:"[cid:%s] Init spk session vol override read from cache: %d","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalDeviceAudioWASAPI.cpp";"RtcPalDeviceAudioRenderWASAPI::SetSessionVolume"
eaa3462a:"[cid:%s] [ECS]: StorageKey %s is found, value is: %llu","D:/a/_work/1/s/MSRTC/msrtc/src/rtcpal/common/common/RtcPalEcsSettings.cpp";"InitSettingValue"
eb129d12:"[cid:%s] pal_audio: enum usb host controller failed: %x","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDevicePlatformWASAPI.cpp";"RtcPalDevicePlatformWASAPI::EnumerateUSBControllerAsyncProc"
eb1b6fd3:"[cid:%s] spEnumerator->GetDefaultAudioEndpoint failed. Will set first enumerated device as default communications device","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDevicePlatformWASAPI.cpp";"RtcPalDevicePlatformWASAPI::EnumerateAudioDevicesInternal"
eb4138a5:"[cid:%s] copied data has not been fully read out. read: %d, real data: %d","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/VirtualAudioSample.cpp";"RtcPal::VirtualAudioSampleImpl::ReleaseBuffer"
eb45ef1b:"[cid:%s] pal_audio: IAudioClient::GetMixFormat failed, hr=%u","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalWASAPIHelpers.cpp";"RtcPal::InitializeAudioClient"
ec1ad7fb:"[cid:%s] AcceptEx fails 0x%x OVERLAPPED %p","D:/a/_work/1/s/MSRTC/msrtc/src/rtcpal/network/windows/WinSockNT.cpp";"RtcPalSocket::AsyncAcceptViaAcceptEx"
ec2e4cdd:"[cid:%s] Releasing session volume","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalDeviceAudioWASAPI.cpp";"RtcPalDeviceAudioWASAPI::~RtcPalDeviceAudioWASAPI"
ecb98117:"[cid:%s] WifiStats: channel:%lu, signal:%lu, handovers:%lu, channelSwitches:%lu, freq:%lu, channelReassociations:%lu, BatteryCharged:%lu","D:/a/_work/1/s/MSRTC/msrtc/src/rtcpal/SystemMetrics/RtcPalSystemMetricsClassic.cpp";"RtcPalSystemMetricsCollectorClassic::OnTimerCallback"
edf671c4:"[cid:%s] EmulatedAcceptEx: WSAEWOUDBLOCK: retry OVERLAPPED %p later","D:/a/_work/1/s/MSRTC/msrtc/src/rtcpal/network/windows/WinSockNT.cpp";"RtcPalSocket::OnEventSelectAccept"
ee47cf20:"[cid:%s] AudioLoopback: LockBuffer=%u FramesCaptured=%u","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalDeviceAudioWASAPI.cpp";"RtcPalDeviceAudioLoopbackWASAPI::LockBuffer"
ee7712d5:"[cid:%s] Failed to populate guid container id for device: type=%d, id=%ls. Error code returned: hr=%x. Using default value for guid contrainer id","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDeviceAudioEnumerationHelperWASAPI.cpp";"RtcPalDeviceAudioEnumerationHelperWASAPI::PopulateNonCriticalAudioDeviceInfo"
ee979796:"[cid:%s] Get IAudioSessionControl for render device faild with 0x%x","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalDeviceAudioWASAPI.cpp";"RtcPalDeviceAudioRenderWASAPI::Activate"
eecd770c:"[cid:%s] GetNetworkConnections failed. hr=0x%x","D:/a/_work/1/s/MSRTC/msrtc/src/rtcpal/network/windows/netsvc.cpp";"UpdateNetworkId"
f03c5545:"[cid:%s] Failed ReleaseBuffer on sample, hr = 0x%x","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/VirtualDeviceExtension.cpp";"RtcPalExtendedVirtualAudioRenderDeviceImpl::PRtcPALVADReleaseBufferCBackInternal"
f0d2bbff:"[cid:%s] Dev query aborted","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDevQueryHelper.cpp";"QueryWinRTDeviceIdsCallback"
f1752a4a:"[cid:%s] IsFormatSupported, hr=%#x","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalWASAPIHelpers.cpp";"RtcPal::InitializeAudioClient"
f185fec2:"[cid:%s] OnPropertyValueChanged, (%ls,{%08X-%04hX-%04hX-%02X%02X-%02X%02X%02X%02X%02X%02X}, pid=%d)","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalDevicePlatformWASAPICallback.cpp";"RtcPalDevicePlatformWASAPICallback::OnPropertyValueChanged"
f1d76e35:"[cid:%s] Error allocating sample pool","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/VirtualDeviceExtension.cpp";"RtcPalExtendedVirtualAudioRenderDeviceImpl::InitializeAudioRenderDevice"
f20b46e1:"[cid:%s] OnDeviceRemoved, device id: %ls","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalDevicePlatformWASAPICallback.cpp";"RtcPalDevicePlatformWASAPICallback::OnDeviceRemoved"
f2533bf5:"[cid:%s] AudioCapture: LockBuffer=%u FramesCaptured=%u","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalDeviceAudioWASAPI.cpp";"RtcPalDeviceAudioCaptureWASAPI::LockBuffer"
f2b83c5d:"[cid:%s] Given obj pointer (%s) and data pointer (%s) can not be null.","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalTestHelper.cpp";"RtcPalAudioLoggingHelper::WriteRawData"
f2db5f15:"[cid:%s] Failed to get RtcPalAudioMediaExtensionType_VirtualDeviceManager extension, hr = 0x%x","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDevicePlatformWASAPI.cpp";"RtcPalDevicePlatformWASAPI::UnregisterVirtualAudioDevices"
f3f578ab:"[cid:%s] ActivateIAudioEndpointVolumeWithTimeout failed with error hr=%x. Will use default values for gain fields","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDeviceAudioEnumerationHelperWASAPI.cpp";"RtcPalDeviceAudioEnumerationHelperWASAPI::PopulateGainControlInfo"
f4127c8f:"[cid:%s] AudioLoopback (%ls): Activated - FormatTag=%u Channels=%d SamplesPerSec=%d BitsPerSample=%u 0x%x","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalDeviceAudioWASAPI.cpp";"RtcPalDeviceAudioLoopbackWASAPI::Activate"
f53043fb:"[cid:%s] Failed to populate display name for device: type=%d, id=%ls. Error code returned: hr=%x. Using default value for device display name","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDeviceAudioEnumerationHelperWASAPI.cpp";"RtcPalDeviceAudioEnumerationHelperWASAPI::PopulateNonCriticalAudioDeviceInfo"
f5d5a54a:"[cid:%s] AudioRender (%ls): Start","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalDeviceAudioWASAPI.cpp";"RtcPalDeviceAudioRenderWASAPI::Start"
f5dbf8be:"[cid:%s] Failed to send complete signal","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDevQueryHelper.cpp";"QueryWinRTDeviceIdsCallback"
f5fc4f44:"[cid:%s] spEnumerator->GetDefaultAudioEndpoint failed. Will set first enumerated device as default audio device","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDevicePlatformWASAPI.cpp";"RtcPalDevicePlatformWASAPI::EnumerateAudioDevicesInternal"
f61ab90f:"[cid:%s] GetPropStoreAndDeviceNameWithTimeout failed with error hr=%x. Will use default values for friendly name and display name and other prop store fields","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDeviceAudioEnumerationAsyncHelpers.cpp";"RtcPalDeviceAudioEnumerationHelperWASAPI::GetInfoFromPropStoreWithTimeout"
f6e66d03:"[cid:%s] pal_audio: activate_loopback[%ls] hr=0x%08X","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDevicePlatformWASAPI.cpp";"RtcPalDevicePlatformWASAPI::ActivateAudioLoopbackDevice"
f86ba81c:"[cid:%s] Queue Close request OVERLAPPED %p for completion","D:/a/_work/1/s/MSRTC/msrtc/src/rtcpal/network/windows/WinSockNT.cpp";"RtcPalSocket::RecvFrom"
f8ef4f6f:"[cid:%s] Async Enum Parameters: type=%d ulCategoryMask=%d timeoutMode=%d timeoutMs=%d m_maxTimeOutsPerDevice=%d maxTimeOutsOverall=%d, timeOutCountOverall=%d","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDevicePlatformWASAPI.cpp";"RtcPalDevicePlatformWASAPI::EnumerateAudioDevices"
f9023523:"[cid:%s] ActivateIAudioEndpointVolumeThreadFunc failed with timeout error","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDeviceAudioEnumerationAsyncHelpers.cpp";"RtcPalDeviceAudioEnumerationHelperWASAPI::ActivateIAudioEndpointVolumeWithTimeout"
f97919f2:"[cid:%s] Using timeout mode to query default device ids. Overall timeout count: %d","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDevicePlatformWASAPI.cpp";"RtcPalDevicePlatformWASAPI::EnumerateAudioDevicesInternal"
f9b45786:"[cid:%s] start virtual audio device with return: %x, requestSampleThreadEnabled = %u","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/VirtualDeviceExtension.cpp";"RtcPalExtendedVirtualAudioCaptureDeviceImpl::PRtcPALVADStartCBackInternal"
f9f70884:"[cid:%s] Setting system volume for device %ls to %d Scaling Factor: %d","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalDeviceAudioWASAPI.cpp";"RtcPalDeviceAudioWASAPI::SetAudioVolume"
f9fbc2a7:"[cid:%s] Sample size changed. sampleSizeinBytes:%d, expectedSampleSizeinBytes:%d, totalSamplesUnexpectedSize:%I64d, object:0x%I64X","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/VirtualAudioSample.cpp";"RtcPal::VirtualAudioSourceImpl::SetFrameSampleJBQueueCap"
fbd6ecaa:"[cid:%s] Close IOCP %p","D:/a/_work/1/s/MSRTC/msrtc/src/rtcpal/network/windows/WinSockIOCP.cpp";"RtcPalCloseSocketIOCP"
fc8f35dd:"[cid:%s] No enough memory","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDevQueryHelper.cpp";"QueryWinRTDeviceIdsCallback"
fcffcd70:"[cid:%s] delivered sample:%I64d, time:%I64d, pSample:%p, enqueued:%d, totalSamplesEnqueued:%I64d, object:0x%I64X","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/VirtualAudioSample.cpp";"RtcPal::VirtualAudioSourceImpl::DeliverSample"
fd732f6f:"[cid:%s] PdhOpenQueryW failed with error %ld","D:/a/_work/1/s/MSRTC/msrtc/src/rtcpal/SystemMetrics/RtcPalSystemMetricsClassic.cpp";"RtcPalSystemMetricsCollectorClassic::CollectPdhMetrics"
fd753d16:"[cid:%s] Num of enum operations timeouts exceeded max allowed count. Enum operations timeout count overall: %d Max allowed timeout count overall: %dEnum operations timeout count for current device: %d Max allowed timeout count per device: %dNot querying device format. Will use default values","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDeviceAudioEnumerationHelperWASAPI.cpp";"RtcPalDeviceAudioEnumerationHelperWASAPI::PopulateGainControlInfo"
fddf293e:"[cid:%s] MF is present, handle=%p, fMFHasAVCapture=%d","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/mfhelper/mfhelper.cpp";"RtcPalMFHasAVCapture"
fe57e29a:"[cid:%s] Get IAudioSessionControl for capture device faild with 0x%x","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/RtcPalDeviceAudioWASAPI.cpp";"RtcPalDeviceAudioCaptureWASAPI::Activate"
fef57221:"[cid:%s] [ECS]: Set ecs key: %s/%s, value: %s","D:/a/_work/1/s/MSRTC/msrtc/src/rtcpal/common/common/RtcPalEcsSettings.cpp";"RtcPalSetCallContextSetting"
ff0fce6f:"[cid:%s] Failed to populate gain control info for device: type=%d, id=%ls. Error code returned: hr=%x. Using default values for gain control fields","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDeviceAudioEnumerationHelperWASAPI.cpp";"RtcPalDeviceAudioEnumerationHelperWASAPI::PopulateNonCriticalAudioDeviceInfo"
ff348776:"[cid:%s] Num of enum operations timeouts %d exceeded max allowed timeout count %d. Not querying default id for eRole: %d. Will mark first device as default device","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDeviceAudioEnumerationAsyncHelpers.cpp";"RtcPalDeviceAudioEnumerationHelperWASAPI::GetDefaultDeviceIdWithTimeout"
ffc5b743:"[cid:%s] Failed to get driver version for device: id=%ls. Error code returned: hr=%x. Using default value for driver version","D:/a/_work/1/s/MSRTC/msrtc/src/rtcavpal/device/audio/windows/classic/RtcPalDeviceAudioEnumerationAsyncHelpers.cpp";"RtcPalDeviceAudioEnumerationHelperWASAPI::GetPnpNameAndRelatedFieldsThreadFunc"
