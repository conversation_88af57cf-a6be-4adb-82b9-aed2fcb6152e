using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Http.Extensions;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Graph.Communications.Common.Telemetry;
using Microsoft.Graph.Communications.Core.Notifications;
using INotificationProcessor = Microsoft.Graph.Communications.Core.Notifications.INotificationProcessor;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Threading.Tasks;
using AccureMD.MediaBot.Worker.Bot;
using AccureMD.MediaBot.Worker.Models.Constants;
using Newtonsoft.Json;

namespace AccureMD.MediaBot.Worker.Controllers
{
    /// <summary>
    /// Entry point for handling call-related web hook requests from Microsoft Graph.
    /// </summary>
    [Route(HttpRouteConstants.CallSignalingRoutePrefix)]
    public class CallsController : ControllerBase
    {
        /// <summary>
        /// The bot service.
        /// </summary>
        private readonly IBotService _botService;

        /// <summary>
        /// The logger.
        /// </summary>
        private readonly IGraphLogger _logger;

        /// <summary>
        /// Initializes a new instance of the <see cref="CallsController"/> class.
        /// </summary>
        /// <param name="botService">The bot service.</param>
        /// <param name="logger">The logger.</param>
        public CallsController(IBotService botService, IGraphLogger logger)
        {
            this._botService = botService;
            this._logger = logger;
        }

        /// <summary>
        /// Handle a callback for an incoming call.
        /// </summary>
        /// <returns>The action result.</returns>
        [HttpPost]
        [Route(HttpRouteConstants.OnIncomingRequestRoute)]
        public async Task<IActionResult> OnIncomingRequestAsync()
        {
            var log = $"Received HTTP {this.Request.Method}, {this.Request.Path.Value}";
            _logger.Info(log);

            var response = await ((INotificationProcessor)_botService.Client).ProcessNotificationAsync(ConvertHttpRequestToHttpRequestMessage(this.Request)).ConfigureAwait(false);

            var content = response.Content == null ? null : await response.Content?.ReadAsStringAsync();
            return Ok(content);
        }

        /// <summary>
        /// Handle a callback for an existing call.
        /// </summary>
        /// <returns>The action result.</returns>
        [HttpPost]
        [Route(HttpRouteConstants.OnNotificationRequestRoute)]
        public async Task<IActionResult> OnNotificationRequestAsync()
        {
            var log = $"Received HTTP {this.Request.Method}, {this.Request.Path}";
            _logger.Info(log);

            // Pass the incoming notification to the sdk.
            var response = await ((INotificationProcessor)_botService.Client).ProcessNotificationAsync(ConvertHttpRequestToHttpRequestMessage(this.Request)).ConfigureAwait(false);

            var content = response.Content == null ? null : await response.Content?.ReadAsStringAsync();
            return Ok(content);
        }

        /// <summary>
        /// Get all active calls.
        /// </summary>
        /// <returns>The action result.</returns>
        [HttpGet]
        [Route("calls")]
        public IActionResult GetCalls()
        {
            _logger.Info("Getting calls");

            if (_botService.CallHandlers.IsEmpty)
            {
                return StatusCode(204); // No content
            }

            var calls = new List<Dictionary<string, string>>();
            foreach (var callHandler in _botService.CallHandlers.Values)
            {
                var call = callHandler.Call;
                var values = new Dictionary<string, string>
                {
                    { "legId", call.Id },
                    { "scenarioId", call.ScenarioId.ToString() },
                    { "state", call.Resource.State.ToString() },
                };
                calls.Add(values);
            }
            return Ok(calls);
        }

        /// <summary>
        /// End a specific call.
        /// </summary>
        /// <param name="callLegId">Id of the call to end.</param>
        /// <returns>The action result.</returns>
        [HttpDelete]
        [Route("calls/{callLegId}")]
        public async Task<IActionResult> EndCallAsync(string callLegId)
        {
            var message = $"Ending call {callLegId}";
            _logger.Info(message);

            try
            {
                await _botService.EndCallByCallLegIdAsync(callLegId).ConfigureAwait(false);
                return Ok();
            }
            catch (Exception e)
            {
                _logger.Error(e, $"Error ending call {callLegId}");
                return StatusCode(500, e.ToString());
            }
        }

        /// <summary>
        /// Join a call.
        /// </summary>
        /// <param name="joinCallBody">The join call body.</param>
        /// <returns>The action result.</returns>
        [HttpPost]
        [Route("join")]
        public async Task<IActionResult> JoinCallAsync([FromBody] JoinCallBody joinCallBody)
        {
            try
            {
                _logger.Info($"Joining call with URL: {joinCallBody.JoinURL}");

                var call = await _botService.JoinCallAsync(joinCallBody).ConfigureAwait(false);

                var response = new
                {
                    CallId = call.Id,
                    ScenarioId = call.ScenarioId,
                    State = call.Resource.State.ToString(),
                    Message = "Successfully joined call"
                };

                return Ok(response);
            }
            catch (Exception e)
            {
                _logger.Error(e, $"Error joining call with URL: {joinCallBody.JoinURL}");
                return StatusCode(500, new { message = e.Message });
            }
        }

        /// <summary>
        /// Health check endpoint.
        /// </summary>
        /// <returns>The action result.</returns>
        [HttpGet]
        [Route("health")]
        public IActionResult HealthCheck()
        {
            return Ok(new
            {
                status = "healthy",
                timestamp = DateTime.UtcNow,
                service = "AccureMD MediaBot Worker",
                activeCalls = _botService.CallHandlers.Count
            });
        }

        /// <summary>
        /// Convert HttpRequest to HttpRequestMessage.
        /// </summary>
        /// <param name="request">The HTTP request.</param>
        /// <returns>The HTTP request message.</returns>
        private HttpRequestMessage ConvertHttpRequestToHttpRequestMessage(HttpRequest request)
        {
            var uri = new Uri(request.GetDisplayUrl());
            var requestMessage = new HttpRequestMessage();
            var requestMethod = request.Method;

            if (!HttpMethods.IsGet(requestMethod) &&
                !HttpMethods.IsHead(requestMethod) &&
                !HttpMethods.IsDelete(requestMethod) &&
                !HttpMethods.IsTrace(requestMethod))
            {
                var streamContent = new StreamContent(request.Body);
                requestMessage.Content = streamContent;
            }

            // Copy the request headers
            foreach (var header in request.Headers)
            {
                if (!requestMessage.Headers.TryAddWithoutValidation(header.Key, header.Value.ToArray()) && requestMessage.Content != null)
                {
                    requestMessage.Content?.Headers.TryAddWithoutValidation(header.Key, header.Value.ToArray());
                }
            }

            requestMessage.Headers.Host = uri.Authority;
            requestMessage.RequestUri = uri;
            requestMessage.Method = new HttpMethod(request.Method);

            return requestMessage;
        }
    }
}
