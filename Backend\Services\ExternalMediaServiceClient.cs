using System.Net.Http.Headers;
using System.Text;
using System.Text.Json;
using System.Text.RegularExpressions;
using Microsoft.Identity.Client;

namespace AccureMD.TeamsBot.Services;

public class ExternalMediaServiceClient
{
    private readonly IHttpClientFactory _httpClientFactory;
    private readonly IConfiguration _configuration;
    private readonly ILogger<ExternalMediaServiceClient> _logger;

    public ExternalMediaServiceClient(
        IHttpClientFactory httpClientFactory,
        IConfiguration configuration,
        ILogger<ExternalMediaServiceClient> logger)
    {
        _httpClientFactory = httpClientFactory;
        _configuration = configuration;
        _logger = logger;
    }

    public async Task<(bool Success, string? CallId, string Message)> JoinMeetingAsync(
        string meetingUrl,
        string? tenantId = null,
        string? displayName = null,
        string? userAccessToken = null)
    {
        try
        {
            var baseUrl = _configuration["MediaService:BaseUrl"];
            if (string.IsNullOrWhiteSpace(baseUrl))
            {
                return (false, null, "MediaService:BaseUrl is not configured");
            }

            var client = _httpClientFactory.CreateClient("MediaService");
            var url = baseUrl.TrimEnd('/') + "/api/calling/join";

            // Resolve effective tenantId from provided or configuration
            var effectiveTenantId = tenantId
                ?? _configuration["Teams:TenantId"]
                ?? _configuration["MicrosoftAppTenantId"];

            // Step 1: Extract joinMeetingId/passcode when using /meet links
            var joinMeetingId = ExtractJoinMeetingId(meetingUrl);
            var passcode = ExtractPasscode(meetingUrl);

            // Step 2: Resolve chatInfo + organizer from Graph when possible
            string? threadId = null;
            string? messageId = null;
            string? organizerUserId = null;
            string? organizerTenantId = null;

            if (!string.IsNullOrWhiteSpace(joinMeetingId))
            {
                var details = await ResolveMeetingDetailsFromGraphAsync(joinMeetingId!, userAccessToken);
                if (details != null)
                {
                    threadId = details.ThreadId;
                    messageId = details.MessageId ?? "0";
                    organizerUserId = details.OrganizerUserId;
                    organizerTenantId = details.OrganizerTenantId ?? effectiveTenantId;
                }
            }

            // Fallbacks
            threadId ??= ExtractThreadIdFromUrl(meetingUrl);
            messageId ??= "0";

            _logger.LogInformation("Join flow: resolvedThreadId={Resolved}, hasOrganizer={HasOrg}", string.IsNullOrWhiteSpace(threadId) ? "<null>" : threadId, !string.IsNullOrWhiteSpace(organizerUserId));

            // Build payload to Media Worker (include chatInfo + organizer if available)
            var payloadJson = BuildMediaWorkerJoinPayload(
                meetingUrl: meetingUrl,
                tenantId: effectiveTenantId,
                displayName: displayName,
                threadId: threadId,
                messageId: messageId,
                organizerUserId: organizerUserId,
                organizerTenantId: organizerTenantId,
                joinMeetingId: joinMeetingId,
                passcode: passcode);

            _logger.LogInformation("MediaWorker payload (sanitized): {Payload}", payloadJson);

            var request = new HttpRequestMessage(HttpMethod.Post, url)
            {
                Content = new StringContent(payloadJson, Encoding.UTF8, "application/json")
            };

            _logger.LogInformation("Calling Media Service join endpoint: {Url}. HasChatInfo={HasChatInfo}", url, threadId != null);
            var response = await client.SendAsync(request);
            var body = await response.Content.ReadAsStringAsync();

            if (!response.IsSuccessStatusCode)
            {
                _logger.LogWarning("Media Service join failed. Status={Status}, Body={Body}", (int)response.StatusCode, body);
                return (false, null, $"Media Service join failed: {(int)response.StatusCode} {response.ReasonPhrase} | {body}");
            }

            using var doc = JsonDocument.Parse(body);
            var root = doc.RootElement;
            var callId = root.TryGetProperty("callId", out var callIdEl) ? callIdEl.GetString() : null;
            var message = root.TryGetProperty("message", out var msgEl) ? msgEl.GetString() ?? "" : "";

            if (string.IsNullOrWhiteSpace(callId))
            {
                _logger.LogWarning("Media Service returned success HTTP but missing callId. Treating as failure. Body={Body}", body);
                return (false, null, string.IsNullOrWhiteSpace(message) ? "Media Service returned no callId" : message);
            }

            return (true, callId, message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error calling Media Service join endpoint");
            return (false, null, ex.Message);
        }
    }

    private static string? ExtractThreadIdFromUrl(string joinUrl)
    {
        try
        {
            // Grab the path segment after /l/meetup-join/ and before the next '/'
            var m = Regex.Match(joinUrl, @"/l/meetup-join/([^/?]+)", RegexOptions.IgnoreCase);
            if (m.Success)
            {
                var segmentEncoded = m.Groups[1].Value; // e.g., 19%3ameeting_...%40thread.v2
                var segment = Uri.UnescapeDataString(segmentEncoded); // e.g., 19:meeting_...@thread.v2
                if (segment.Contains("@thread.v2", StringComparison.OrdinalIgnoreCase))
                {
                    return segment;
                }
            }
            return null;
        }
        catch
        {
            return null;
        }
    }

    private static string? ExtractJoinMeetingId(string url)
    {
        try
        {
            // Matches https://teams.microsoft.com/meet/<id>?p=...
            var m = Regex.Match(url, @"/meet/([^/?#]+)", RegexOptions.IgnoreCase);
            if (m.Success)
            {
                return m.Groups[1].Value;
            }
            return null;
        }
        catch { return null; }
    }

    private static string? ExtractPasscode(string url)
    {
        try
        {
            var m = Regex.Match(url, @"[?&]p=([^&#]+)", RegexOptions.IgnoreCase);
            return m.Success ? Uri.UnescapeDataString(m.Groups[1].Value) : null;
        }
        catch { return null; }
    }

    private async Task<MeetingGraphDetails?> ResolveMeetingDetailsFromGraphAsync(string joinMeetingId, string? userAccessToken)
    {
        try
        {
            // Try delegated token first
            if (!string.IsNullOrWhiteSpace(userAccessToken))
            {
                var details = await QueryOnlineMeetingsAsync($"https://graph.microsoft.com/v1.0/me/onlineMeetings?$filter=joinMeetingIdSettings/joinMeetingId%20eq%20'{Uri.EscapeDataString(joinMeetingId)}'", userAccessToken!);
                if (details != null) return details;
            }

            // Fallback to application token with application access policy to a specific lookup user
            var lookupUserId = _configuration["Graph:MeetingLookupUserId"];
            if (!string.IsNullOrWhiteSpace(lookupUserId))
            {
                var appToken = await AcquireAppTokenAsync(_configuration["Teams:TenantId"] ?? _configuration["MicrosoftAppTenantId"]);
                if (!string.IsNullOrWhiteSpace(appToken))
                {
                    var url = $"https://graph.microsoft.com/v1.0/users/{lookupUserId}/onlineMeetings?$filter=joinMeetingIdSettings/joinMeetingId%20eq%20'{Uri.EscapeDataString(joinMeetingId)}'";
                    var details = await QueryOnlineMeetingsAsync(url, appToken);
                    if (details != null) return details;
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Failed to resolve meeting details from Graph using joinMeetingId {JoinMeetingId}", joinMeetingId);
        }
        return null;
    }

    private async Task<MeetingGraphDetails?> QueryOnlineMeetingsAsync(string url, string bearerToken)
    {
        var http = _httpClientFactory.CreateClient();
        var req = new HttpRequestMessage(HttpMethod.Get, url);
        req.Headers.Authorization = new AuthenticationHeaderValue("Bearer", bearerToken);

        var resp = await http.SendAsync(req);
        var body = await resp.Content.ReadAsStringAsync();
        if (!resp.IsSuccessStatusCode)
        {
            _logger.LogWarning("Graph onlineMeetings lookup failed. Status={Status}, Body={Body}", (int)resp.StatusCode, body);
            return null;
        }

        using var doc = JsonDocument.Parse(body);
        if (!doc.RootElement.TryGetProperty("value", out var valueArr) || valueArr.GetArrayLength() == 0)
            return null;
        var meeting = valueArr[0];

        // Prefer chatInfo.threadId/messageId directly
        string? threadId = meeting.TryGetProperty("chatInfo", out var chat) && chat.TryGetProperty("threadId", out var t) ? t.GetString() : null;
        string? messageId = meeting.TryGetProperty("chatInfo", out chat) && chat.TryGetProperty("messageId", out var mid) ? mid.GetString() : null;

        // Organizer id/tenant
        string? organizerUserId = null;
        string? organizerTenantId = null;
        try
        {
            var organizer = meeting.GetProperty("participants").GetProperty("organizer").GetProperty("identity").GetProperty("user");
            organizerUserId = organizer.TryGetProperty("id", out var oidEl) ? oidEl.GetString() : null;
            organizerTenantId = organizer.TryGetProperty("tenantId", out var tidEl) ? tidEl.GetString() : null;
        }
        catch { }

        // If chatInfo missing, decode from base64 meeting id to thread id
        if (string.IsNullOrWhiteSpace(threadId))
        {
            var idB64 = meeting.TryGetProperty("id", out var idEl) ? idEl.GetString() : null;
            if (!string.IsNullOrWhiteSpace(idB64))
            {
                try
                {
                    var decoded = Encoding.ASCII.GetString(Convert.FromBase64String(idB64));
                    var match = Regex.Match(decoded, @"^0#([^#]+)#0$");
                    if (match.Success) threadId = match.Groups[1].Value;
                }
                catch { }
            }
        }


        return new MeetingGraphDetails
        {
            ThreadId = threadId,
            MessageId = string.IsNullOrWhiteSpace(messageId) ? "0" : messageId,
            OrganizerUserId = organizerUserId,
            OrganizerTenantId = organizerTenantId
        };
    }

    private async Task<string?> AcquireAppTokenAsync(string? tenantId)
    {
        try
        {
            var appId = _configuration["Teams:AppId"] ?? _configuration["MicrosoftAppId"];
            var appSecret = _configuration["Teams:AppSecret"] ?? _configuration["MicrosoftAppPassword"];
            if (string.IsNullOrWhiteSpace(appId) || string.IsNullOrWhiteSpace(appSecret) || string.IsNullOrWhiteSpace(tenantId)) return null;

            var cca = ConfidentialClientApplicationBuilder
                .Create(appId)
                .WithClientSecret(appSecret)
                .WithAuthority($"https://login.microsoftonline.com/{tenantId}")
                .Build();

            var tr = await cca.AcquireTokenForClient(new[] { "https://graph.microsoft.com/.default" }).ExecuteAsync();
            return tr.AccessToken;
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Failed to acquire app token for tenant {TenantId}", tenantId);
            return null;
        }
    }

    private string BuildMediaWorkerJoinPayload(
        string meetingUrl,
        string? tenantId,
        string? displayName,
        string? threadId,
        string? messageId,
        string? organizerUserId,
        string? organizerTenantId,
        string? joinMeetingId,
        string? passcode)
    {
        // Build JSON payload for the new MediaBot implementation
        using var stream = new MemoryStream();
        using (var writer = new Utf8JsonWriter(stream))
        {
            writer.WriteStartObject();
            writer.WriteString("JoinURL", meetingUrl);
            writer.WriteString("DisplayName", string.IsNullOrWhiteSpace(displayName) ? "AccureMD Media Bot" : displayName);

            // The new MediaBot handles URL parsing internally, so we just send the basic info

            writer.WriteEndObject();
        }
        stream.Position = 0;
        return Encoding.UTF8.GetString(stream.ToArray());
    }

    private sealed class MeetingGraphDetails
    {
        public string? ThreadId { get; set; }
        public string? MessageId { get; set; }
        public string? OrganizerUserId { get; set; }
        public string? OrganizerTenantId { get; set; }
    }
}
