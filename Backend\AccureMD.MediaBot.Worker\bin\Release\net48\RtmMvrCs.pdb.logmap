043b7bb2:"[cid:%s] MMVRCustom: Unable to convert frame to correct size due to missing aspect ratio, orig=[%d,%d], enc=[%d,%d], throttle=%d","D:/a/_work/1/s/MSRTC/msrtc/src/mmvr/custom/MMVR_CustomRenderer.cpp";"MMVRCustomRenderer::InternalRenderOneFrame"
0998940f:"[cid:%s] Unexpected frame type provided","D:/a/_work/1/s/MSRTC/msrtc/src/mmvr/custom/MMVR_CustomRendererPassthrough.cpp";"MMVRCustomRendererPassthrough::RenderSample"
0a00ed8f:"[cid:%s] Ran out of samples in the pool","D:/a/_work/1/s/MSRTC/msrtc/src/mmvr/custom/MMVR_CustomRenderer.cpp";"MMVRCustomRenderer::ConvertFrame"
0e4f8f23:"[cid:%s] MMVRCustomRenderer: Destroying Instance","D:/a/_work/1/s/MSRTC/msrtc/src/mmvr/custom/MMVR_CustomRenderer.cpp";"MMVRCustomRenderer::~MMVRCustomRenderer"
1943ccb5:"[cid:%s] Binary attachment/frame data mismatch, attachmentData=%d, frameID=%d, timestamp:%lld","D:/a/_work/1/s/MSRTC/msrtc/src/mmvr/custom/MMVR_CustomRenderer.cpp";"MMVRCustomRenderer::VerifyHololensData::<lambda_...>::operator ()"
25d8a051:"[cid:%s] failed call to m_hVideoConverter->Convert, hr=0x%x","D:/a/_work/1/s/MSRTC/msrtc/src/mmvr/custom/MMVR_CustomRenderer.cpp";"MMVRCustomRenderer::ConvertFrame"
2ce44a27:"[cid:%s] RtcPalVideoExtension::ComputeSmartCropRectFromCropType failed (0x%x)","D:/a/_work/1/s/MSRTC/msrtc/src/mmvr/custom/MMVR_CustomRenderer.cpp";"MMVRCustomRenderer::InternalRenderOneFrame"
382ae502:"[cid:%s] MMVRCustomRenderer: Setting preferred format as [%d, %d], forceresise=%d, fcc=%x","D:/a/_work/1/s/MSRTC/msrtc/src/mmvr/custom/MMVR_CustomRenderer.cpp";"MMVRCustomRenderer::SetPreferredFormat"
42ca3e2d:"[cid:%s] Invalid hololens data in frame err=[%x].","D:/a/_work/1/s/MSRTC/msrtc/src/mmvr/custom/MMVR_CustomRenderer.cpp";"MMVRCustomRenderer::RenderFrame"
434f00f7:"[cid:%s] MMVRCustomRendererPassthrough: Destroying Instance","D:/a/_work/1/s/MSRTC/msrtc/src/mmvr/custom/MMVR_CustomRendererPassthrough.cpp";"MMVRCustomRendererPassthrough::~MMVRCustomRendererPassthrough"
448c4450:"[cid:%s] Null frame supplied","D:/a/_work/1/s/MSRTC/msrtc/src/mmvr/custom/MMVR_CustomRendererPassthrough.cpp";"MMVRCustomRendererPassthrough::RenderSample"
4b0c394a:"[cid:%s] MMVRCustomRenderer: To many color formats specified to creation=%d, max=%d","D:/a/_work/1/s/MSRTC/msrtc/src/mmvr/custom/MMVR_CustomRenderer.cpp";"MMVRCustomRenderer::CreateInstance"
597c1a2e:"[cid:%s] MMVRCustom: Unable to compute clipping rectangles, hr = %x","D:/a/_work/1/s/MSRTC/msrtc/src/mmvr/custom/MMVR_CustomRenderer.cpp";"MMVRCustomRenderer::InternalRenderOneFrame"
5bb5f7fd:"[cid:%s] MMVRCustomRendererPassthrough: Creating Instance","D:/a/_work/1/s/MSRTC/msrtc/src/mmvr/custom/MMVR_CustomRendererPassthrough.cpp";"MMVRCustomRendererPassthrough::MMVRCustomRendererPassthrough"
5c5b3a41:"[cid:%s] hr=0x%x","D:/a/_work/1/s/MSRTC/msrtc/src/mmvr/custom/MMVR_CustomRenderer.cpp";"MMVRCustomRenderer::SetImageProcessingOptions"
67ec5eca:"[cid:%s] MMVRCustomRenderer: Creating Instance","D:/a/_work/1/s/MSRTC/msrtc/src/mmvr/custom/MMVR_CustomRenderer.cpp";"MMVRCustomRenderer::MMVRCustomRenderer"
6d7be0cb:"[cid:%s] Unexpected pReserved supplied","D:/a/_work/1/s/MSRTC/msrtc/src/mmvr/custom/MMVR_CustomRendererPassthrough.cpp";"MMVRCustomRendererPassthrough::GetResource"
7b4e3c46:"[cid:%s] Stream info not provided","D:/a/_work/1/s/MSRTC/msrtc/src/mmvr/custom/MMVR_CustomRendererPassthrough.cpp";"MMVRCustomRendererPassthrough::GetResource"
7bfbf2d6:"[cid:%s] MMVRCustom: Unable to render frame, hr = %x","D:/a/_work/1/s/MSRTC/msrtc/src/mmvr/custom/MMVR_CustomRenderer.cpp";"MMVRCustomRenderer::InternalRenderOneFrame"
7c2d70a7:"[cid:%s] MMVRCustom: Unable to convert frame, hr = %x","D:/a/_work/1/s/MSRTC/msrtc/src/mmvr/custom/MMVR_CustomRenderer.cpp";"MMVRCustomRenderer::InternalRenderOneFrame"
a24caf9d:"[cid:%s] Frame size can\'t be at or below zero (size=%d)","D:/a/_work/1/s/MSRTC/msrtc/src/mmvr/custom/MMVR_CustomRenderer.cpp";"MMVRCustomRenderer::ConvertFrame"
a7e8d740:"[cid:%s] MMVRCustom: Unable to resize frame, hr = %x","D:/a/_work/1/s/MSRTC/msrtc/src/mmvr/custom/MMVR_CustomRenderer.cpp";"MMVRCustomRenderer::InternalRenderOneFrame"
b8e28eb7:"[cid:%s] MMVRCustom: Unable to allocate frame","D:/a/_work/1/s/MSRTC/msrtc/src/mmvr/custom/MMVR_CustomRenderer.cpp";"MMVRCustomRenderer::InternalRenderOneFrame"
bf1b7fea:"[cid:%s] MMVRCustom: ReleaseMMVR has been called","D:/a/_work/1/s/MSRTC/msrtc/src/mmvr/custom/MMVR_CustomRenderer.cpp";"MMVRCustomRenderer::RenderFrame"
ca418bfa:"[cid:%s] MMVRCustom: A frame was dropped because we\'re busy","D:/a/_work/1/s/MSRTC/msrtc/src/mmvr/custom/MMVR_CustomRenderer.cpp";"MMVRCustomRenderer::RenderFrame"
cdd7a126:"[cid:%s] Failed to allocate (%d bytes) output frame data","D:/a/_work/1/s/MSRTC/msrtc/src/mmvr/custom/MMVR_CustomRenderer.cpp";"MMVRCustomRenderer::ConvertFrame"
d0dad423:"[cid:%s] MMVRCustom: Adjusting preferred encoded frame dimensions to [%d,%d], forceResize=%d","D:/a/_work/1/s/MSRTC/msrtc/src/mmvr/custom/MMVR_CustomRenderer.cpp";"MMVRCustomRenderer::SetPreferredFormat"
d76ec93b:"[cid:%s] MMVRCustom: Frame arride prior to initialization completed","D:/a/_work/1/s/MSRTC/msrtc/src/mmvr/custom/MMVR_CustomRenderer.cpp";"MMVRCustomRenderer::InternalRenderOneFrame"
e052a7e6:"[cid:%s] MMVRCustom: Adjusting internal encoded frame dimensions to [%d,%d]","D:/a/_work/1/s/MSRTC/msrtc/src/mmvr/custom/MMVR_CustomRenderer.cpp";"MMVRCustomRenderer::InternalRenderOneFrame"
e758d5bd:"[cid:%s] MMVRCustomRenderer::RenderFrame Change in ORIGINAL dimensions to [%d, %d]","D:/a/_work/1/s/MSRTC/msrtc/src/mmvr/custom/MMVR_CustomRenderer.cpp";"MMVRCustomRenderer::InternalRenderOneFrame"
eecf17c7:"[cid:%s] Binary attachment/frame data, attachmentData=%d, frameID=%d, timestamp:%lld","D:/a/_work/1/s/MSRTC/msrtc/src/mmvr/custom/MMVR_CustomRenderer.cpp";"MMVRCustomRenderer::VerifyHololensData::<lambda_...>::operator ()"
f1c75b7a:"[cid:%s] MMVRCustom: Invalid NULL frame","D:/a/_work/1/s/MSRTC/msrtc/src/mmvr/custom/MMVR_CustomRenderer.cpp";"MMVRCustomRenderer::InternalRenderOneFrame"
f3b2f28b:"[cid:%s] Unsupported resource requested","D:/a/_work/1/s/MSRTC/msrtc/src/mmvr/custom/MMVR_CustomRendererPassthrough.cpp";"MMVRCustomRendererPassthrough::GetResource"
f419ec6e:"[cid:%s] frame received, ts:%llu, size:%d, wxh:%dx%d","D:/a/_work/1/s/MSRTC/msrtc/src/mmvr/custom/MMVR_CustomRendererPassthrough.cpp";"MMVRCustomRendererPassthrough::RenderSample"
f779432a:"[cid:%s] Invalid data in frame header, err=[%x].","D:/a/_work/1/s/MSRTC/msrtc/src/mmvr/custom/MMVR_CustomRenderer.cpp";"MMVRCustomRenderer::RenderFrame"
fc92b450:"[cid:%s] MMVRCustomRenderer: GetSupportedFormat called with zero-length output buffer","D:/a/_work/1/s/MSRTC/msrtc/src/mmvr/custom/MMVR_CustomRenderer.cpp";"MMVRCustomRenderer::GetSupportedFormats"
