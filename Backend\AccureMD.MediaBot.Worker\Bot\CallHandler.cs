using Microsoft.Graph.Communications.Calls;
using Microsoft.Graph.Communications.Calls.Media;
using Microsoft.Graph.Communications.Common.Telemetry;
using Microsoft.Graph.Communications.Resources;
using Microsoft.Graph;
using Microsoft.Psi;
using Microsoft.Psi.Data;
using Microsoft.Psi.TeamsBot;
using System;
using System.Collections.Generic;
using System.Timers;
using AccureMD.MediaBot.Worker.Models;
using AccureMD.MediaBot.Worker.TeamsBot;
using System.IO;
using System.Linq;

namespace AccureMD.MediaBot.Worker.Bot
{
    /// <summary>
    /// Call Handler Logic.
    /// </summary>
    public class CallHandler : IDisposable
    {
        /// <summary>
        /// Gets the call.
        /// </summary>
        public ICall Call { get; }

        /// <summary>
        /// Gets the bot media stream.
        /// </summary>
        public BotMediaStream BotMediaStream { get; private set; }

        /// <summary>
        /// MSI when there is no dominant speaker.
        /// </summary>
        public const uint DominantSpeakerNone = 4294967295; // DominantSpeakerChangedEventArgs.None

        /// <summary>
        /// The bot configuration.
        /// </summary>
        private readonly BotConfiguration botConfiguration;

        /// <summary>
        /// HashSet of the available sockets.
        /// </summary>
        private readonly HashSet<uint> availableSocketIds = new HashSet<uint>();

        /// <summary>
        /// The Psi pipeline.
        /// </summary>
        private readonly Pipeline pipeline;

        /// <summary>
        /// The Teams bot instance.
        /// </summary>
        private readonly ITeamsBot teamsBot;

        /// <summary>
        /// The heartbeat timer.
        /// </summary>
        private readonly Timer heartbeatTimer;

        /// <summary>
        /// Initializes a new instance of the <see cref="CallHandler"/> class.
        /// </summary>
        /// <param name="statefulCall">The stateful call.</param>
        /// <param name="botConfiguration">The bot configuration.</param>
        public CallHandler(ICall statefulCall, BotConfiguration botConfiguration)
        {
            this.botConfiguration = botConfiguration;

            this.pipeline = Pipeline.Create(enableDiagnostics: true);
            this.teamsBot = CreateTeamsBot(this.pipeline);

            PsiExporter exporter = null;
            if (!string.IsNullOrEmpty(botConfiguration.PsiStoreDirectory))
            {
                var storePath = Path.Combine(botConfiguration.PsiStoreDirectory, $"Call_{statefulCall.Id}_{DateTime.UtcNow:yyyyMMdd_HHmmss}");
                exporter = PsiStore.Create(this.pipeline, "MediaBot", storePath);
            }

            this.Call = statefulCall;
            this.Call.OnUpdated += this.CallOnUpdated;

            // Subscribe to the participants updates
            this.Call.Participants.OnUpdated += this.ParticipantsOnUpdated;

            // Subscribe to dominant speaker event on the audioSocket
            this.Call.GetLocalMediaSession().AudioSocket.DominantSpeakerChanged += this.OnDominantSpeakerChanged;

            foreach (var videoSocket in this.Call.GetLocalMediaSession().VideoSockets)
            {
                this.availableSocketIds.Add((uint)videoSocket.SocketId);
            }

            var waitingToShare = true;
            this.Call.OnUpdated += (call, args) =>
            {
                if (waitingToShare && call.Resource.State == CallState.Established && this.teamsBot.EnableScreenSharing)
                {
                    // Enable screen sharing
                    this.Call.ChangeScreenSharingRoleAsync(ScreenSharingRole.Sharer).Wait();
                    waitingToShare = false;
                }
            };

            // Attach the botMediaStream
            this.BotMediaStream = new BotMediaStream(
                this.Call.GetLocalMediaSession(),
                this,
                pipeline,
                teamsBot,
                exporter,
                this.Call.GraphLogger,
                this.botConfiguration);

            this.pipeline.PipelineExceptionNotHandled += (_, ex) =>
            {
                this.Call.GraphLogger.Error($"PSI PIPELINE ERROR: {ex.Exception.Message}");
            };

            // Setup heartbeat timer
            this.heartbeatTimer = new Timer(TimeSpan.FromMinutes(10).TotalMilliseconds);
            this.heartbeatTimer.Elapsed += this.HeartbeatAsync;
            this.heartbeatTimer.Start();

            this.pipeline.RunAsync();
        }

        /// <summary>
        /// Create your ITeamsBot component.
        /// </summary>
        /// <param name="pipeline">The pipeline.</param>
        /// <returns>ITeamsBot instance.</returns>
        private static ITeamsBot CreateTeamsBot(Pipeline pipeline)
        {
            return new AccureMDTeamsBot(pipeline);
        }

        /// <summary>
        /// Heartbeat handler.
        /// </summary>
        /// <param name="sender">The sender.</param>
        /// <param name="e">The elapsed event args.</param>
        private async void HeartbeatAsync(object sender, ElapsedEventArgs e)
        {
            try
            {
                await this.Call.KeepAliveAsync().ConfigureAwait(false);
            }
            catch (Exception ex)
            {
                this.Call.GraphLogger.Error(ex, "Error in heartbeat");
            }
        }

        /// <summary>
        /// Event fired when the call has been updated.
        /// </summary>
        /// <param name="sender">The call.</param>
        /// <param name="e">The event args containing call changes.</param>
        private void CallOnUpdated(ICall sender, ResourceEventArgs<Call> e)
        {
            this.Call.GraphLogger.Info($"Call status updated to {e.NewResource.State} - {e.NewResource.ResultInfo?.Message}");
        }

        /// <summary>
        /// Event fired when the participants collection has been updated.
        /// </summary>
        /// <param name="sender">Participants collection.</param>
        /// <param name="args">Event args containing added/removed participants.</param>
        private void ParticipantsOnUpdated(IParticipantCollection sender, CollectionEventArgs<IParticipant> args)
        {
            foreach (var participant in args.AddedResources)
            {
                this.Call.GraphLogger.Info($"Participant {participant.Id} joined the call");
                this.TrySubscribeToParticipant(participant);
            }

            foreach (var participant in args.RemovedResources)
            {
                this.Call.GraphLogger.Info($"Participant {participant.Id} left the call");
            }
        }

        /// <summary>
        /// Event fired when dominant speaker changes.
        /// </summary>
        /// <param name="sender">The audio socket.</param>
        /// <param name="e">The dominant speaker changed event args.</param>
        private void OnDominantSpeakerChanged(object sender, Microsoft.Skype.Bots.Media.DominantSpeakerChangedEventArgs e)
        {
            this.Call.GraphLogger.Info($"Dominant speaker changed to: {e.CurrentDominantSpeaker}");
        }

        /// <summary>
        /// Try to subscribe to a participant.
        /// </summary>
        /// <param name="participant">The participant.</param>
        private void TrySubscribeToParticipant(IParticipant participant)
        {
            if (participant.Resource.MediaStreams.Any())
            {
                var videoStream = participant.Resource.MediaStreams.FirstOrDefault(s => s.MediaType == Modality.Video);
                if (videoStream != null && this.availableSocketIds.Any())
                {
                    var socketId = this.availableSocketIds.First();
                    this.availableSocketIds.Remove(socketId);

                    this.BotMediaStream?.Subscribe(
                        Microsoft.Skype.Bots.Media.MediaType.Video,
                        uint.Parse(videoStream.SourceId),
                        Microsoft.Skype.Bots.Media.VideoResolution.HD720p,
                        socketId);
                }
            }
        }

        /// <summary>
        /// Dispose the call handler.
        /// </summary>
        public void Dispose()
        {
            this.heartbeatTimer?.Stop();
            this.heartbeatTimer?.Dispose();
            this.BotMediaStream?.Dispose();
            this.pipeline?.Dispose();
        }
    }
}
