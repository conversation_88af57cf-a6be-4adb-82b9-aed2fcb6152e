# AccureMD MediaBot Worker

A production-ready Microsoft Teams media bot implementation for audio/video processing and real-time transcription, built using the Microsoft Graph Communications API and Microsoft Psi framework.

## Overview

The AccureMD MediaBot Worker is a complete rewrite of the previous MediaBot implementation, based on the Microsoft PsiBot reference sample. It provides:

- **Real-time Audio Processing**: Captures and processes audio from Teams meetings
- **Video Stream Handling**: Receives and processes video streams from participants
- **Screen Sharing**: Generates and shares screen content during meetings
- **Transcription Ready**: Designed to integrate with speech recognition services
- **Scalable Architecture**: Built with Microsoft Psi for high-performance media processing
- **Production Hosting**: Configured for Windows VM deployment with proper SSL and service management

## Architecture

### Core Components

1. **BotService**: Main service that manages the Microsoft Graph Communications client
2. **CallHandler**: Handles individual call lifecycle and media session management
3. **BotMediaStream**: Processes incoming audio/video streams and manages outgoing media
4. **MediaFrameSourceComponent**: Converts raw media frames to Psi-compatible formats
5. **AccureMDTeamsBot**: Custom Teams bot implementation for audio/video processing
6. **AuthenticationProvider**: Handles Azure AD authentication for Graph API calls

### Technology Stack

- **.NET Framework 4.8**: Required for Microsoft Graph Communications SDK
- **Microsoft Graph Communications API**: For Teams call management
- **Microsoft Psi Framework**: For real-time media processing
- **ASP.NET Core**: For REST API endpoints
- **Microsoft Bot Media SDK**: For media stream handling

## Features

### Media Processing
- Multi-participant audio stream separation
- Real-time video frame processing
- Screen sharing with custom content generation
- NV12 to BGR video format conversion
- Audio format conversion (PCM 16kHz)

### Call Management
- Automatic call joining via Teams meeting URLs
- Call state monitoring and management
- Participant tracking and media subscription
- Graceful call termination

### API Endpoints
- `POST /api/calling/join` - Join a Teams meeting
- `GET /api/calling/calls` - List active calls
- `DELETE /api/calling/calls/{id}` - End a specific call
- `GET /api/calling/health` - Health check endpoint
- `POST /api/calling` - Handle incoming call notifications
- `POST /api/calling/notification` - Handle call state changes

## Quick Start

### Prerequisites
- Windows Server 2019+ or Windows 10/11 Pro
- .NET Framework 4.8
- SSL Certificate for your domain
- Azure App Registration with proper permissions

### Deployment

1. **Build the Application**
   ```bash
   dotnet publish -c Release -f net48 -o ./publish
   ```

2. **Deploy to Windows VM**
   ```powershell
   # Run as Administrator
   .\Deploy-MediaBot.ps1 -CertificateThumbprint "YOUR_CERT_THUMBPRINT"
   ```

3. **Verify Installation**
   ```powershell
   # Check service status
   Get-Service "AccureMD MediaBot"
   
   # Test health endpoint
   Invoke-WebRequest "https://your-domain:9441/api/calling/health"
   ```

### Configuration

Update `appsettings.json` with your Azure app registration details:

```json
{
  "BotConfiguration": {
    "AadAppId": "your-app-id",
    "AadAppSecret": "your-app-secret",
    "ServiceCname": "your-domain.com",
    "CertificateThumbprint": "your-cert-thumbprint",
    "CallSignalingPort": 9441,
    "InstanceInternalPort": 8445
  }
}
```

## Integration

### Backend Integration

The main backend application has been updated to work with the new MediaBot:

1. **ExternalMediaServiceClient**: Updated to send requests to `/api/calling/join`
2. **Payload Format**: Simplified to use `JoinURL` and `DisplayName` parameters
3. **Endpoint Configuration**: Points to `https://your-domain:9441`

### Example Integration

```csharp
// Backend sends join request to MediaBot
var payload = new {
    JoinURL = "https://teams.microsoft.com/l/meetup-join/...",
    DisplayName = "AccureMD Media Bot"
};

var response = await httpClient.PostAsJsonAsync(
    "https://accuremd.eastus.cloudapp.azure.com:9441/api/calling/join", 
    payload);
```

## Development

### Project Structure
```
AccureMD.MediaBot.Worker/
├── Authentication/          # Azure AD authentication
├── Bot/                    # Core bot implementation
├── Controllers/            # REST API controllers
├── Models/                 # Configuration and data models
├── TeamsBot/              # Custom Teams bot logic
├── appsettings.json       # Configuration
├── Program.cs             # Application entry point
└── Startup.cs             # Service configuration
```

### Key Classes

- **IBotService**: Main bot service interface
- **ITeamsBot**: Custom Teams bot interface for media processing
- **BotConfiguration**: Configuration model with Azure and media settings
- **CallHandler**: Manages individual call sessions
- **MediaFrameSourceComponent**: Converts media frames for Psi processing

### Extending the Bot

To add custom media processing:

1. Implement `ITeamsBot` interface
2. Process audio/video in the `ProcessAudio`/`ProcessVideo` methods
3. Generate output via `AudioOut`/`VideoOut`/`ScreenShareOut` emitters

## Monitoring

### Health Monitoring
- Service status via Windows Services
- Health endpoint: `/api/calling/health`
- Active calls endpoint: `/api/calling/calls`

### Logging
- Windows Event Viewer (Application log)
- Console output during development
- Psi store data in configured directory

### Performance Monitoring
- CPU and memory usage
- Network bandwidth utilization
- Psi store disk usage
- Call success/failure rates

## Troubleshooting

### Common Issues

1. **Certificate Errors**: Verify SSL certificate installation and thumbprint
2. **Port Binding**: Ensure ports 9441 and 8445 are available
3. **Authentication**: Check Azure app registration permissions
4. **Media Processing**: Verify Psi store directory permissions

### Debug Mode

For development, run with debug logging:

```json
{
  "Logging": {
    "LogLevel": {
      "Default": "Debug",
      "AccureMD.MediaBot": "Debug"
    }
  }
}
```

## Security

### Network Security
- HTTPS-only communication
- Firewall rules for specific ports
- Azure Network Security Group configuration

### Authentication
- Azure AD app-only authentication
- Certificate-based SSL/TLS
- Secure storage of app secrets

### Data Protection
- Encrypted media streams
- Secure Psi store directory
- No persistent storage of sensitive data

## Performance

### Optimization
- Efficient media frame processing
- Minimal memory allocation in hot paths
- Asynchronous I/O operations
- Proper resource disposal

### Scaling
- Single-instance design for media processing
- Horizontal scaling via multiple VMs
- Load balancing for high availability
- Resource monitoring and alerting

## Support

### Documentation
- [Setup Guide](SETUP_GUIDE.md) - Detailed installation instructions
- [Deployment Script](Deploy-MediaBot.ps1) - Automated deployment
- [Monitoring Script](Monitor-MediaBot.ps1) - Health monitoring

### Maintenance
- Regular certificate renewal
- Psi store cleanup
- Service health monitoring
- Performance optimization

## License

This project is part of the AccureMD Teams Bot solution and is proprietary software.

## Contributing

For development and contributions:
1. Follow the existing code structure
2. Add appropriate logging
3. Include error handling
4. Test with real Teams meetings
5. Update documentation

## Version History

- **v1.0.0**: Initial implementation based on PsiBot reference
- **v1.1.0**: Added production hosting and deployment automation
- **v1.2.0**: Enhanced error handling and monitoring capabilities

---

For detailed setup instructions, see [SETUP_GUIDE.md](SETUP_GUIDE.md).
For automated deployment, use [Deploy-MediaBot.ps1](Deploy-MediaBot.ps1).
