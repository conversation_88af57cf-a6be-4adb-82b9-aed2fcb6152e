{"format": 1, "restore": {"D:\\iData Project\\ASR_Bot_New\\Backend\\AccureMD.MediaBot.Worker\\AccureMD.MediaBot.Worker.csproj": {}}, "projects": {"D:\\iData Project\\ASR_Bot_New\\Backend\\AccureMD.MediaBot.Worker\\AccureMD.MediaBot.Worker.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\iData Project\\ASR_Bot_New\\Backend\\AccureMD.MediaBot.Worker\\AccureMD.MediaBot.Worker.csproj", "projectName": "AccureMD.MediaBot.Worker", "projectPath": "D:\\iData Project\\ASR_Bot_New\\Backend\\AccureMD.MediaBot.Worker\\AccureMD.MediaBot.Worker.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\iData Project\\ASR_Bot_New\\Backend\\AccureMD.MediaBot.Worker\\obj\\", "projectStyle": "PackageReference", "crossTargeting": true, "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net48"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net48": {"targetAlias": "net48", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net48": {"targetAlias": "net48", "dependencies": {"Microsoft.AspNetCore": {"target": "Package", "version": "[2.2.0, )"}, "Microsoft.AspNetCore.HttpsPolicy": {"target": "Package", "version": "[2.2.0, )"}, "Microsoft.AspNetCore.Mvc": {"target": "Package", "version": "[2.2.0, )"}, "Microsoft.Graph.Communications.Calls": {"target": "Package", "version": "[1.2.0.850, )"}, "Microsoft.Graph.Communications.Calls.Media": {"target": "Package", "version": "[1.2.0.850, )"}, "Microsoft.Graph.Communications.Client": {"target": "Package", "version": "[1.2.0.850, )"}, "Microsoft.Graph.Communications.Common": {"target": "Package", "version": "[1.2.0.850, )"}, "Microsoft.Graph.Communications.Core": {"target": "Package", "version": "[1.2.0.850, )"}, "Microsoft.IdentityModel.Clients.ActiveDirectory": {"target": "Package", "version": "[5.2.4, )"}, "Microsoft.IdentityModel.Protocols.OpenIdConnect": {"target": "Package", "version": "[8.0.1, )"}, "Microsoft.Psi.Audio": {"target": "Package", "version": "[0.19.100.1-beta, )"}, "Microsoft.Psi.Imaging": {"target": "Package", "version": "[0.18.72.1-beta, )"}, "Microsoft.Psi.Imaging.Windows": {"target": "Package", "version": "[0.15.49.1-beta, )"}, "Microsoft.Skype.Bots.Media": {"target": "Package", "version": "[1.19.0.25-alpha, )"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.302\\RuntimeIdentifierGraph.json"}}, "runtimes": {"win-x86": {"#import": []}}}}}