using Microsoft.Graph.Communications.Calls.Media;
using Microsoft.Graph.Communications.Common.Telemetry;
using Microsoft.Graph.Communications.Resources;
using Microsoft.Graph;
using Microsoft.Psi;
using Microsoft.Psi.Data;
using Microsoft.Psi.Imaging;
using Microsoft.Psi.TeamsBot;
using Microsoft.Psi.Components;
using Microsoft.Skype.Bots.Media;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.InteropServices;
using AccureMD.MediaBot.Worker.Models;
using AccureMD.MediaBot.Worker.TeamsBot;
using Microsoft.Psi.Audio;
using PsiImage = Microsoft.Psi.Imaging.Image;

namespace AccureMD.MediaBot.Worker.Bot
{
    /// <summary>
    /// Bot media stream implementation.
    /// </summary>
    public class BotMediaStream : IDisposable
    {
        /// <summary>
        /// Video format map for different resolutions.
        /// </summary>
        public static readonly Dictionary<(VideoColorFormat, int, int), VideoFormat> VideoFormatMap = new Dictionary<(VideoColorFormat, int, int), VideoFormat>
        {
            { (VideoColorFormat.NV12, 1920, 1080), VideoFormat.NV12_1920x1080_30Fps },
            { (VideoColorFormat.NV12, 1280, 720), VideoFormat.NV12_1280x720_30Fps },
            { (VideoColorFormat.NV12, 960, 540), VideoFormat.NV12_960x540_30Fps },
            { (VideoColorFormat.NV12, 640, 360), VideoFormat.NV12_640x360_30Fps },
            { (VideoColorFormat.NV12, 480, 270), VideoFormat.NV12_480x270_15Fps },
            { (VideoColorFormat.NV12, 424, 240), VideoFormat.NV12_424x240_15Fps },
            { (VideoColorFormat.NV12, 320, 180), VideoFormat.NV12_320x180_15Fps },
        };

        /// <summary>
        /// The participants.
        /// </summary>
        internal List<Microsoft.Graph.Communications.Calls.IParticipant> participants = new List<Microsoft.Graph.Communications.Calls.IParticipant>();

        private readonly IAudioSocket audioSocket;
        private readonly IVideoSocket vbssSocket;
        private readonly IVideoSocket mainVideoSocket;
        private readonly List<IVideoSocket> multiViewVideoSockets;
        private readonly ILocalMediaSession mediaSession;
        private readonly IGraphLogger logger;
        private readonly MediaFrameSourceComponent mediaFrameSourceComponent;
        private int shutdown;
        private MediaSendStatus videoMediaSendStatus = MediaSendStatus.Inactive;
        private MediaSendStatus vbssMediaSendStatus = MediaSendStatus.Inactive;
        private MediaSendStatus audioSendStatus = MediaSendStatus.Inactive;

        /// <summary>
        /// Initializes a new instance of the <see cref="BotMediaStream"/> class.
        /// </summary>
        /// <param name="mediaSession">The media session.</param>
        /// <param name="callHandler">Call handler.</param>
        /// <param name="pipeline">Psi Pipeline.</param>
        /// <param name="teamsBot">Teams bot instance.</param>
        /// <param name="exporter">Psi Exporter.</param>
        /// <param name="logger">Graph logger.</param>
        /// <param name="botConfiguration">Bot configuration.</param>
        public BotMediaStream(
            ILocalMediaSession mediaSession,
            CallHandler callHandler,
            Pipeline pipeline,
            ITeamsBot teamsBot,
            Exporter exporter,
            IGraphLogger logger,
            BotConfiguration botConfiguration)
        {
            this.mediaSession = mediaSession;
            this.logger = logger;
            this.mediaFrameSourceComponent = new MediaFrameSourceComponent(pipeline, callHandler, logger);

            if (exporter != null)
            {
                this.mediaFrameSourceComponent.Audio.Parallel(
                    (id, stream) =>
                    {
                        // Extract and persist audio streams with the original timestamps for each buffer
                        stream.Process<(AudioBuffer, DateTime), AudioBuffer>((tuple, _, emitter) =>
                        {
                            (var audioBuffer, var originatingTime) = tuple;
                            if (originatingTime > emitter.LastEnvelope.OriginatingTime)
                            {
                                // Out-of-order messages are ignored
                                emitter.Post(audioBuffer, originatingTime);
                            }
                        }).Write($"Participants.{id}.Audio", exporter);
                    },
                    branchTerminationPolicy: BranchTerminationPolicy<string, (AudioBuffer, DateTime)>.Never(),
                    name: "PersistParticipantAudio");

                this.mediaFrameSourceComponent.Video.Parallel(
                    (id, stream) =>
                    {
                        // Extract and persist video streams with the original timestamps for each image
                        stream.Process<(Shared<PsiImage>, DateTime), Shared<PsiImage>>(
                            (tuple, _, emitter) =>
                            {
                                (var image, var originatingTime) = tuple;
                                if (originatingTime > emitter.LastEnvelope.OriginatingTime)
                                {
                                    // Out-of-order messages are ignored
                                    emitter.Post(image, originatingTime);
                                }
                            },
                            DeliveryPolicy.LatestMessage).EncodeJpeg(90, DeliveryPolicy.LatestMessage).Write($"Participants.{id}.Video", exporter, true);
                    },
                    DeliveryPolicy.LatestMessage,
                    BranchTerminationPolicy<string, (Shared<PsiImage>, DateTime)>.Never(),
                    name: "PersistParticipantVideo");

                teamsBot.ScreenShareOut?.EncodeJpeg(90, DeliveryPolicy.LatestMessage).Write("Bot.Screen", exporter, true);
                teamsBot.AudioOut?.Write("Bot.Audio", exporter);
                teamsBot.VideoOut?.EncodeJpeg(90, DeliveryPolicy.LatestMessage).Write("Bot.Video", exporter, true);
            }

            this.mediaFrameSourceComponent.Audio.PipeTo(teamsBot.AudioIn);
            this.mediaFrameSourceComponent.Video.PipeTo(teamsBot.VideoIn);

            teamsBot.AudioOut?.Do(buffer =>
            {
                if (this.audioSendStatus == MediaSendStatus.Active && teamsBot.EnableAudioOutput)
                {
                    IntPtr unmanagedPointer = Marshal.AllocHGlobal(buffer.Length);
                    Marshal.Copy(buffer.Data, 0, unmanagedPointer, buffer.Length);
                    this.SendAudio(new AudioSendBuffer(unmanagedPointer, buffer.Length, AudioFormat.Pcm16K));
                    Marshal.FreeHGlobal(unmanagedPointer);
                }
            });

            teamsBot.VideoOut?.Do(
                frame =>
                {
                    if (this.videoMediaSendStatus == MediaSendStatus.Active && teamsBot.EnableVideoOutput)
                    {
                        var image = frame.Resource;
                        var imageDataSize = image.Width * image.Height * 4; // BGRA = 4 bytes per pixel
                        var imageDataBytes = new byte[imageDataSize];
                        Marshal.Copy(image.ImageData, imageDataBytes, 0, imageDataSize);
                        var nv12 = this.BGRAtoNV12(imageDataBytes, image.Width, image.Height);
                        var format = VideoFormatMap[(VideoColorFormat.NV12, teamsBot.VideoSize.Width, teamsBot.VideoSize.Height)];
                        this.SendVideo(new VideoSendBuffer(nv12, (uint)nv12.Length, format));
                    }
                },
                DeliveryPolicy.LatestMessage);

            teamsBot.ScreenShareOut?.Do(
                frame =>
                {
                    if (this.vbssMediaSendStatus == MediaSendStatus.Active && teamsBot.EnableScreenSharing)
                    {
                        var image = frame.Resource;
                        var imageDataSize = image.Width * image.Height * 4; // BGRA = 4 bytes per pixel
                        var imageDataBytes = new byte[imageDataSize];
                        Marshal.Copy(image.ImageData, imageDataBytes, 0, imageDataSize);
                        var nv12 = this.BGRAtoNV12(imageDataBytes, image.Width, image.Height);
                        var format = VideoFormatMap[(VideoColorFormat.NV12, teamsBot.ScreenShareSize.Width, teamsBot.ScreenShareSize.Height)];
                        this.SendScreen(new VideoSendBuffer(nv12, (uint)nv12.Length, format));
                    }
                },
                DeliveryPolicy.LatestMessage);

            // Subscribe to the audio media.
            this.audioSocket = this.mediaSession.AudioSocket;
            if (this.audioSocket == null)
            {
                throw new InvalidOperationException("A mediaSession needs to have at least an audioSocket");
            }

            this.audioSocket.AudioMediaReceived += this.OnAudioMediaReceived;

            // Subscribe to the video media.
            this.mainVideoSocket = this.mediaSession.VideoSockets?.FirstOrDefault();
            if (this.mainVideoSocket != null)
            {
                this.mainVideoSocket.VideoMediaReceived += this.OnVideoMediaReceived;
            }

            // Subscribe to the VBSS media.
            this.vbssSocket = this.mediaSession.VbssSocket;
            if (this.vbssSocket != null)
            {
                this.vbssSocket.VideoMediaReceived += this.OnVideoMediaReceived;
            }

            // Iterate over the video sockets and subscribe to the video media.
            this.multiViewVideoSockets = this.mediaSession.VideoSockets?.ToList();
            if (this.multiViewVideoSockets?.Count > 0)
            {
                foreach (var videoSocket in this.multiViewVideoSockets)
                {
                    videoSocket.VideoMediaReceived += this.OnVideoMediaReceived;
                }
            }

            this.StartAudioVideoFrameProcessing();
        }

        /// <summary>
        /// Subscription for video and vbss.
        /// </summary>
        /// <param name="mediaType">vbss or video.</param>
        /// <param name="mediaSourceId">The video source Id.</param>
        /// <param name="videoResolution">The preferred video resolution.</param>
        /// <param name="socketId">Socket id requesting the video. For vbss it is always 0.</param>
        public void Subscribe(MediaType mediaType, uint mediaSourceId, VideoResolution videoResolution, uint socketId = 0)
        {
            try
            {
                this.ValidateSubscriptionMediaType(mediaType);
                this.logger.Info($"Subscribing to the video source: {mediaSourceId} on socket: {socketId} with the preferred resolution: {videoResolution} and mediaType: {mediaType}");
                if (mediaType == MediaType.Vbss)
                {
                    if (this.vbssSocket == null)
                    {
                        this.logger.Warn($"vbss socket not initialized");
                    }
                    else
                    {
                        this.vbssSocket.Subscribe(videoResolution, mediaSourceId);
                    }
                }
                else if (mediaType == MediaType.Video)
                {
                    if (this.multiViewVideoSockets == null)
                    {
                        this.logger.Warn($"video sockets were not created");
                    }
                    else
                    {
                        this.multiViewVideoSockets[(int)socketId].Subscribe(videoResolution, mediaSourceId);
                    }
                }
            }
            catch (Exception ex)
            {
                this.logger.Error(ex, $"Video Subscription failed for the socket: {socketId} and MediaSourceId: {mediaSourceId} with exception");
            }
        }

        /// <summary>
        /// Validate subscription media type.
        /// </summary>
        /// <param name="mediaType">The media type.</param>
        private void ValidateSubscriptionMediaType(MediaType mediaType)
        {
            if (mediaType != MediaType.Video && mediaType != MediaType.Vbss)
            {
                throw new ArgumentOutOfRangeException($"Invalid mediaType: {mediaType}");
            }
        }

        /// <summary>
        /// Start audio and video frame processing.
        /// </summary>
        private void StartAudioVideoFrameProcessing()
        {
            this.audioSendStatus = MediaSendStatus.Active;
            this.videoMediaSendStatus = MediaSendStatus.Active;
            this.vbssMediaSendStatus = MediaSendStatus.Active;
        }

        /// <summary>
        /// Receive audio from subscribed participant.
        /// </summary>
        /// <param name="sender">The sender.</param>
        /// <param name="e">The audio media received arguments.</param>
        private void OnAudioMediaReceived(object sender, AudioMediaReceivedEventArgs e)
        {
            this.logger.Info($"[AudioMediaReceivedEventArgs(Data=<{e.Buffer.Data.ToString()}>, Length={e.Buffer.Length}, Timestamp={e.Buffer.Timestamp}, Format={e.Buffer.AudioFormat})]");
            this.mediaFrameSourceComponent.Received(e.Buffer);
            e.Buffer.Dispose();
        }

        /// <summary>
        /// Receive video from subscribed participant.
        /// </summary>
        /// <param name="sender">The sender.</param>
        /// <param name="e">The video media received arguments.</param>
        private void OnVideoMediaReceived(object sender, VideoMediaReceivedEventArgs e)
        {
            this.logger.Info($"[VideoMediaReceivedEventArgs(Data=<{e.Buffer.Data.ToString()}>, Length={e.Buffer.Length}, Timestamp={e.Buffer.Timestamp}, Width={e.Buffer.VideoFormat.Width}, Height={e.Buffer.VideoFormat.Height}, ColorFormat={e.Buffer.VideoFormat.VideoColorFormat}, FrameRate={e.Buffer.VideoFormat.FrameRate} MediaSourceId={e.Buffer.MediaSourceId})]");
            this.mediaFrameSourceComponent.Received(e.Buffer, e.Buffer.MediaSourceId);
            e.Buffer.Dispose();
        }

        /// <summary>
        /// Sends audio to the call.
        /// </summary>
        /// <param name="buffer">The audio buffer to send.</param>
        private void SendAudio(AudioSendBuffer buffer)
        {
            try
            {
                this.audioSocket.Send(buffer);
            }
            catch (Exception ex)
            {
                this.logger.Error(ex, $"[SendAudio] Exception while calling audioSocket.Send()");
            }
        }

        /// <summary>
        /// Sends video to the call.
        /// </summary>
        /// <param name="buffer">The video buffer to send.</param>
        private void SendVideo(VideoSendBuffer buffer)
        {
            try
            {
                this.mainVideoSocket?.Send(buffer);
            }
            catch (Exception ex)
            {
                this.logger.Error(ex, $"[SendVideo] Exception while calling videoSocket.Send()");
            }
        }

        /// <summary>
        /// Sends a video buffer as a shared screen frame to the call.
        /// </summary>
        /// <param name="buffer">The video buffer to send.</param>
        private void SendScreen(VideoSendBuffer buffer)
        {
            try
            {
                this.vbssSocket?.Send(buffer);
            }
            catch (Exception ex)
            {
                this.logger.Error(ex, $"[SendScreen] Exception while calling vbssSocket.Send()");
            }
        }

        /// <summary>
        /// Convert BGRA to NV12 format.
        /// </summary>
        /// <param name="bgraData">The BGRA data.</param>
        /// <param name="width">The width.</param>
        /// <param name="height">The height.</param>
        /// <returns>NV12 data.</returns>
        private byte[] BGRAtoNV12(byte[] bgraData, int width, int height)
        {
            byte[] nv12Data = new byte[width * height * 3 / 2];

            // Convert BGRA to YUV420 (NV12)
            for (int y = 0; y < height; y++)
            {
                for (int x = 0; x < width; x++)
                {
                    int bgraIndex = (y * width + x) * 4;
                    byte b = bgraData[bgraIndex];
                    byte g = bgraData[bgraIndex + 1];
                    byte r = bgraData[bgraIndex + 2];

                    // Convert RGB to Y (luminance)
                    byte yValue = (byte)((0.299 * r) + (0.587 * g) + (0.114 * b));
                    nv12Data[y * width + x] = yValue;

                    // Convert RGB to U and V (chrominance) for every 2x2 block
                    if (y % 2 == 0 && x % 2 == 0)
                    {
                        byte uValue = (byte)(128 + ((-0.169 * r) + (-0.331 * g) + (0.5 * b)));
                        byte vValue = (byte)(128 + ((0.5 * r) + (-0.419 * g) + (-0.081 * b)));

                        int uvIndex = width * height + (y / 2) * width + x;
                        nv12Data[uvIndex] = uValue;
                        nv12Data[uvIndex + 1] = vValue;
                    }
                }
            }

            return nv12Data;
        }

        /// <summary>
        /// Dispose the bot media stream.
        /// </summary>
        public void Dispose()
        {
            if (System.Threading.Interlocked.CompareExchange(ref this.shutdown, 1, 0) == 1)
            {
                return;
            }

            // Unsubscribe from events
            if (this.audioSocket != null)
            {
                this.audioSocket.AudioMediaReceived -= this.OnAudioMediaReceived;
            }

            if (this.mainVideoSocket != null)
            {
                this.mainVideoSocket.VideoMediaReceived -= this.OnVideoMediaReceived;
            }

            if (this.vbssSocket != null)
            {
                this.vbssSocket.VideoMediaReceived -= this.OnVideoMediaReceived;
            }

            if (this.multiViewVideoSockets != null)
            {
                foreach (var videoSocket in this.multiViewVideoSockets)
                {
                    videoSocket.VideoMediaReceived -= this.OnVideoMediaReceived;
                }
            }
        }
    }
}
