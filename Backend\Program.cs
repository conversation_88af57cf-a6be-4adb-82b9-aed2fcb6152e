using Microsoft.AspNetCore.Hosting;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.Bot.Builder;
using Microsoft.Bot.Builder.Integration.AspNet.Core;
using Microsoft.Bot.Builder.Teams;
using Microsoft.Bot.Connector.Authentication; // Added for BotFrameworkAuthentication registration
using Microsoft.Bot.Schema;
using Microsoft.EntityFrameworkCore;
using AccureMD.TeamsBot.Services;
using AccureMD.TeamsBot.Data;

// Enable legacy timestamp behavior for Npgsql to allow 'Unspecified' DateTime kinds to be written.
AppContext.SetSwitch("Npgsql.EnableLegacyTimestampBehavior", true);

var builder = WebApplication.CreateBuilder(args);

// Add services to the container
builder.Services.AddControllers();
builder.Services.AddHttpClient();

// Register BotFrameworkAuthentication implementation (fix DI error)
builder.Services.AddSingleton<BotFrameworkAuthentication, ConfigurationBotFrameworkAuthentication>();

// Named client for Media Service
var allowSelfSigned = builder.Configuration.GetValue<bool>("MediaService:AllowSelfSigned", false);
builder.Services.AddHttpClient("MediaService")
    .ConfigurePrimaryHttpMessageHandler(() =>
    {
        if (allowSelfSigned)
        {
            var handler = new HttpClientHandler();
            handler.ServerCertificateCustomValidationCallback = (request, cert, chain, errors) => true; // WARNING: for non-prod use only
            return handler;
        }
        return new HttpClientHandler();
    });

// Add CORS for Teams authentication
builder.Services.AddCors(options =>
{
    options.AddPolicy("TeamsPolicy", policy =>
    {
        policy.WithOrigins(
            "https://teams.microsoft.com",
            "https://teams.microsoft.us",
            "https://gov.teams.microsoft.us",
            "https://dod.teams.microsoft.us",
            "https://int.teams.microsoft.com",
            "https://devspaces.skype.com",
            "https://ssauth.skype.com",
            "https://local.teams.office.com",
            "https://accuremd.azurewebsites.net",
            "https://login.microsoftonline.com"
        )
        .AllowAnyMethod()
        .AllowAnyHeader()
        .AllowCredentials();
    });
});

// Register the DbContext for PostgreSQL database
builder.Services.AddDbContext<ApplicationDbContext>(options =>
    options.UseNpgsql(builder.Configuration.GetConnectionString("DefaultConnection")));

// Configure Bot Framework
builder.Services.AddSingleton<IBotFrameworkHttpAdapter, AdapterWithErrorHandler>();
builder.Services.AddTransient<IBot, AccureMDBotService>();

// Add custom services (scoped for database operations)
builder.Services.AddScoped<AuthenticationService>();
// builder.Services.AddScoped<GraphCommunicationsService>(); // Keep registered only if needed for non-media features
builder.Services.AddScoped<ExternalMediaServiceClient>();
builder.Services.AddScoped<MeetingService>();
builder.Services.AddScoped<RecordingService>();
builder.Services.AddScoped<TranscriptionService>();
builder.Services.AddScoped<StorageService>();
builder.Services.AddScoped<DatabaseInitializationService>();

// Call state tracking for Graph callbacks
builder.Services.AddSingleton<AccureMD.TeamsBot.Services.CallStateService>();

// Add memory storage and conversation state
builder.Services.AddSingleton<IStorage, MemoryStorage>();
builder.Services.AddSingleton<ConversationState>();
builder.Services.AddSingleton<UserState>();

// Configure authentication
builder.Services.AddAuthentication();

var app = builder.Build();

// Initialize database
using (var scope = app.Services.CreateScope())
{
    var dbInitService = scope.ServiceProvider.GetRequiredService<DatabaseInitializationService>();
    await dbInitService.InitializeAsync();
}

// Configure the HTTP request pipeline
if (app.Environment.IsDevelopment())
{
    app.UseDeveloperExceptionPage();
}

var defaultFilesOptions = new DefaultFilesOptions();
defaultFilesOptions.DefaultFileNames.Clear();
defaultFilesOptions.DefaultFileNames.Add("html/index.html");

app.UseHttpsRedirection();

// Enable CORS for Teams authentication
app.UseCors("TeamsPolicy");

// Enable WebSockets for media ingest
app.UseWebSockets(new WebSocketOptions
{
    KeepAliveInterval = TimeSpan.FromSeconds(30),
    ReceiveBufferSize = 256 * 1024
});

app.UseDefaultFiles(defaultFilesOptions); // Serve html/index.html as the default
app.UseStaticFiles();

app.UseRouting();
app.UseAuthentication();
app.UseAuthorization();

app.MapControllers();

app.Run();

/// <summary>
/// Error handler for Bot Framework
/// </summary>
public class AdapterWithErrorHandler : CloudAdapter
{
    public AdapterWithErrorHandler(BotFrameworkAuthentication auth, ILogger<AdapterWithErrorHandler> logger)
        : base(auth, logger)
    {
        OnTurnError = async (turnContext, exception) =>
        {
            logger.LogError(exception, "Exception caught : {ExceptionMessage}", exception.Message);

            // Send a message to the user
            await turnContext.SendActivityAsync("The bot encountered an error or bug.");
            await turnContext.SendActivityAsync("To continue to run this bot, please fix the bot source code.");

            // Send a trace activity, which will be displayed in the Bot Framework Emulator
            var traceActivity = MessageFactory.Text($"OnTurnError Trace: {exception.Message}");
            traceActivity.Type = ActivityTypes.Trace;
            traceActivity.Name = "TurnError";
            await turnContext.SendActivityAsync(traceActivity);
        };
    }
}