
//
// This file contains generated symbol names used by lodctr.exe.
//
#pragma once

#define PERF_OPERATIONS 0
#define PERF_OPERATIONS_GLOBAL_HEALTH 2
#define PERF_OPERATIONS_TCPPACKETIZER_OUT_OF_SYNC 4
#define PERF_OPERATIONS_FAILED_ALLOCATIONS 6
#define PERF_OPERATIONS_NUM_PACKETS_DROPPED_BY_SRTP 8

#define PERF_PLANNING 10
#define PERF_PLANNING_NUM_CONF_STARTED 12
#define PERF_PLANNING_NUM_AUDCHANNEL_STARTED 14
#define PERF_PLANNING_NUM_VIDCHANNEL_STARTED 16
#define PERF_PLANNING_NUM_DATCHANNEL_STARTED 18
#define PERF_PLANNING_NUM_PARTICIPANT_STARTED 20
#define PERF_PLANNING_NUM_PARENT_PARTICIPANT_STARTED 22
#define PERF_PLANNING_NUM_PARENT_AUDCHANNEL_STARTED 24
#define PERF_PLANNING_NUM_PARENT_VIDCHANNEL_STARTED 26
#define PERF_PLANNING_NUM_PARENT_DATCHANNEL_STARTED 28
#define PERF_PLANNING_NUM_CHILD_PARTICIPANT_STARTED 30
#define PERF_PLANNING_NUM_CHILD_AUDCHANNEL_STARTED 32
#define PERF_PLANNING_NUM_CHILD_VIDCHANNEL_STARTED 34
#define PERF_PLANNING_NUM_CHILD_DATCHANNEL_STARTED 36
#define PERF_PLANNING_NUM_CONFS_HEALTH_NORMAL 38
#define PERF_PLANNING_NUM_CONFS_HEALTH_OVERLOADED 40
#define PERF_PLANNING_NUM_PACKETS_DROPPED 42
#define PERF_PLANNING_FAILED_CONNECTIVITY_CHECKS 44
#define PERF_PLANNING_NUM_DATA_CONNECTION_DROPPED 46
#define PERF_PLANNING_NUM_CONFERENCE_PROCESSING_DELAYS 48

#define PERF_INFORMATIONAL 50
#define PERF_INFORMATIONAL_NUM_SEND_AUDCHANNEL_STARTED 52
#define PERF_INFORMATIONAL_NUM_RECV_AUDCHANNEL_STARTED 54
#define PERF_INFORMATIONAL_NUM_BOTH_AUDCHANNEL_STARTED 56
#define PERF_INFORMATIONAL_NUM_SEND_VIDCHANNEL_STARTED 58
#define PERF_INFORMATIONAL_NUM_RECV_VIDCHANNEL_STARTED 60
#define PERF_INFORMATIONAL_NUM_BOTH_VIDCHANNEL_STARTED 62
#define PERF_INFORMATIONAL_AUDIOROUTER_TIMESLICE_TIME 64
#define PERF_INFORMATIONAL_AUDIOROUTER_TIMESLICE_TIME_BASE 66
#define PERF_INFORMATIONAL_STREAMENG_TIMERTICKS 68
#define PERF_INFORMATIONAL_CONF_PROCESSING_RATE 70
#define PERF_INFORMATIONAL_AVERAGE_ICE_ADDRESS_BINDING_TIME 72
#define PERF_INFORMATIONAL_AVERAGE_ICE_ADDRESS_BINDING_TIME_BASE 74
#define PERF_INFORMATIONAL_AVERAGE_ICE_CONNECTIVITY_CHECK_TIME 76
#define PERF_INFORMATIONAL_AVERAGE_ICE_CONNECTIVITY_CHECK_TIME_BASE 78
#define PERF_INFORMATIONAL_RECV_AUDIOPACKETS_FROM_TRANSPORT 80
#define PERF_INFORMATIONAL_RECV_AUDIOPACKETS_INTO_ENGINE 82
#define PERF_INFORMATIONAL_RECV_AUDIOPACKETS_FROM_ENGINE 84
#define PERF_INFORMATIONAL_SEND_AUDIOPACKETS_INTO_ENGINE 86
#define PERF_INFORMATIONAL_AUDIOROUTER_INPUT_BUFFERS 88
#define PERF_INFORMATIONAL_AUDIOROUTER_OUTPUT_BUFFERS 90
#define PERF_INFORMATIONAL_SEND_AUDIOPACKETS_INTO_RTP 92
#define PERF_INFORMATIONAL_SEND_AUDIOPACKETS_INTO_TRANSPORT 94
#define PERF_INFORMATIONAL_AUDIO_FEC_PACKETS 96
#define PERF_INFORMATIONAL_RECV_VIDEOPACKETS_FROM_TRANSPORT 98
#define PERF_INFORMATIONAL_RECV_VIDEOPACKETS_INTO_ENGINE 100
#define PERF_INFORMATIONAL_RECV_VIDEOPACKETS_FROM_ENGINE 102
#define PERF_INFORMATIONAL_SEND_VIDEOPACKETS_FROM_ENGINE 104
#define PERF_INFORMATIONAL_SEND_VIDEOPACKETS_INTO_RTP 106
#define PERF_INFORMATIONAL_SEND_VIDEOPACKETS_INTO_TRANSPORT 108
#define PERF_INFORMATIONAL_VIDSWITCHER_INPUTFRAMES 110
#define PERF_INFORMATIONAL_VIDSWITCHER_OUTPUTFRAMES 112
#define PERF_INFORMATIONAL_RECV_DATAPACKETS_FROM_TRANSPORT 114
#define PERF_INFORMATIONAL_SEND_DATAPACKETS_TO_TRANSPORT 116
#define PERF_INFORMATIONAL_NUM_DATA_CHANNEL_STALLED 118
#define PERF_INFORMATIONAL_NUM_DATA_CHANNEL_TRANSPORT_STALLED 120
#define PERF_INFORMATIONAL_PACKETS_FROM_TCPPACKETIZER 122
#define PERF_INFORMATIONAL_RECEIVE_IO_SUCCEEDED 124
#define PERF_INFORMATIONAL_SEND_IO_SUCCEEDED 126
#define PERF_INFORMATIONAL_RECEIVE_IO_FAILED 128
#define PERF_INFORMATIONAL_SEND_IO_FAILED 130
#define PERF_INFORMATIONAL_RTCPPACKETS_RECEIVED 132
#define PERF_INFORMATIONAL_RTCPPACKETS_SENT 134
#define PERF_INFORMATIONAL_TOTAL_NUM_MEMORYPOOL_ALLOC 136
#define PERF_INFORMATIONAL_TOTAL_NUM_MEMORYPOOL_FREE 138
#define PERF_INFORMATIONAL_TOTAL_NUM_CBufferTransportIOContext 140
#define PERF_INFORMATIONAL_TOTAL_NUM_CBufferStream 142
#define PERF_INFORMATIONAL_TOTAL_NUM_ALLOC_PORT_COLLISION 144
#define PERF_INFORMATIONAL_TOTAL_NUM_MEDIA_TIMEOUTS 146
#define PERF_INFORMATIONAL_VIDSWITCHER_RATEMATCHEDFRAMES 148
#define PERF_INFORMATIONAL_NUM_VIDEO_IN_QVGA 150
#define PERF_INFORMATIONAL_NUM_VIDEO_IN_VGA 152
#define PERF_INFORMATIONAL_NUM_VIDEO_IN_HD 154
#define PERF_INFORMATIONAL_NUM_VIDEO_IN_PANO 156
#define PERF_INFORMATIONAL_NUM_VIDEO_OUT_QVGA 158
#define PERF_INFORMATIONAL_NUM_VIDEO_OUT_VGA 160
#define PERF_INFORMATIONAL_NUM_VIDEO_OUT_HD 162
#define PERF_INFORMATIONAL_NUM_VIDEO_OUT_PANO 164
#define PERF_INFORMATIONAL_NUM_VIDEO_IN_VC1 166
#define PERF_INFORMATIONAL_NUM_VIDEO_OUT_VC1 168
#define PERF_INFORMATIONAL_AUDIO_IN_BANDWIDTH 170
#define PERF_INFORMATIONAL_AUDIO_OUT_BANDWIDTH 172
#define PERF_INFORMATIONAL_VIDEO_IN_BANDWIDTH 174
#define PERF_INFORMATIONAL_VIDEO_OUT_BANDWIDTH 176
#define PERF_INFORMATIONAL_DATA_IN_BANDWIDTH 178
#define PERF_INFORMATIONAL_DATA_OUT_BANDWIDTH 180
#define PERF_INFORMATIONAL_PARENT_AUDIO_IN_BANDWIDTH 182
#define PERF_INFORMATIONAL_PARENT_AUDIO_OUT_BANDWIDTH 184
#define PERF_INFORMATIONAL_PARENT_VIDEO_IN_BANDWIDTH 186
#define PERF_INFORMATIONAL_PARENT_VIDEO_OUT_BANDWIDTH 188
#define PERF_INFORMATIONAL_PARENT_DATA_IN_BANDWIDTH 190
#define PERF_INFORMATIONAL_PARENT_DATA_OUT_BANDWIDTH 192
#define PERF_INFORMATIONAL_CHILD_AUDIO_IN_BANDWIDTH 194
#define PERF_INFORMATIONAL_CHILD_AUDIO_OUT_BANDWIDTH 196
#define PERF_INFORMATIONAL_CHILD_VIDEO_IN_BANDWIDTH 198
#define PERF_INFORMATIONAL_CHILD_VIDEO_OUT_BANDWIDTH 200
#define PERF_INFORMATIONAL_CHILD_DATA_IN_BANDWIDTH 202
#define PERF_INFORMATIONAL_CHILD_DATA_OUT_BANDWIDTH 204
#define PERF_INFORMATIONAL_AUDIO_SIREN_ENCODES 206
#define PERF_INFORMATIONAL_AUDIO_SIREN_DECODES 208
#define PERF_INFORMATIONAL_AUDIO_SILK_ENCODES 210
#define PERF_INFORMATIONAL_AUDIO_SILK_DECODES 212
#define PERF_INFORMATIONAL_AUDIO_SATIN_LR_ENCODES 214
#define PERF_INFORMATIONAL_AUDIO_SATIN_LR_DECODES 216
#define PERF_INFORMATIONAL_AUDIO_SATIN_HR_ENCODES 218
#define PERF_INFORMATIONAL_AUDIO_SATIN_HR_DECODES 220
#define PERF_INFORMATIONAL_AUDIO_OTHERS_ENCODES 222
#define PERF_INFORMATIONAL_AUDIO_OTHERS_DECODES 224
#define PERF_INFORMATIONAL_AUDIO_G722_ENCODES 226
#define PERF_INFORMATIONAL_AUDIO_G722_DECODES 228
#define PERF_INFORMATIONAL_NUM_RTP_PACKETS_DROPPED 230
#define PERF_INFORMATIONAL_VIDEO_TICK_COUNT 232
#define PERF_INFORMATIONAL_SHAPER_FLUSH_COUNT 234
#define PERF_INFORMATIONAL_VIDEO_FRAME_THROUGHPUT 236

#define PERF_PRIVATE 238
#define PERF_PRIVATE_AUDIO_BUFFER_HITS 240
#define PERF_PRIVATE_AUDIOROUTER_TIMESLICE 242
#define PERF_PRIVATE_CONF_PROCESSED 244
#define PERF_PRIVATE_AVERAGE_CONF_PROCESSING_TIME 246
#define PERF_PRIVATE_AVERAGE_CONF_PROCESSING_TIME_BASE 248
#define PERF_PRIVATE_AVERAGE_TRANSPORT_PROCESSING_TIME 250
#define PERF_PRIVATE_AVERAGE_TRANSPORT_PROCESSING_TIME_BASE 252
#define PERF_PRIVATE_NUM_SEND_20MS_ACHANNELS 254
#define PERF_PRIVATE_NUM_SEND_40MS_ACHANNELS 256
#define PERF_PRIVATE_NUM_SEND_60MS_ACHANNELS 258
#define PERF_PRIVATE_NUM_SEND_100MS_ACHANNELS 260
#define PERF_PRIVATE_NUM_SEND_200MS_ACHANNELS 262
#define PERF_PRIVATE_NUM_RECV_20MS_ACHANNELS 264
#define PERF_PRIVATE_NUM_RECV_40MS_ACHANNELS 266
#define PERF_PRIVATE_NUM_RECV_60MS_ACHANNELS 268
#define PERF_PRIVATE_NUM_RECV_100MS_ACHANNELS 270
#define PERF_PRIVATE_NUM_RECV_200MS_ACHANNELS 272
#define PERF_PRIVATE_AVERAGE_SIREN_ENCODE_TIME 274
#define PERF_PRIVATE_AVERAGE_SIREN_ENCODE_TIME_BASE 276
#define PERF_PRIVATE_AVERAGE_SIREN_DECODE_TIME 278
#define PERF_PRIVATE_AVERAGE_SIREN_DECODE_TIME_BASE 280
#define PERF_PRIVATE_AVERAGE_SILK_ENCODE_TIME 282
#define PERF_PRIVATE_AVERAGE_SILK_ENCODE_TIME_BASE 284
#define PERF_PRIVATE_AVERAGE_SILK_DECODE_TIME 286
#define PERF_PRIVATE_AVERAGE_SILK_DECODE_TIME_BASE 288
#define PERF_PRIVATE_AVERAGE_SRTP_ENCRYPT_TIME_LARGE 290
#define PERF_PRIVATE_AVERAGE_SRTP_ENCRYPT_TIME_LARGE_BASE 292
#define PERF_PRIVATE_AVERAGE_SRTP_ENCRYPT_TIME_SMALL 294
#define PERF_PRIVATE_AVERAGE_SRTP_ENCRYPT_TIME_SMALL_BASE 296
#define PERF_PRIVATE_AVERAGE_SRTP_AUTH_INC_TIME_LARGE 298
#define PERF_PRIVATE_AVERAGE_SRTP_AUTH_INC_TIME_LARGE_BASE 300
#define PERF_PRIVATE_AVERAGE_SRTP_AUTH_INC_TIME_SMALL 302
#define PERF_PRIVATE_AVERAGE_SRTP_AUTH_INC_TIME_SMALL_BASE 304
#define PERF_PRIVATE_AVERAGE_SRTP_AUTH_FINAL_TIME_LARGE 306
#define PERF_PRIVATE_AVERAGE_SRTP_AUTH_FINAL_TIME_LARGE_BASE 308
#define PERF_PRIVATE_AVERAGE_SRTP_AUTH_FINAL_TIME_SMALL 310
#define PERF_PRIVATE_AVERAGE_SRTP_AUTH_FINAL_TIME_SMALL_BASE 312
#define PERF_PRIVATE_AVERAGE_SRTP_DECRYPT_TIME_LARGE 314
#define PERF_PRIVATE_AVERAGE_SRTP_DECRYPT_TIME_LARGE_BASE 316
#define PERF_PRIVATE_AVERAGE_SRTP_DECRYPT_TIME_SMALL 318
#define PERF_PRIVATE_AVERAGE_SRTP_DECRYPT_TIME_SMALL_BASE 320
#define PERF_PRIVATE_AVERAGE_SRTP_AUTH_CHECK_TIME_LARGE 322
#define PERF_PRIVATE_AVERAGE_SRTP_AUTH_CHECK_TIME_LARGE_BASE 324
#define PERF_PRIVATE_AVERAGE_SRTP_AUTH_CHECK_TIME_SMALL 326
#define PERF_PRIVATE_AVERAGE_SRTP_AUTH_CHECK_TIME_SMALL_BASE 328
#define PERF_PRIVATE_TRANSPORT_SENDDROP 330
#define PERF_PRIVATE_AVERAGE_SENDTIME 332
#define PERF_PRIVATE_AVERAGE_SENDTIME_BASE 334
#define PERF_PRIVATE_AVERAGE_AUDIO_CROSSBAR_TIME 336
#define PERF_PRIVATE_AVERAGE_AUDIO_CROSSBAR_TIME_BASE 338
#define PERF_PRIVATE_AVERAGE_VIDEO_CROSSBAR_TIME 340
#define PERF_PRIVATE_AVERAGE_VIDEO_CROSSBAR_TIME_BASE 342
#define PERF_PRIVATE_AVERAGE_CHANNEL_CROSSBAR_TIME 344
#define PERF_PRIVATE_AVERAGE_CHANNEL_CROSSBAR_TIME_BASE 346
#define PERF_PRIVATE_COUNTER1 348
#define PERF_PRIVATE_COUNTER2 350
#define PERF_PRIVATE_COUNTER3 352
#define PERF_PRIVATE_COUNTER4 354
#define PERF_PRIVATE_NUM_VALUE1 356
#define PERF_PRIVATE_NUM_VALUE2 358
#define PERF_PRIVATE_NUM_VALUE3 360
#define PERF_PRIVATE_NUM_VALUE4 362
#define PERF_PRIVATE_AVERAGE_VALUE1 364
#define PERF_PRIVATE_AVERAGE_VALUE1_BASE 366
#define PERF_PRIVATE_AVERAGE_VALUE2 368
#define PERF_PRIVATE_AVERAGE_VALUE2_BASE 370
#define PERF_PRIVATE_AVERAGE_VALUE3 372
#define PERF_PRIVATE_AVERAGE_VALUE3_BASE 374
#define PERF_PRIVATE_AVERAGE_VALUE4 376
#define PERF_PRIVATE_AVERAGE_VALUE4_BASE 378
#define PERF_PRIVATE_AUDIO_MIXES 380
#define PERF_PRIVATE_RMA_SEND_BYTES 382
#define PERF_PRIVATE_RMA_RECV_BYTES 384
#define PERF_PRIVATE_RMA_SEND_EVENTS 386
#define PERF_PRIVATE_RMA_RECV_EVENTS 388
#define PERF_PRIVATE_NUM_AUDIO_MIXER 390
#define PERF_PRIVATE_NUM_MEMORYPOOL_STREAM 392
#define PERF_PRIVATE_NUM_MEMORYPOOL_AUDIO_SOURCE 394
#define PERF_PRIVATE_NUM_MEMORYPOOL_AUDIO_ENCODE 396
#define PERF_PRIVATE_NUM_MEMORYPOOL_AUDIO_METADATA 398
#define PERF_PRIVATE_NUM_MEMORYPOOL_RTCP 400
#define PERF_PRIVATE_NUM_MEMORYPOOL_RTPEXTHEADER 402
#define PERF_PRIVATE_NUM_MEMORYPOOL_RTPHEADER 404
#define PERF_PRIVATE_NUM_MEMORYPOOL_TRANSPORTIOCONTEXT 406
#define PERF_PRIVATE_TOTAL_POSTED_CBufferTransportIOContext 408
#define PERF_PRIVATE_CONFERENCE_SCHEDULING_RATE 410
#define PERF_PRIVATE_CONFERENCE_SCHEDULING_RATE_BASE 412
#define PERF_PRIVATE_NUM_CONFERENCE_SCHEDULES 414
#define PERF_PRIVATE_TOTAL_NUM_Core1_Conf 416
#define PERF_PRIVATE_TOTAL_Duration_Core1_Conf 418
#define PERF_PRIVATE_TOTAL_Duration_Core1_Work_Conf 420
#define PERF_PRIVATE_TOTAL_Duration_Core1_Transport 422
#define PERF_PRIVATE_TOTAL_NUM_Core2_Conf 424
#define PERF_PRIVATE_TOTAL_Duration_Core2_Conf 426
#define PERF_PRIVATE_TOTAL_Duration_Core2_Work_Conf 428
#define PERF_PRIVATE_TOTAL_Duration_Core2_Transport 430
#define PERF_PRIVATE_TOTAL_NUM_Core3_Conf 432
#define PERF_PRIVATE_TOTAL_Duration_Core3_Conf 434
#define PERF_PRIVATE_TOTAL_Duration_Core3_Work_Conf 436
#define PERF_PRIVATE_TOTAL_Duration_Core3_Transport 438
#define PERF_PRIVATE_TOTAL_NUM_Core4_Conf 440
#define PERF_PRIVATE_TOTAL_Duration_Core4_Conf 442
#define PERF_PRIVATE_TOTAL_Duration_Core4_Work_Conf 444
#define PERF_PRIVATE_TOTAL_Duration_Core4_Transport 446
#define PERF_PRIVATE_TOTAL_NUM_Core5_Conf 448
#define PERF_PRIVATE_TOTAL_Duration_Core5_Conf 450
#define PERF_PRIVATE_TOTAL_Duration_Core5_Work_Conf 452
#define PERF_PRIVATE_TOTAL_Duration_Core5_Transport 454
#define PERF_PRIVATE_TOTAL_NUM_Core6_Conf 456
#define PERF_PRIVATE_TOTAL_Duration_Core6_Conf 458
#define PERF_PRIVATE_TOTAL_Duration_Core6_Work_Conf 460
#define PERF_PRIVATE_TOTAL_Duration_Core6_Transport 462
#define PERF_PRIVATE_TOTAL_NUM_Core7_Conf 464
#define PERF_PRIVATE_TOTAL_Duration_Core7_Conf 466
#define PERF_PRIVATE_TOTAL_Duration_Core7_Work_Conf 468
#define PERF_PRIVATE_TOTAL_Duration_Core7_Transport 470
#define PERF_PRIVATE_TOTAL_NUM_Core8_Conf 472
#define PERF_PRIVATE_TOTAL_Duration_Core8_Conf 474
#define PERF_PRIVATE_TOTAL_Duration_Core8_Work_Conf 476
#define PERF_PRIVATE_TOTAL_Duration_Core8_Transport 478

#define PERF_TRANSCODING 480
#define PERF_TRANSCODING_MODE 482
#define PERF_TRANSCODING_NUM_INSTANCES_TOTAL 484
#define PERF_TRANSCODING_NUM_INSTANCES_CAPABLE 486
#define PERF_TRANSCODING_NUM_INSTANCES_CAPABLE_MAX 488
#define PERF_TRANSCODING_NUM_INSTANCES_FORCE_PASSTHROUGH 490
#define PERF_TRANSCODING_NUM_INSTANCES_FORCE_TRANSCODE_RESOLUTION 492
#define PERF_TRANSCODING_NUM_INSTANCES_FORCE_TRANSCODE_BITSTREAM 494
#define PERF_TRANSCODING_CURRENT_UTILIZATION 496
#define PERF_TRANSCODING_REQUIRED_UTILIZATION 498
#define PERF_TRANSCODING_REQUIRED_UTILIZATION_THRESHOLD 500
#define PERF_TRANSCODING_DYNAMIC_LOAD 502
#define PERF_TRANSCODING_NUM_STREAMS_SR_VERY_LOW 504
#define PERF_TRANSCODING_NUM_STREAMS_SR_LOW 506
#define PERF_TRANSCODING_NUM_STREAMS_SR_MEDIUM 508
#define PERF_TRANSCODING_NUM_STREAMS_SR_HIGH 510
#define PERF_TRANSCODING_NUM_STREAMS_SR_VERY_HIGH 512
#define PERF_TRANSCODING_NUM_STREAMS_LAYOUT_VERY_LOW 514
#define PERF_TRANSCODING_NUM_STREAMS_LAYOUT_LOW 516
#define PERF_TRANSCODING_NUM_STREAMS_LAYOUT_MEDIUM 518
#define PERF_TRANSCODING_NUM_STREAMS_LAYOUT_HIGH 520
#define PERF_TRANSCODING_NUM_STREAMS_LAYOUT_VERY_HIGH 522
#define PERF_TRANSCODING_NUM_STREAMS_PT_VERY_LOW 524
#define PERF_TRANSCODING_NUM_STREAMS_PT_LOW 526
#define PERF_TRANSCODING_NUM_STREAMS_PT_MEDIUM 528
#define PERF_TRANSCODING_NUM_STREAMS_PT_HIGH 530
#define PERF_TRANSCODING_NUM_STREAMS_PT_VERY_HIGH 532
#define PERF_TRANSCODING_NUM_ENCODERS_VERY_OVERLOADED 534
#define PERF_TRANSCODING_ENCODERS_OVERLOADED 536
#define PERF_TRANSCODING_ENCODERS_UNDERLOADED 538
#define PERF_TRANSCODING_NUM_DECODERS_VERY_OVERLOADED 540
#define PERF_TRANSCODING_DECODERS_OVERLOADED 542
#define PERF_TRANSCODING_DECODERS_UNDERLOADED 544

#define PERF_MISC 546
#define PERF_MISC_NUM_VIDEO_IN_HD1080 548
#define PERF_MISC_NUM_VIDEO_IN_HD1440 550
#define PERF_MISC_NUM_VIDEO_IN_HD2160 552
#define PERF_MISC_NUM_VIDEO_OUT_HD1080 554
#define PERF_MISC_NUM_VIDEO_OUT_HD1440 556
#define PERF_MISC_NUM_VIDEO_OUT_HD2160 558
#define PERF_MISC_TOTAL_NUM_DTLS_HANDSHAKE_SUCCESS 560
#define PERF_MISC_TOTAL_NUM_DTLS_HANDSHAKE_FAILURE 562
#define PERF_MISC_AVERAGE_SENT_BW_ESTIMATE 564
#define PERF_MISC_AVERAGE_SENT_BW_ESTIMATE_BASE 566
#define PERF_MISC_AVERAGE_RECV_BW_ESTIMATE 568
#define PERF_MISC_AVERAGE_RECV_BW_ESTIMATE_BASE 570
#define PERF_MISC_INCOMING_PACKETS_LOST 572
#define PERF_MISC_OUTGOING_PACKETS_LOST 574
#define PERF_MISC_AVERAGE_RTT 576
#define PERF_MISC_AVERAGE_RTT_BASE 578
#define PERF_MISC_VIDEOROUTER_PARALLEL_MODE_STARTED_NUM 580
#define PERF_MISC_VIDEOROUTER_PARALLEL_THRESHOLD 582
#define PERF_MISC_VIDEOROUTER_PARALLEL_TOTAL_NUM_SINKS 584
#define PERF_MISC_VIDEOROUTER_PARALLEL_TOTAL_NUM_PARTICIPANTS 586
#define PERF_MISC_VIDEOROUTER_PARALLEL_SINK_NUM_GROUP1 588
#define PERF_MISC_VIDEOROUTER_PARALLEL_SINK_NUM_GROUP2 590
#define PERF_MISC_VIDEOROUTER_PARALLEL_SINK_NUM_GROUP3 592
#define PERF_MISC_VIDEOROUTER_PARALLEL_SINK_NUM_GROUP4 594
#define PERF_MISC_CONFERENCE_PARALLEL_NUM_CONSECUTIVE_ENGINETICK_SKIPPED 596
#define PERF_MISC_CONFERENCE_PARALLEL_NUM_MAX_CONSECUTIVE_ENGINETICK_SKIPPED 598
#define PERF_MISC_PARALLEL_NUM_SCALE_SRTP_PACKAGE_SHARED 600
#define PERF_MISC_PARALLEL_NUM_SCALE_SRTP_PACKAGE_UNSHARED 602
#define PERF_MISC_PARALLEL_NUM_SRTP_ENCRYPTION_FAILURE 604
#define PERF_MISC_PARALLEL_NUM_SRTP_DECRYPTION_FAILURE 606
#define PERF_MISC_ENGINE_API_PROCESS_RATE 608
#define PERF_MISC_ENGINE_API_CALL_PROCESS_TIME 610
#define PERF_MISC_ENGINE_API_CALL_PROCESS_TIME_BASE 612
#define PERF_MISC_VIDEO_ENGINE_DELAY 614
#define PERF_MISC_VIDEO_ENGINE_DELAY_BASE 616
#define PERF_MISC_VIDEO_ENGINE_TIMEOUT_PULL_NUM 618
#define PERF_MISC_RECEIVEQUEUE_AVG_LENGTH 620
#define PERF_MISC_RECEIVEQUEUE_AVG_LENGTH_BASE 622
#define PERF_MISC_RECEIVEQUEUE_MAX_LENGTH 624
#define PERF_MISC_RECEIVEQUEUE_AVG_PUSH_INTERVAL 626
#define PERF_MISC_RECEIVEQUEUE_MAX_PUSH_INTERVAL 628
#define PERF_MISC_RECEIVEQUEUE_AVG_PULL_INTERVAL 630
#define PERF_MISC_RECEIVEQUEUE_MAX_PULL_INTERVAL 632
#define PERF_MISC_RECEIVEQUEUE_FAILED_PUSH_RATE 634
#define PERF_MISC_RECEIVEQUEUE_FAILED_PULL_RATE 636
#define PERF_MISC_COUNT_10PCT_INCOMING_LOSS 638
#define PERF_MISC_COUNT_5PCT_INCOMING_LOSS 640
#define PERF_MISC_COUNT_1PCT_INCOMING_LOSS 642
#define PERF_MISC_COUNT_0PCT_INCOMING_LOSS 644
#define PERF_MISC_COUNT_UNK_INCOMING_LOSS 646
#define PERF_MISC_COUNT_10PCT_OUTGOING_LOSS 648
#define PERF_MISC_COUNT_5PCT_OUTGOING_LOSS 650
#define PERF_MISC_COUNT_1PCT_OUTGOING_LOSS 652
#define PERF_MISC_COUNT_0PCT_OUTGOING_LOSS 654
#define PERF_MISC_COUNT_UNK_OUTGOING_LOSS 656
#define PERF_MISC_COUNT_300MS_AVG_RTT 658
#define PERF_MISC_COUNT_100MS_AVG_RTT 660
#define PERF_MISC_COUNT_0MS_AVG_RTT 662
#define PERF_MISC_COUNT_UNK_AVG_RTT 664
#define PERF_MISC_COUNT_1500KBPS_VIDEO_IN_BANDWIDTH 666
#define PERF_MISC_COUNT_600KBPS_VIDEO_IN_BANDWIDTH 668
#define PERF_MISC_COUNT_0KBPS_VIDEO_IN_BANDWIDTH 670
#define PERF_MISC_COUNT_UNK_VIDEO_IN_BANDWIDTH 672
#define PERF_MISC_COUNT_1500KBPS_VIDEO_OUT_BANDWIDTH 674
#define PERF_MISC_COUNT_600KBPS_VIDEO_OUT_BANDWIDTH 676
#define PERF_MISC_COUNT_0KBPS_VIDEO_OUT_BANDWIDTH 678
#define PERF_MISC_COUNT_UNK_VIDEO_OUT_BANDWIDTH 680
#define PERF_MISC_COUNT_1500KBPS_VBSS_IN_BANDWIDTH 682
#define PERF_MISC_COUNT_600KBPS_VBSS_IN_BANDWIDTH 684
#define PERF_MISC_COUNT_0KBPS_VBSS_IN_BANDWIDTH 686
#define PERF_MISC_COUNT_UNK_VBSS_IN_BANDWIDTH 688
#define PERF_MISC_COUNT_1500KBPS_VBSS_OUT_BANDWIDTH 690
#define PERF_MISC_COUNT_600KBPS_VBSS_OUT_BANDWIDTH 692
#define PERF_MISC_COUNT_0KBPS_VBSS_OUT_BANDWIDTH 694
#define PERF_MISC_COUNT_UNK_VBSS_OUT_BANDWIDTH 696

