{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning", "Microsoft.Graph": "Information", "AccureMD.MediaBot": "Information"}}, "AllowedHosts": "*", "BotConfiguration": {"BotName": "AccureMD MediaBot", "AadAppId": "24a397f4-16dd-4dae-8b8f-5368c3a81fed", "AadAppSecret": "****************************************", "ServiceCname": "accuremd.eastus.cloudapp.azure.com", "MediaServiceFQDN": "accuremd.eastus.cloudapp.azure.com", "ServiceDnsName": "accuremd.eastus.cloudapp.azure.com", "CertificateThumbprint": "", "InstancePublicPort": 443, "CallSignalingPort": 9441, "InstanceInternalPort": 8445, "PlaceCallEndpointUrl": "https://graph.microsoft.com/v1.0", "PsiStoreDirectory": "C:\\MediaBot\\Stores"}, "Kestrel": {"Endpoints": {"Http": {"Url": "http://0.0.0.0:8445"}, "Https": {"Url": "https://0.0.0.0:9441"}}}}