using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Options;
using Microsoft.Graph.Communications.Common.Telemetry;
using AccureMD.MediaBot.Worker.Bot;
using AccureMD.MediaBot.Worker.Models;

namespace AccureMD.MediaBot.Worker
{
    /// <summary>
    /// Startup configuration for the MediaBot worker.
    /// </summary>
    public class Startup
    {
        /// <summary>
        /// Initializes a new instance of the <see cref="Startup"/> class.
        /// </summary>
        /// <param name="configuration">The configuration.</param>
        public Startup(IConfiguration configuration)
        {
            Configuration = configuration;
        }

        /// <summary>
        /// Gets the configuration.
        /// </summary>
        public IConfiguration Configuration { get; }

        /// <summary>
        /// This method gets called by the runtime. Use this method to add services to the container.
        /// </summary>
        /// <param name="services">The service collection.</param>
        public void ConfigureServices(IServiceCollection services)
        {
            services.AddMvc().SetCompatibilityVersion(CompatibilityVersion.Version_2_1);

            services.AddSingleton<IGraphLogger>(_ => new Microsoft.Graph.Communications.Common.Telemetry.GraphLogger("AccureMD.MediaBot", redirectToTrace: true));

            // Configure bot configuration
            services.Configure<BotConfiguration>(Configuration.GetSection(nameof(BotConfiguration)));
            services.PostConfigure<BotConfiguration>(config => config.Initialize());

            // Add bot service
            services.AddSingleton<IBotService, BotService>(provider =>
            {
                var logger = provider.GetRequiredService<IGraphLogger>();
                var botConfig = provider.GetRequiredService<IOptions<BotConfiguration>>().Value;

                var bot = new BotService(logger, botConfig);
                bot.Initialize();
                return bot;
            });
        }

        /// <summary>
        /// This method gets called by the runtime. Use this method to configure the HTTP request pipeline.
        /// </summary>
        /// <param name="app">The application builder.</param>
        /// <param name="env">The web host environment.</param>
        public void Configure(IApplicationBuilder app, IHostingEnvironment env)
        {
            if (env.IsDevelopment())
            {
                app.UseDeveloperExceptionPage();
            }

            app.UseMvc();
        }
    }
}
