# AccureMD MediaBot Deployment Script for Windows VM
# Run this script as Administrator

param(
    [Parameter(Mandatory=$true)]
    [string]$CertificateThumbprint,
    
    [Parameter(Mandatory=$false)]
    [string]$InstallPath = "C:\MediaBot",
    
    [Parameter(Mandatory=$false)]
    [string]$ServiceName = "AccureMD MediaBot",
    
    [Parameter(Mandatory=$false)]
    [string]$SourcePath = ".\publish"
)

# Check if running as Administrator
if (-NOT ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")) {
    Write-Error "This script must be run as Administrator. Exiting..."
    exit 1
}

Write-Host "Starting AccureMD MediaBot deployment..." -ForegroundColor Green

# Step 1: Create installation directory
Write-Host "Creating installation directory: $InstallPath" -ForegroundColor Yellow
if (!(Test-Path $InstallPath)) {
    New-Item -ItemType Directory -Path $InstallPath -Force
}

# Create Psi store directory
$PsiStoreDir = Join-Path $InstallPath "Stores"
if (!(Test-Path $PsiStoreDir)) {
    New-Item -ItemType Directory -Path $PsiStoreDir -Force
}

# Step 2: Stop existing service if running
Write-Host "Checking for existing service..." -ForegroundColor Yellow
$existingService = Get-Service -Name $ServiceName -ErrorAction SilentlyContinue
if ($existingService) {
    Write-Host "Stopping existing service..." -ForegroundColor Yellow
    Stop-Service -Name $ServiceName -Force
    
    Write-Host "Removing existing service..." -ForegroundColor Yellow
    sc.exe delete $ServiceName
    Start-Sleep -Seconds 5
}

# Step 3: Copy application files
Write-Host "Copying application files from $SourcePath to $InstallPath..." -ForegroundColor Yellow
if (!(Test-Path $SourcePath)) {
    Write-Error "Source path $SourcePath does not exist. Please build the application first."
    exit 1
}

Copy-Item -Path "$SourcePath\*" -Destination $InstallPath -Recurse -Force

# Step 4: Update configuration file
Write-Host "Updating configuration file..." -ForegroundColor Yellow
$configPath = Join-Path $InstallPath "appsettings.json"
$configContent = Get-Content $configPath -Raw | ConvertFrom-Json

# Update certificate thumbprint
$configContent.BotConfiguration.CertificateThumbprint = $CertificateThumbprint

# Update Psi store directory
$configContent.BotConfiguration.PsiStoreDirectory = $PsiStoreDir.Replace('\', '\\')

# Save updated configuration
$configContent | ConvertTo-Json -Depth 10 | Set-Content $configPath

Write-Host "Configuration updated with certificate thumbprint: $CertificateThumbprint" -ForegroundColor Green

# Step 5: Configure Windows Firewall
Write-Host "Configuring Windows Firewall..." -ForegroundColor Yellow

# Remove existing rules if they exist
Remove-NetFirewallRule -DisplayName "MediaBot HTTPS" -ErrorAction SilentlyContinue
Remove-NetFirewallRule -DisplayName "MediaBot HTTP" -ErrorAction SilentlyContinue

# Add new firewall rules
New-NetFirewallRule -DisplayName "MediaBot HTTPS" -Direction Inbound -Protocol TCP -LocalPort 9441 -Action Allow
New-NetFirewallRule -DisplayName "MediaBot HTTP" -Direction Inbound -Protocol TCP -LocalPort 8445 -Action Allow

Write-Host "Firewall rules configured for ports 9441 and 8445" -ForegroundColor Green

# Step 6: Install and configure Windows Service
Write-Host "Installing Windows Service..." -ForegroundColor Yellow
$exePath = Join-Path $InstallPath "AccureMD.MediaBot.Worker.exe"

if (!(Test-Path $exePath)) {
    Write-Error "Executable not found at $exePath"
    exit 1
}

# Create the service
sc.exe create $ServiceName binPath= $exePath start= auto
sc.exe description $ServiceName "AccureMD Teams MediaBot for audio/video processing and transcription"

# Configure service recovery options
sc.exe failure $ServiceName reset= 86400 actions= restart/5000/restart/5000/restart/5000

Write-Host "Windows Service '$ServiceName' installed successfully" -ForegroundColor Green

# Step 7: Set appropriate permissions
Write-Host "Setting directory permissions..." -ForegroundColor Yellow
$acl = Get-Acl $InstallPath
$accessRule = New-Object System.Security.AccessControl.FileSystemAccessRule("NETWORK SERVICE", "FullControl", "ContainerInherit,ObjectInherit", "None", "Allow")
$acl.SetAccessRule($accessRule)
Set-Acl $InstallPath $acl

# Step 8: Start the service
Write-Host "Starting MediaBot service..." -ForegroundColor Yellow
Start-Service -Name $ServiceName

# Wait a moment for service to start
Start-Sleep -Seconds 10

# Step 9: Verify service status
$service = Get-Service -Name $ServiceName
if ($service.Status -eq "Running") {
    Write-Host "MediaBot service started successfully!" -ForegroundColor Green
} else {
    Write-Warning "MediaBot service failed to start. Status: $($service.Status)"
    Write-Host "Check Windows Event Viewer for error details." -ForegroundColor Yellow
}

# Step 10: Test endpoints
Write-Host "Testing MediaBot endpoints..." -ForegroundColor Yellow

try {
    $healthResponse = Invoke-WebRequest -Uri "https://localhost:9441/api/calling/health" -UseBasicParsing -TimeoutSec 30
    if ($healthResponse.StatusCode -eq 200) {
        Write-Host "Health endpoint responding successfully!" -ForegroundColor Green
    }
} catch {
    Write-Warning "Health endpoint test failed: $($_.Exception.Message)"
    Write-Host "This may be normal if SSL certificate is not properly configured." -ForegroundColor Yellow
}

# Step 11: Display summary
Write-Host "`n=== Deployment Summary ===" -ForegroundColor Cyan
Write-Host "Installation Path: $InstallPath" -ForegroundColor White
Write-Host "Service Name: $ServiceName" -ForegroundColor White
Write-Host "Service Status: $($service.Status)" -ForegroundColor White
Write-Host "Certificate Thumbprint: $CertificateThumbprint" -ForegroundColor White
Write-Host "Psi Store Directory: $PsiStoreDir" -ForegroundColor White

Write-Host "`n=== Endpoints ===" -ForegroundColor Cyan
Write-Host "Health Check: https://accuremd.eastus.cloudapp.azure.com:9441/api/calling/health" -ForegroundColor White
Write-Host "Join Meeting: https://accuremd.eastus.cloudapp.azure.com:9441/api/calling/join" -ForegroundColor White
Write-Host "Active Calls: https://accuremd.eastus.cloudapp.azure.com:9441/api/calling/calls" -ForegroundColor White

Write-Host "`n=== Next Steps ===" -ForegroundColor Cyan
Write-Host "1. Verify SSL certificate is properly installed and matches the thumbprint" -ForegroundColor White
Write-Host "2. Test the health endpoint from external network" -ForegroundColor White
Write-Host "3. Update backend configuration to point to this MediaBot instance" -ForegroundColor White
Write-Host "4. Test meeting join functionality" -ForegroundColor White
Write-Host "5. Monitor Windows Event Viewer for any errors" -ForegroundColor White

Write-Host "`nDeployment completed!" -ForegroundColor Green

# Step 12: Create monitoring script
$monitorScript = @"
# MediaBot Monitoring Script
# Run this periodically to check service health

`$serviceName = "$ServiceName"
`$service = Get-Service -Name `$serviceName -ErrorAction SilentlyContinue

if (`$service) {
    Write-Host "Service Status: `$(`$service.Status)" -ForegroundColor $(if (`$service.Status -eq "Running") { "Green" } else { "Red" })
    
    if (`$service.Status -ne "Running") {
        Write-Host "Attempting to start service..." -ForegroundColor Yellow
        Start-Service -Name `$serviceName
    }
} else {
    Write-Host "Service not found!" -ForegroundColor Red
}

# Check disk space in Psi store
`$psiStore = "$PsiStoreDir"
if (Test-Path `$psiStore) {
    `$size = (Get-ChildItem `$psiStore -Recurse | Measure-Object -Property Length -Sum).Sum / 1GB
    Write-Host "Psi Store Size: `$([math]::Round(`$size, 2)) GB" -ForegroundColor White
}

# Test health endpoint
try {
    `$response = Invoke-WebRequest -Uri "https://localhost:9441/api/calling/health" -UseBasicParsing -TimeoutSec 10
    Write-Host "Health Endpoint: OK" -ForegroundColor Green
} catch {
    Write-Host "Health Endpoint: FAILED" -ForegroundColor Red
}
"@

$monitorScriptPath = Join-Path $InstallPath "Monitor-MediaBot.ps1"
$monitorScript | Out-File -FilePath $monitorScriptPath -Encoding UTF8

Write-Host "`nMonitoring script created at: $monitorScriptPath" -ForegroundColor Cyan
Write-Host "Run this script periodically to monitor MediaBot health." -ForegroundColor White
